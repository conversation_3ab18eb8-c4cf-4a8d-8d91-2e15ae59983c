# Nephiris AI React Chat - Script de Démarrage PowerShell
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   🔮 Nephiris AI - Interface React" -ForegroundColor Magenta
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Vérifier si Node.js est installé
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js détecté: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js n'est pas installé !" -ForegroundColor Red
    Write-Host ""
    Write-Host "Veuillez installer Node.js depuis : https://nodejs.org/" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Appuyez sur Entrée pour continuer"
    exit 1
}

Write-Host ""

# Vérifier si les dépendances sont installées
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 Installation des dépendances..." -ForegroundColor Yellow
    Write-Host ""
    
    try {
        npm install
        Write-Host ""
        Write-Host "✅ Dépendances installées avec succès !" -ForegroundColor Green
    } catch {
        Write-Host "❌ Erreur lors de l'installation des dépendances" -ForegroundColor Red
        Read-Host "Appuyez sur Entrée pour continuer"
        exit 1
    }
    Write-Host ""
}

Write-Host "🚀 Démarrage de l'interface Nephiris AI..." -ForegroundColor Green
Write-Host ""
Write-Host "📱 L'application s'ouvrira automatiquement dans votre navigateur" -ForegroundColor Cyan
Write-Host "🌐 URL : http://127.0.0.1:5000" -ForegroundColor Cyan
Write-Host ""
Write-Host "⚡ Fonctionnalités disponibles :" -ForegroundColor Yellow
Write-Host "  - Interface ChatGPT moderne" -ForegroundColor White
Write-Host "  - Reconnaissance vocale 🎤" -ForegroundColor White
Write-Host "  - Thème sombre/clair 🌙" -ForegroundColor White
Write-Host "  - Historique des conversations 💾" -ForegroundColor White
Write-Host "  - Mode focus plein écran 🖥️" -ForegroundColor White
Write-Host "  - Export des conversations 📊" -ForegroundColor White
Write-Host "  - Lecture audio des réponses 🔊" -ForegroundColor White
Write-Host "  - PWA (installable) 📱" -ForegroundColor White
Write-Host ""
Write-Host "🛑 Pour arrêter : Ctrl+C" -ForegroundColor Red
Write-Host ""

# Définir les variables d'environnement
$env:PORT = "5000"
$env:HOST = "127.0.0.1"
$env:BROWSER = "none"

# Démarrer l'application React
try {
    Write-Host "🔄 Démarrage en cours..." -ForegroundColor Yellow
    
    # Attendre un peu puis ouvrir le navigateur
    Start-Job -ScriptBlock {
        Start-Sleep -Seconds 5
        Start-Process "http://127.0.0.1:5000"
    } | Out-Null
    
    # Démarrer npm
    npm start
} catch {
    Write-Host "❌ Erreur lors du démarrage de l'application" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Read-Host "Appuyez sur Entrée pour continuer"
}
