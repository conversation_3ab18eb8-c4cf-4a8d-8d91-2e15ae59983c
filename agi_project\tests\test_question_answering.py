import unittest
from agi_project.modules.nlp.question_answering import QuestionAnswering

class TestQuestionAnswering(unittest.TestCase):
    def setUp(self):
        self.qa = QuestionAnswering()

    def test_answer_question_with_valid_input(self):
        question = "Qui est le président ?"
        context = "<PERSON> est le président de la France."
        result = QuestionAnswering.answer_question(question, context)
        self.assertEqual(result, "<PERSON> est le président de la France.")

    def test_answer_question_with_no_match(self):
        question = "Quelle est la capitale ?" 
        context = "Paris est une belle ville."
        result = QuestionAnswering.answer_question(question, context)
        self.assertIsNone(result)

    def test_answer_question_with_empty_input(self):
        result = QuestionAnswering.answer_question("", "")
        self.assertIsNone(result)

    def test_answer_question_with_partial_match(self):
        question = "Qui est le chef de l'état ?"
        context = "Le président de la République est <PERSON>."
        result = QuestionAnswering.answer_question(question, context)
        self.assertEqual(result, "Le président de la République est <PERSON>.")

if __name__ == '__main__':
    unittest.main()
