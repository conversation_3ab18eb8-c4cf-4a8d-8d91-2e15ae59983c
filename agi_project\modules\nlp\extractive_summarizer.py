"""
Module : extractive_summarizer.py

Ce module réalise un résumé extractif de textes, en sélectionnant les phrases
les plus pertinentes par rapport au thème ou à l'objectif de la synthèse.

Fonctionnalités :
- Analyse de la pertinence des phrases dans un document en se basant 
  sur la fréquence des mots-clés, la position des phrases, ou un score de similarité.
- Possibilité de configurer le nombre maximum de phrases à extraire.
- Support multilingue (placeholder), ou configuration pour un usage monolingue spécifique.
- Gestion d'exceptions ou cas limites (texte trop court, pas assez de contenu).

Exemple d’utilisation :
1. Passer un document texte et le nombre de phrases à extraire.
2. Le module calcule un score de pertinence pour chaque phrase.
3. Il renvoie un résumé sous forme de liste de phrases.

Approche simplifiée :
- Implémentation d’un scoring basique par fréquence de mots.
- Pas d’intégration à un moteur ML ou IA avancé, 
  mais le code est structuré pour accueillir de futures améliorations.
"""

import re
from typing import List, Dict
from collections import Counter

class ExtractiveSummarizer:
    @staticmethod
    def summarize_text(text: str, max_sentences: int = 3) -> List[str]:
        """
        Extrait un certain nombre de phrases clés du document (max_sentences).
        Retourne la liste des phrases sélectionnées.
        """
        if not text.strip():
            return []

        # Séparer en phrases (très simplifié)
        sentences = re.split(r'(?<=[\.!?])\s+', text.strip())
        # Tokeniser mots
        tokens = [word.lower() for word in re.findall(r'\w+', text)]
        freq = Counter(tokens)

        # Calculer un score par phrase (somme des fréquences des mots qu’elle contient)
        sentence_scores: Dict[str, float] = {}
        for sent in sentences:
            words = re.findall(r'\w+', sent.lower())
            score = sum(freq[word] for word in words if word in freq)
            sentence_scores[sent] = score

        # Trier les phrases par ordre décroissant de score
        ranked_sentences = sorted(sentence_scores, key=sentence_scores.get, reverse=True)

        # Retourner les best-sentences
        return ranked_sentences[:max_sentences]
