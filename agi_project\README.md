# Projet AGI - Instructions de démarrage

## Prérequis
- Python 3.8 ou supérieur
- pip installé

## Installation des dépendances
Dans le dossier `agi_project`, exécutez :
```
pip install -r requirements.txt
```

## Lancer le backend FastAPI
Dans le dossier `agi_project`, lancez :
```
uvicorn main:app --host 127.0.0.1 --port 8000
```
Le backend sera accessible sur : http://127.0.0.1:8000

## Lancer le serveur frontend
Dans un autre terminal, dans le dossier `agi_project`, lancez :
```
python serve_frontend.py
```
Le frontend sera accessible sur : http://localhost:5500

## Tester l'application
- Ouvrez http://localhost:5500 dans votre navigateur.
- Utilisez l'interface pour interagir avec l'AGI.
- Les boutons de la barre supérieure et la navigation dans la sidebar affichent des alertes pour l'instant.

## Notes
- Le backend est minimal et doit être étendu pour gérer les requêtes réelles.
- Le frontend est une interface moderne inspirée de Genspark, personnalisée pour ce projet.

N'hésitez pas à me demander pour toute aide supplémentaire.
