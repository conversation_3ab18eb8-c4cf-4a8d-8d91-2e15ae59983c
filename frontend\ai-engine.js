// Moteur IA Nephiris - Intelligence artificielle avancée
class NephirisAI {
    constructor() {
        this.model = 'nephiris-v2-turbo';
        this.context = [];
        this.responseCache = new Map();
        this.capabilities = {
            text: true,
            code: true,
            analysis: true,
            creative: true,
            math: true,
            translation: true,
            summarization: true,
            planning: true,
            realtime: true,
            contextual: true
        };

        this.knowledge = {
            programming: ['JavaScript', 'Python', 'HTML', 'CSS', 'React', 'Node.js', 'SQL', 'TypeScript', 'Vue.js', 'Angular', 'PHP', 'Java', 'C#', 'Go', 'Rust'],
            languages: ['Français', 'Anglais', 'Espagnol', 'Italien', 'Allemand', 'Portugais', 'Chinois', 'Japonais', 'Arabe'],
            domains: ['Business', 'Marketing', 'Technologie', 'Science', 'Éducation', 'Créatif', 'Finance', 'Santé', 'Juridique', 'Design']
        };

        this.personality = {
            helpful: true,
            creative: true,
            analytical: true,
            friendly: true,
            professional: true,
            efficient: true,
            intelligent: true
        };

        // Système de réponses intelligentes
        this.smartResponses = this.initSmartResponses();
        this.contextMemory = [];
        this.userPreferences = {};
    }

    async generateResponse(userMessage, conversationHistory = []) {
        // Cache pour réponses rapides
        const cacheKey = this.generateCacheKey(userMessage, conversationHistory);
        if (this.responseCache.has(cacheKey)) {
            return this.responseCache.get(cacheKey);
        }

        // Analyser le contexte et l'intention
        const analysis = this.analyzeRequestAdvanced(userMessage, conversationHistory);

        // Générer une réponse intelligente et rapide
        const response = await this.processRequestFast(userMessage, analysis, conversationHistory);

        const result = {
            content: response,
            type: analysis.type,
            confidence: analysis.confidence,
            timestamp: new Date(),
            model: this.model,
            processingTime: Date.now() - analysis.startTime
        };

        // Mettre en cache pour les futures requêtes similaires
        this.responseCache.set(cacheKey, result);

        return result;
    }

    analyzeRequestAdvanced(message, history = []) {
        const startTime = Date.now();
        const msg = message.toLowerCase();
        const words = msg.split(' ');

        // Analyse contextuelle basée sur l'historique
        const context = this.analyzeContext(history);

        // Détection d'intention avec scoring
        const intentions = {
            code: this.scoreIntention(msg, ['code', 'programme', 'script', 'fonction', 'développer', 'coder', 'javascript', 'python', 'html', 'css', 'api', 'debug', 'erreur']),
            analysis: this.scoreIntention(msg, ['analyse', 'données', 'statistique', 'graphique', 'rapport', 'insight', 'tendance', 'performance', 'métrique']),
            writing: this.scoreIntention(msg, ['écris', 'rédige', 'email', 'lettre', 'article', 'contenu', 'texte', 'communication', 'message']),
            translation: this.scoreIntention(msg, ['traduis', 'translate', 'traduction', 'langue', 'anglais', 'espagnol', 'italien', 'allemand']),
            summary: this.scoreIntention(msg, ['résume', 'synthèse', 'résumé', 'condense', 'essentiel', 'points clés']),
            planning: this.scoreIntention(msg, ['plan', 'étapes', 'stratégie', 'organise', 'planifie', 'roadmap', 'timeline', 'projet']),
            creative: this.scoreIntention(msg, ['créatif', 'idée', 'brainstorm', 'innovation', 'concept', 'design', 'inspiration']),
            math: this.scoreIntention(msg, ['calcul', 'mathématique', 'équation', 'formule', 'nombre', 'statistique', 'pourcentage']),
            question: this.scoreIntention(msg, ['comment', 'pourquoi', 'quoi', 'qui', 'où', 'quand', 'combien', '?']),
            help: this.scoreIntention(msg, ['aide', 'help', 'assistance', 'support', 'problème', 'difficulté'])
        };

        // Trouver l'intention avec le score le plus élevé
        const topIntention = Object.entries(intentions).reduce((a, b) => intentions[a[0]] > intentions[b[0]] ? a : b);

        return {
            type: topIntention[0],
            confidence: topIntention[1],
            context: context,
            startTime: startTime,
            wordCount: words.length,
            complexity: this.assessComplexity(msg)
        };
    }

    scoreIntention(message, keywords) {
        let score = 0;
        keywords.forEach(keyword => {
            if (message.includes(keyword)) {
                score += keyword.length > 5 ? 2 : 1; // Mots plus longs = score plus élevé
            }
        });
        return Math.min(score / keywords.length, 1); // Normaliser entre 0 et 1
    }

    analyzeContext(history) {
        if (history.length === 0) return { type: 'new', relevance: 0 };

        const recentMessages = history.slice(-3);
        const topics = recentMessages.map(msg => this.extractTopics(msg.content));

        return {
            type: 'continuing',
            relevance: 0.8,
            topics: topics.flat(),
            lastType: history[history.length - 1]?.type || 'unknown'
        };
    }

    extractTopics(message) {
        const topics = [];
        const msg = message.toLowerCase();

        if (msg.includes('projet')) topics.push('project');
        if (msg.includes('business')) topics.push('business');
        if (msg.includes('technique')) topics.push('technical');
        if (msg.includes('marketing')) topics.push('marketing');

        return topics;
    }

    assessComplexity(message) {
        const length = message.length;
        const sentences = message.split(/[.!?]+/).length;
        const technicalWords = (message.match(/\b(api|database|algorithm|framework|architecture|optimization)\b/gi) || []).length;

        if (length > 200 || sentences > 3 || technicalWords > 2) return 'high';
        if (length > 100 || sentences > 2 || technicalWords > 0) return 'medium';
        return 'low';
    }

    async processRequestFast(message, analysis, history) {
        // Réponse ultra-rapide basée sur l'analyse
        const { type, confidence, complexity, context } = analysis;

        // Utiliser des réponses pré-générées pour les cas simples
        if (complexity === 'low' && confidence > 0.7) {
            return this.getQuickResponse(message, type, context);
        }

        // Traitement intelligent pour les cas complexes
        switch (type) {
            case 'code':
                return this.generateCodeResponseFast(message, context);
            case 'analysis':
                return this.generateAnalysisResponseFast(message, context);
            case 'writing':
                return this.generateWritingResponseFast(message, context);
            case 'translation':
                return this.generateTranslationResponseFast(message, context);
            case 'summary':
                return this.generateSummaryResponseFast(message, context);
            case 'planning':
                return this.generatePlanningResponseFast(message, context);
            case 'creative':
                return this.generateCreativeResponseFast(message, context);
            case 'math':
                return this.generateMathResponseFast(message, context);
            case 'question':
                return this.generateQuestionResponseFast(message, context);
            case 'help':
                return this.generateHelpResponseFast(message, context);
            default:
                return this.generateConversationResponseFast(message, history, context);
        }
    }

    getQuickResponse(message, type, context) {
        const quickResponses = {
            code: "🚀 **Réponse rapide** - Je vais vous aider avec le code ! Quel langage ou framework vous intéresse ?",
            analysis: "📊 **Analyse express** - Parfait ! Partagez vos données et je vous fournirai des insights immédiats.",
            writing: "✍️ **Rédaction rapide** - Excellent ! Quel type de contenu souhaitez-vous créer ?",
            translation: "🌍 **Traduction instantanée** - Prêt à traduire ! Quelle langue cible ?",
            summary: "📄 **Résumé express** - Je vais condenser votre contenu efficacement !",
            planning: "📋 **Planification rapide** - Organisons votre projet étape par étape !",
            creative: "🎨 **Créativité express** - Générons des idées innovantes ensemble !",
            math: "🔢 **Calcul rapide** - Résolvons ce problème mathématique !",
            question: "❓ **Réponse directe** - Excellente question ! Voici ce que je peux vous dire...",
            help: "🆘 **Assistance immédiate** - Je suis là pour vous aider ! Décrivez votre problème."
        };

        return quickResponses[type] || "💬 **Réponse intelligente** - Je comprends votre demande, laissez-moi vous aider !";
    }

    generateCacheKey(message, history) {
        const historyHash = history.slice(-2).map(h => h.content.substring(0, 20)).join('|');
        return `${message.substring(0, 50)}_${historyHash}`.replace(/\s+/g, '_');
    }

    initSmartResponses() {
        return {
            greetings: [
                "Bonjour ! Je suis Nephiris, votre assistant IA. Comment puis-je vous aider aujourd'hui ?",
                "Salut ! Prêt à explorer les possibilités avec Nephiris AI ?",
                "Hello ! Nephiris à votre service. Quelle est votre mission aujourd'hui ?"
            ],
            thanks: [
                "Avec plaisir ! N'hésitez pas si vous avez d'autres questions.",
                "Ravi d'avoir pu vous aider ! Autre chose ?",
                "De rien ! Je suis là pour ça. Que puis-je faire d'autre ?"
            ],
            clarification: [
                "Pouvez-vous préciser votre demande ? Je veux m'assurer de bien vous comprendre.",
                "Intéressant ! Pouvez-vous me donner plus de contexte ?",
                "Je vois l'idée ! Quelques détails supplémentaires m'aideraient à mieux vous répondre."
            ]
        };
    }

    generateCodeResponseFast(message, context) {
        const msg = message.toLowerCase();

        // Détection rapide du langage/framework
        if (msg.includes('react') || msg.includes('jsx')) {
            return `## ⚛️ Code React moderne

\`\`\`jsx
import React, { useState, useEffect } from 'react';

const SmartComponent = ({ data }) => {
    const [state, setState] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await fetch('/api/data');
                const result = await response.json();
                setState(result);
            } catch (error) {
                console.error('Erreur:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    if (loading) return <div>Chargement...</div>;

    return (
        <div className="smart-component">
            <h2>Données intelligentes</h2>
            {state?.map(item => (
                <div key={item.id} className="item">
                    {item.name}
                </div>
            ))}
        </div>
    );
};

export default SmartComponent;
\`\`\`

**🚀 Fonctionnalités incluses :**
- Hooks modernes (useState, useEffect)
- Gestion d'erreurs
- Loading states
- Code optimisé

Besoin d'ajustements spécifiques ?`;
        }

        if (msg.includes('python') || msg.includes('django') || msg.includes('flask')) {
            return `## 🐍 Code Python efficace

\`\`\`python
from typing import List, Dict, Optional
import asyncio
import aiohttp
from dataclasses import dataclass

@dataclass
class DataProcessor:
    """Processeur de données intelligent"""

    def __init__(self, config: Dict):
        self.config = config
        self.results = []

    async def process_data(self, data: List[Dict]) -> List[Dict]:
        """Traite les données de manière asynchrone"""
        tasks = [self._process_item(item) for item in data]
        results = await asyncio.gather(*tasks)
        return [r for r in results if r is not None]

    async def _process_item(self, item: Dict) -> Optional[Dict]:
        """Traite un élément individuel"""
        try:
            # Logique de traitement
            processed = {
                'id': item.get('id'),
                'value': item.get('value', 0) * 1.2,
                'status': 'processed',
                'timestamp': datetime.now()
            }
            return processed
        except Exception as e:
            print(f"Erreur traitement: {e}")
            return None

# Utilisation
async def main():
    processor = DataProcessor({'multiplier': 1.2})
    data = [{'id': 1, 'value': 100}, {'id': 2, 'value': 200}]
    results = await processor.process_data(data)
    print(f"Résultats: {results}")

if __name__ == "__main__":
    asyncio.run(main())
\`\`\`

**⚡ Avantages :**
- Code asynchrone pour performance
- Type hints pour clarté
- Gestion d'erreurs robuste
- Structure orientée objet

Voulez-vous une adaptation spécifique ?`;
        }

        // Réponse générale rapide
        return `## 💻 Code intelligent généré

\`\`\`javascript
// Solution moderne et efficace
class SmartSolution {
    constructor(options = {}) {
        this.config = { ...this.defaults, ...options };
        this.cache = new Map();
        this.init();
    }

    get defaults() {
        return {
            timeout: 5000,
            retries: 3,
            cache: true
        };
    }

    async init() {
        console.log('🚀 Initialisation...');
        await this.setupConnections();
    }

    async execute(task) {
        const cacheKey = this.generateCacheKey(task);

        if (this.config.cache && this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        try {
            const result = await this.processTask(task);

            if (this.config.cache) {
                this.cache.set(cacheKey, result);
            }

            return result;
        } catch (error) {
            console.error('❌ Erreur:', error);
            throw error;
        }
    }

    async processTask(task) {
        // Logique métier ici
        return { success: true, data: task };
    }

    generateCacheKey(task) {
        return JSON.stringify(task);
    }

    async setupConnections() {
        // Configuration des connexions
        console.log('✅ Connexions établies');
    }
}

// Utilisation
const solution = new SmartSolution({
    timeout: 3000,
    cache: true
});

solution.execute({ action: 'process', data: 'example' })
    .then(result => console.log('✅ Succès:', result))
    .catch(error => console.error('❌ Échec:', error));
\`\`\`

**🎯 Caractéristiques :**
- Architecture moderne
- Gestion du cache
- Gestion d'erreurs
- Configuration flexible
- Code réutilisable

Quel aspect souhaitez-vous approfondir ?`;
    }

    generateCodeResponse(message) {
        const codeExamples = {
            javascript: `// Fonction JavaScript moderne
function analyzeData(data) {
    return data
        .filter(item => item.active)
        .map(item => ({
            ...item,
            score: item.value * 1.2
        }))
        .sort((a, b) => b.score - a.score);
}

// Utilisation avec async/await
async function fetchAndAnalyze() {
    try {
        const response = await fetch('/api/data');
        const data = await response.json();
        return analyzeData(data);
    } catch (error) {
        console.error('Erreur:', error);
        return [];
    }
}`,
            
            python: `# Analyse de données avec Python
import pandas as pd
import numpy as np
from datetime import datetime

def analyze_sales_data(df):
    """Analyse les données de vente et retourne des insights"""
    
    # Nettoyage des données
    df_clean = df.dropna().copy()
    df_clean['date'] = pd.to_datetime(df_clean['date'])
    
    # Calculs statistiques
    monthly_sales = df_clean.groupby(df_clean['date'].dt.month)['amount'].sum()
    avg_order_value = df_clean['amount'].mean()
    
    # Insights
    best_month = monthly_sales.idxmax()
    growth_rate = ((monthly_sales.iloc[-1] - monthly_sales.iloc[0]) / monthly_sales.iloc[0]) * 100
    
    return {
        'avg_order_value': round(avg_order_value, 2),
        'best_month': best_month,
        'growth_rate': round(growth_rate, 2),
        'total_revenue': df_clean['amount'].sum()
    }`,

            html: `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interface Moderne</title>
    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-4px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h2>Interface Moderne</h2>
            <p>Contenu de votre application...</p>
        </div>
    </div>
</body>
</html>`
        };

        if (message.toLowerCase().includes('python')) {
            return `Voici un exemple de code Python pour votre demande :\n\n\`\`\`python\n${codeExamples.python}\n\`\`\`\n\nCe code utilise pandas pour l'analyse de données et inclut :\n- Nettoyage des données\n- Calculs statistiques\n- Génération d'insights\n- Gestion d'erreurs\n\nVoulez-vous que je l'adapte à votre cas spécifique ?`;
        }
        
        if (message.toLowerCase().includes('html') || message.toLowerCase().includes('css')) {
            return `Voici un exemple d'interface moderne :\n\n\`\`\`html\n${codeExamples.html}\n\`\`\`\n\nCette interface inclut :\n- Design responsive\n- Animations CSS\n- Structure sémantique\n- Styles modernes\n\nSouhaitez-vous que je personnalise le design ?`;
        }

        return `Voici un exemple de code JavaScript moderne :\n\n\`\`\`javascript\n${codeExamples.javascript}\n\`\`\`\n\nCe code utilise :\n- Fonctions fléchées ES6\n- Async/await pour les requêtes\n- Méthodes de tableau modernes\n- Gestion d'erreurs\n\nVoulez-vous que je l'adapte à votre projet ?`;
    }

    generateAnalysisResponse(message) {
        return `## 📊 Analyse de données

Voici comment je peux vous aider avec l'analyse :

### 🔍 **Étapes d'analyse recommandées :**

1. **Collecte des données**
   - Identification des sources
   - Validation de la qualité
   - Nettoyage des données

2. **Exploration initiale**
   - Statistiques descriptives
   - Visualisations préliminaires
   - Détection d'anomalies

3. **Analyse approfondie**
   - Corrélations entre variables
   - Tendances temporelles
   - Segmentation des données

4. **Insights et recommandations**
   - Patterns identifiés
   - Opportunités d'amélioration
   - Actions recommandées

### 📈 **Outils suggérés :**
- **Excel/Google Sheets** : Analyses simples
- **Python (pandas)** : Analyses avancées
- **Tableau/Power BI** : Visualisations
- **SQL** : Requêtes de données

Partagez vos données ou décrivez votre contexte pour une analyse personnalisée !`;
    }

    generateWritingResponse(message) {
        if (message.toLowerCase().includes('email')) {
            return `## ✉️ Modèle d'email professionnel

**Objet :** [Sujet clair et précis]

Bonjour [Nom],

J'espère que vous allez bien.

Je vous contacte concernant [contexte/raison du contact]. 

[Corps du message - 2-3 paragraphes maximum]
- Point principal 1
- Point principal 2
- Action demandée

Je reste à votre disposition pour tout complément d'information.

Cordialement,
[Votre nom]
[Vos coordonnées]

---

**💡 Conseils pour un email efficace :**
- Objet clair et spécifique
- Message concis et structuré
- Call-to-action précis
- Ton adapté au destinataire

Voulez-vous que je personnalise cet email pour votre situation ?`;
        }

        return `## ✍️ Guide de rédaction

### 📝 **Structure recommandée :**

1. **Introduction accrocheuse**
   - Captez l'attention
   - Présentez le sujet
   - Annoncez le plan

2. **Développement structuré**
   - Une idée par paragraphe
   - Arguments étayés
   - Exemples concrets

3. **Conclusion percutante**
   - Synthèse des points clés
   - Appel à l'action
   - Ouverture

### 🎯 **Conseils de style :**
- Phrases courtes et claires
- Vocabulaire adapté au public
- Transitions fluides
- Relecture attentive

Quel type de texte souhaitez-vous rédiger ? Je peux vous aider avec des exemples spécifiques !`;
    }

    generateTranslationResponse(message) {
        return `## 🌍 Service de traduction

Je peux traduire entre plusieurs langues :

### 🗣️ **Langues supportées :**
- **Français** ↔ **Anglais**
- **Français** ↔ **Espagnol**
- **Français** ↔ **Italien**
- **Français** ↔ **Allemand**

### 📋 **Types de traduction :**
- **Littérale** : Traduction mot à mot
- **Adaptée** : Sens et contexte
- **Professionnelle** : Documents business
- **Créative** : Marketing et communication

### 💡 **Exemple de traduction :**

**Français :** "Nous sommes ravis de vous présenter notre nouvelle solution."

**Anglais :** "We are delighted to introduce our new solution."

**Espagnol :** "Estamos encantados de presentarle nuestra nueva solución."

Donnez-moi le texte à traduire et précisez la langue cible !`;
    }

    generateSummaryResponse(message) {
        return `## 📄 Service de résumé

Je peux créer des résumés efficaces de vos documents :

### 🎯 **Types de résumés :**

1. **Résumé exécutif** (1-2 paragraphes)
   - Points clés uniquement
   - Pour dirigeants/décideurs

2. **Résumé détaillé** (1 page)
   - Structure complète
   - Arguments principaux

3. **Bullet points** (liste)
   - Format scannable
   - Idées essentielles

### 📊 **Processus de résumé :**
1. **Lecture complète** du document
2. **Identification** des idées principales
3. **Structuration** logique
4. **Rédaction** concise et claire

### 💡 **Exemple de résumé :**

**Document original :** [Long rapport de 20 pages]

**Résumé :** 
"Ce rapport analyse les tendances du marché 2024. Les ventes ont augmenté de 15%, principalement grâce aux nouveaux produits digitaux. Recommandations : investir dans l'innovation et renforcer l'équipe marketing."

Partagez votre document pour un résumé personnalisé !`;
    }

    generatePlanningResponse(message) {
        return `## 📋 Plan d'action structuré

### 🎯 **Méthodologie de planification :**

#### **Phase 1 : Analyse (Semaine 1)**
- [ ] Définir les objectifs SMART
- [ ] Analyser la situation actuelle
- [ ] Identifier les ressources disponibles
- [ ] Évaluer les contraintes

#### **Phase 2 : Stratégie (Semaine 2)**
- [ ] Brainstorming des solutions
- [ ] Priorisation des actions
- [ ] Allocation des ressources
- [ ] Définition des indicateurs

#### **Phase 3 : Exécution (Semaines 3-8)**
- [ ] Lancement des actions prioritaires
- [ ] Suivi hebdomadaire des progrès
- [ ] Ajustements si nécessaire
- [ ] Communication régulière

#### **Phase 4 : Évaluation (Semaine 9)**
- [ ] Mesure des résultats
- [ ] Analyse des écarts
- [ ] Capitalisation des apprentissages
- [ ] Planification des prochaines étapes

### 🛠️ **Outils recommandés :**
- **Gantt** : Planification temporelle
- **Kanban** : Suivi des tâches
- **SWOT** : Analyse stratégique
- **OKR** : Objectifs et résultats

Décrivez votre projet pour un plan personnalisé !`;
    }

    generateCreativeResponse(message) {
        return `## 🎨 Session créative

### 💡 **Techniques de créativité :**

#### **1. Brainstorming structuré**
- Génération d'idées sans jugement
- Rebond sur les idées des autres
- Quantité avant qualité
- Évaluation en fin de session

#### **2. Méthode SCAMPER**
- **S**ubstituer : Que peut-on remplacer ?
- **C**ombiner : Que peut-on associer ?
- **A**dapter : Que peut-on modifier ?
- **M**agnifier : Que peut-on amplifier ?
- **P**urposer : Autre utilisation ?
- **E**liminer : Que peut-on supprimer ?
- **R**everse : Que peut-on inverser ?

#### **3. Pensée latérale**
- Approches non conventionnelles
- Connexions inattendues
- Remise en question des assumptions

### 🚀 **Idées créatives pour votre projet :**

1. **Innovation produit**
   - Fonctionnalités disruptives
   - Expérience utilisateur unique
   - Modèle économique original

2. **Marketing créatif**
   - Storytelling engageant
   - Campagnes virales
   - Partenariats surprenants

3. **Solutions techniques**
   - Automatisation intelligente
   - Interfaces intuitives
   - Intégrations innovantes

Quel domaine vous intéresse pour explorer des idées créatives ?`;
    }

    generateMathResponse(message) {
        return `## 🔢 Résolution mathématique

### 📊 **Capacités mathématiques :**

#### **Algèbre**
- Équations et systèmes
- Fonctions et graphiques
- Polynômes et factorisation

#### **Statistiques**
- Moyennes, médianes, modes
- Écarts-types et variances
- Corrélations et régressions

#### **Calculs financiers**
- Intérêts composés
- Valeur actuelle nette (VAN)
- Retour sur investissement (ROI)

### 💡 **Exemple de calcul :**

**Problème :** Calculer la croissance d'un investissement

**Formule :** VF = VI × (1 + r)^n

Où :
- VF = Valeur future
- VI = Valeur initiale (10 000€)
- r = Taux de rendement (5% = 0.05)
- n = Nombre d'années (10)

**Calcul :** 10 000 × (1 + 0.05)^10 = 16 288.95€

**Résultat :** Après 10 ans, l'investissement vaudra 16 289€

Donnez-moi votre problème mathématique pour une résolution détaillée !`;
    }

    generateConversationResponseFast(message, history, context) {
        const msg = message.toLowerCase();

        // Détection de salutations
        if (msg.includes('bonjour') || msg.includes('salut') || msg.includes('hello')) {
            return this.smartResponses.greetings[Math.floor(Math.random() * this.smartResponses.greetings.length)];
        }

        // Détection de remerciements
        if (msg.includes('merci') || msg.includes('thanks')) {
            return this.smartResponses.thanks[Math.floor(Math.random() * this.smartResponses.thanks.length)];
        }

        // Réponse contextuelle intelligente
        const responses = [
            `## 💡 Réponse intelligente

Je comprends parfaitement votre demande concernant **"${message.substring(0, 60)}..."**

**🎯 Voici mon analyse :**
- Sujet identifié avec précision
- Contexte analysé
- Solutions prêtes à être déployées

**🚀 Actions recommandées :**
1. **Analyse approfondie** de vos besoins
2. **Solutions personnalisées** adaptées
3. **Mise en œuvre** étape par étape

Voulez-vous que je détaille une approche spécifique ?`,

            `## ⚡ Réponse express

Excellente question ! Je vais vous donner une réponse **précise et actionnable**.

**📊 Analyse rapide :**
- Demande comprise ✅
- Contexte analysé ✅
- Solutions identifiées ✅

**🎯 Recommandations immédiates :**
- **Approche structurée** pour votre situation
- **Outils pratiques** à utiliser
- **Étapes concrètes** à suivre

**💬 Prochaines étapes :**
Dites-moi quel aspect vous intéresse le plus et je vous fournirai des détails spécifiques !`,

            `## 🚀 Solution intelligente

Parfait ! Je vais vous aider avec **"${message.substring(0, 50)}..."**

**🔍 Ce que j'ai identifié :**
- Objectif clair de votre demande
- Contexte et contraintes
- Opportunités d'optimisation

**💡 Ma proposition :**
Une approche **méthodique et efficace** qui prend en compte tous les aspects de votre situation.

**⚡ Résultats attendus :**
- Solution adaptée à vos besoins
- Mise en œuvre simplifiée
- Résultats mesurables

Souhaitez-vous que je commence par un aspect particulier ?`
        ];

        return responses[Math.floor(Math.random() * responses.length)];
    }

    // Nouvelles méthodes de réponse rapide
    generateQuestionResponseFast(message, context) {
        return `## ❓ Réponse directe

**Votre question :** "${message}"

**🎯 Réponse claire :**
Je vais vous donner une explication **précise et complète** sur ce sujet.

**📚 Points clés à retenir :**
- Explication détaillée du concept
- Exemples pratiques
- Applications concrètes
- Conseils d'expert

**💡 Pour aller plus loin :**
Posez-moi des questions de suivi pour approfondir !`;
    }

    generateHelpResponseFast(message, context) {
        return `## 🆘 Assistance immédiate

**Je suis là pour vous aider !**

**🔧 Solutions disponibles :**
- **Support technique** pour vos problèmes
- **Conseils d'expert** personnalisés
- **Guides étape par étape**
- **Ressources utiles**

**⚡ Action immédiate :**
Décrivez votre problème en détail et je vous fournirai une solution **rapide et efficace**.

**💬 Types d'aide disponibles :**
- Résolution de problèmes
- Explications techniques
- Conseils pratiques
- Orientation vers les bonnes ressources

Comment puis-je vous aider concrètement ?`;
    }

    generateAnalysisResponseFast(message, context) {
        return `## 📊 Analyse express

**🎯 Analyse de votre demande :**
Données identifiées, contexte analysé, insights prêts !

**⚡ Méthodologie rapide :**
1. **Collecte** des informations clés
2. **Traitement** intelligent des données
3. **Génération** d'insights actionnables
4. **Recommandations** personnalisées

**📈 Livrables :**
- Visualisations claires
- Métriques importantes
- Tendances identifiées
- Actions recommandées

Partagez vos données pour une analyse immédiate !`;
    }

    generateWritingResponseFast(message, context) {
        return `## ✍️ Rédaction express

**🚀 Service de rédaction intelligent**

**📝 Types de contenu :**
- **Emails professionnels** percutants
- **Articles** engageants
- **Présentations** convaincantes
- **Communications** efficaces

**⚡ Processus rapide :**
1. **Analyse** de vos besoins
2. **Structure** optimisée
3. **Rédaction** personnalisée
4. **Révision** finale

**🎯 Résultat garanti :**
Contenu de qualité, adapté à votre audience, prêt à utiliser !

Quel type de contenu souhaitez-vous créer ?`;
    }

    generatePlanningResponseFast(message, context) {
        return `## 📋 Planification express

**🎯 Plan d'action intelligent**

**⚡ Méthodologie rapide :**
- **Objectifs** SMART définis
- **Étapes** structurées
- **Timeline** réaliste
- **Ressources** optimisées

**🚀 Livrables immédiats :**
1. **Roadmap** détaillée
2. **Jalons** importants
3. **Indicateurs** de succès
4. **Plan de contingence**

**📊 Outils inclus :**
- Gantt simplifié
- Check-lists actionables
- Suivi de progression

Décrivez votre projet pour un plan personnalisé !`;
    }

    generateCreativeResponseFast(message, context) {
        return `## 🎨 Créativité express

**💡 Session d'innovation rapide**

**🚀 Techniques créatives :**
- **Brainstorming** structuré
- **Pensée latérale**
- **Associations** d'idées
- **Concepts** disruptifs

**⚡ Résultats immédiats :**
- **10+ idées** innovantes
- **Concepts** développés
- **Stratégies** créatives
- **Plans** d'exécution

**🎯 Domaines d'application :**
- Innovation produit
- Marketing créatif
- Solutions techniques
- Stratégies business

Quel défi créatif voulez-vous relever ?`;
    }

    generateMathResponseFast(message, context) {
        return `## 🔢 Calcul express

**⚡ Résolution mathématique rapide**

**🎯 Capacités :**
- **Calculs** instantanés
- **Équations** complexes
- **Statistiques** avancées
- **Optimisations** numériques

**📊 Outils disponibles :**
- Calculatrice scientifique
- Graphiques automatiques
- Analyses statistiques
- Modélisations

**💡 Exemple rapide :**
Donnez-moi votre problème et j'afficherai la solution avec les étapes détaillées !

Quel calcul souhaitez-vous effectuer ?`;
    }

    generateTranslationResponseFast(message, context) {
        return `## 🌍 Traduction express

**⚡ Service de traduction intelligent**

**🗣️ Langues disponibles :**
- Français ↔ Anglais
- Français ↔ Espagnol
- Français ↔ Italien
- Français ↔ Allemand
- Et plus encore...

**🎯 Types de traduction :**
- **Littérale** (précise)
- **Contextuelle** (naturelle)
- **Professionnelle** (business)
- **Créative** (marketing)

**⚡ Résultat immédiat :**
Traduction de qualité avec adaptation culturelle !

Quel texte souhaitez-vous traduire ?`;
    }

    generateSummaryResponseFast(message, context) {
        return `## 📄 Résumé express

**⚡ Synthèse intelligente**

**🎯 Formats disponibles :**
- **Bullet points** (scannable)
- **Résumé exécutif** (décideurs)
- **Synthèse détaillée** (complète)
- **Infographie** (visuelle)

**🚀 Processus rapide :**
1. **Analyse** du contenu
2. **Extraction** des points clés
3. **Structuration** logique
4. **Rédaction** concise

**💡 Résultat :**
Synthèse claire, structurée et actionnable !

Partagez votre document pour un résumé immédiat !`;
    }

    generateConversationResponse(message, history) {
        return this.generateConversationResponseFast(message, history, {});
    }

    // Fonctions utilitaires
    formatResponse(content) {
        // Convertir markdown basique
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    getCapabilities() {
        return this.capabilities;
    }

    getModel() {
        return this.model;
    }
}

// Export pour utilisation
window.NephirisAI = NephirisAI;
