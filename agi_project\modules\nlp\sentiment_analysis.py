"""
Module : sentiment_analysis.py

Ce module analyse le sentiment d’un texte donné, en classant le texte comme
positif, négatif ou neutre.

Fonctionnalités :
- Analyse basée sur un lexique simple de mots positifs et négatifs.
- Calcul d’un score global de sentiment.
- Retourne la catégorie de sentiment et un score associé.
- Extensible pour intégrer des modèles ML ou API externes.

Exemple d’utilisation :
result = SentimentAnalysis.analyze_sentiment("J'adore ce produit, il est fantastique!")

Approche simplifiée :
- Utilisation d’un dictionnaire de mots positifs et négatifs.
- Score calculé par différence de fréquence.
"""

import re
from typing import Dict

class SentimentAnalysis:
    POSITIVE_WORDS = {"bon", "excellent", "fantastique", "heureux", "aimer", "adorer", "super"}
    NEGATIVE_WORDS = {"mauvais", "terrible", "horrible", "triste", "détester", "nul", "pire"}

    @staticmethod
    def analyze_sentiment(text: str) -> Dict[str, float]:
        """
        Analyse le sentiment du texte et retourne un dictionnaire avec :
        - 'sentiment': 'positif', 'négatif' ou 'neutre'
        - 'score': score numérique entre -1 (négatif) et 1 (positif)
        """
        tokens = re.findall(r'\w+', text.lower())
        pos_count = sum(1 for t in tokens if t in SentimentAnalysis.POSITIVE_WORDS)
        neg_count = sum(1 for t in tokens if t in SentimentAnalysis.NEGATIVE_WORDS)
        total = pos_count + neg_count

        if total == 0:
            return {"sentiment": "neutre", "score": 0.0}

        score = (pos_count - neg_count) / total
        sentiment = "positif" if score > 0 else "négatif" if score < 0 else "neutre"

        return {"sentiment": sentiment, "score": score}
