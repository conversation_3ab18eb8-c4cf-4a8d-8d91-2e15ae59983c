import unittest
from agi_project.modules.nlp.text_classifier import TextClassifier

class TestTextClassifier(unittest.TestCase):
    def setUp(self):
        self.classifier = TextClassifier()

    def test_classify_news_article(self):
        text = "Le président a annoncé de nouvelles mesures économiques aujourd'hui."
        result = self.classifier.classify(text)
        self.assertEqual(result["category"], "politics")
        self.assertGreater(result["confidence"], 0.7)

    def test_classify_sports_text(self):
        text = "L'équipe a remporté le match 3-0 hier soir."
        result = self.classifier.classify(text)
        self.assertEqual(result["category"], "sports")
        self.assertGreater(result["confidence"], 0.7)

    def test_classify_technical_text(self):
        text = "Le nouveau modèle utilise une architecture transformer pour le NLP."
        result = self.classifier.classify(text)
        self.assertEqual(result["category"], "technology")
        self.assertGreater(result["confidence"], 0.7)

    def test_empty_input(self):
        with self.assertRaises(ValueError):
            self.classifier.classify("")

    def test_low_confidence_classification(self):
        text = "Ceci est un texte ambigu sans contexte clair."
        result = self.classifier.classify(text)
        self.assertEqual(result["category"], "unknown")
        self.assertLess(result["confidence"], 0.4)

if __name__ == '__main__':
    unittest.main()
