import pytest
import asyncio
from unittest.mock import AsyncMock, patch

"""
Test d'intégration E2E (end-to-end) impliquant 
plusieurs agents (Creative, Education, Code, Web, etc.)
pour un scénario complet.
"""

from agi_project.agent.multi_agent_system import Task
from agi_project.agent.creative_agent import CreativeAgent
from agi_project.agent.education_agent import EducationAgent
from agi_project.agent.code_agent import CodeAgent
from agi_project.agent.web_agent import WebAgent

@pytest.mark.asyncio
class TestE2EIntegration:

    @pytest.fixture
    def creative_agent(self):
        return CreativeAgent(agent_id="creative_e2e")

    @pytest.fixture
    def edu_agent(self):
        return EducationAgent(agent_id="edu_e2e")

    @pytest.fixture
    def code_agent(self):
        # Suppose qu'on se moque de la partie exécution
        return CodeAgent(agent_id="code_e2e")

    @pytest.fixture
    def web_agent(self):
        return WebAgent(agent_id="web_e2e")

    @patch("agi_project.tools.browser.BrowserTool.fetch_page", new_callable=AsyncMock)
    @patch("agi_project.modules.generation.image_generator.ImageGenerator.generate", new_callable=AsyncMock)
    @patch("agi_project.modules.generation.quiz_generator.QuizGenerator.generate_quiz", new_callable=AsyncMock)
    async def test_scenario_complet(
        self,
        mock_gen_quiz,
        mock_image_gen,
        mock_fetch,
        creative_agent,
        edu_agent,
        code_agent,
        web_agent
    ):
        """
        Scénario E2E simulant:
         1) WebAgent scrap un site concurrent
         2) EducationAgent génère un quiz sur un sujet
         3) CreativeAgent génère une image pour le pitch
         4) CodeAgent exécute un snippet
        """

        # Mock des retours
        mock_fetch.return_value = "<html><head><title>Concurrent Site</title></head><body>Some info</body></html>"
        mock_image_gen.return_value = "image_bytes_data"
        mock_gen_quiz.return_value = [{"question": "Q1", "answer": "A1"}]

        # 1) WebAgent scrap
        web_task = Task(id="web_scrap", description="scrape:https://competitor.example.com:analyse")
        result_web = await web_agent.execute_task(web_task)
        assert result_web["status"] in ["completed", "failed"]

        # 2) EducationAgent génère un quiz
        edu_task = Task(id="quiz_gen", description="quiz:IA Générative:5")
        result_edu = await edu_agent.execute_task(edu_task)
        assert result_edu["status"] == "completed"
        assert len(result_edu["result"]) == 1

        # 3) CreativeAgent génère une image
        img_task = Task(id="img_gen", description="image:IA Branding")
        result_img = await creative_agent.execute_task(img_task)
        assert result_img["status"] == "completed"
        assert "image_bytes_data" in result_img["result"]

        # 4) CodeAgent exécute un snippet
        snippet_task = Task(id="code_run", description="run_code:print('Hello E2E')")
        result_code = await code_agent.execute_task(snippet_task)
        # Selon l'implémentation, on peut s'attendre à un "completed" ou "failed" 
        # si l'exécution est mockée
        assert result_code["status"] in ["completed", "failed"]

        # Vérification rapide que tout ne s'est pas effondré 
        # (pas un test de code complet, juste E2E)
        print("E2E scenario done. Web scrap -> Quiz -> Image -> Code run.")
