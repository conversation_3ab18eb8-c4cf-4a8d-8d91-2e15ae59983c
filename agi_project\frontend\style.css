/* Reset and base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f7f7f8;
    color: #333;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: stretch;
}

.container {
    display: flex;
    width: 100%;
    height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 220px;
    background-color: #1f2937;
    color: white;
    display: flex;
    flex-direction: column;
    padding: 1rem;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 2rem;
    text-align: center;
}

.sidebar nav ul {
    list-style: none;
}

.sidebar nav ul li {
    margin-bottom: 1rem;
}

.sidebar nav ul li a {
    color: #d1d5db;
    text-decoration: none;
    padding: 0.5rem 1rem;
    display: block;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.sidebar nav ul li a.active,
.sidebar nav ul li a:hover {
    background-color: #3b82f6;
    color: white;
}

/* Main content */
.main-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    background-color: white;
}

/* Topbar */
.topbar {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: #f3f4f6;
    border-bottom: 1px solid #e5e7eb;
    justify-content: space-between;
}

.menu-btn {
    font-size: 1.5rem;
    background: none;
    border: none;
    cursor: pointer;
}

.topbar-buttons {
    display: flex;
    gap: 0.5rem;
}

.top-btn {
    background-color: #e5e7eb;
    border: none;
    border-radius: 9999px;
    padding: 0.4rem 1rem;
    cursor: pointer;
    font-size: 0.9rem;
    position: relative;
    transition: background-color 0.3s ease;
}

.top-btn:hover {
    background-color: #d1d5db;
}

.new-badge {
    background-color: #ef4444;
    color: white;
    font-size: 0.7rem;
    padding: 0 0.3rem;
    border-radius: 9999px;
    position: absolute;
    top: -6px;
    right: -10px;
}

/* Chat section */
.chat-section {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    padding: 1rem;
}

.chat-messages {
    flex-grow: 1;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    overflow-y: auto;
    background-color: #f9fafb;
    margin-bottom: 1rem;
    font-size: 0.95rem;
    line-height: 1.4;
}

.chat-input-container {
    display: flex;
    gap: 0.5rem;
}

#chatInput {
    flex-grow: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 9999px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s ease;
}

#chatInput:focus {
    border-color: #3b82f6;
}

.send-btn {
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 9999px;
    padding: 0 1.5rem;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.send-btn:hover {
    background-color: #2563eb;
}

/* Messages */
.message {
    margin-bottom: 0.75rem;
    padding: 0.5rem 1rem;
    border-radius: 12px;
    max-width: 70%;
    word-wrap: break-word;
}

.message.user {
    background-color: #3b82f6;
    color: white;
    align-self: flex-end;
}

.message.agi {
    background-color: #e5e7eb;
    color: #111827;
    align-self: flex-start;
}

/* Attach and voice buttons */
.attach-btn, .voice-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    margin-left: 0.5rem;
    color: #6b7280;
    transition: color 0.3s ease;
}

.attach-btn:hover, .voice-btn:hover {
    color: #3b82f6;
}
