"""
Tests for memory/memory_manager.py
Ensures basic storing and retrieving functionalities for text and vectors.
"""

import pytest
from agi_project.memory.memory_manager import MemoryManager

def test_store_and_retrieve_text():
    manager = MemoryManager()

    key = "test_key"
    text_content = "This is a test"

    manager.store_text(key, text_content)
    retrieved = manager.retrieve_text(key)

    # For now, these methods are placeholders, so we may want to check default behavior
    # once they are implemented. For now, we test that it doesn't crash.
    # Later, we will verify that retrieved == text_content if actual storage is implemented.
    assert isinstance(retrieved, str)

def test_store_and_retrieve_vector():
    manager = MemoryManager()

    key = "vector_key"
    vector = [0.1, 0.2, 0.3]

    manager.store_vector(key, vector)
    retrieved = manager.retrieve_vector(key)

    # Once implemented, we can check for actual equality: assert retrieved == vector
    assert isinstance(retrieved, list)
