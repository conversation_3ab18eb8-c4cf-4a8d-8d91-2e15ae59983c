"""
Module logic_deduction.py

Ce module gère la détection et la correction d'erreurs de raisonnement, 
ainsi que la génération d'hypothèses et la déduction logique, conformément 
à la liste de fonctionnalités avancées pour la future AGI.

Fonctionnalités clés :
- Détection de contradictions et d'erreurs logiques.
- Évaluation et validation d'hypothèses.
- Génération de nouvelles hypothèses basées sur un contexte établi.
- Mécanismes basiques de raisonnement logique (modus ponens, chainage, etc.).

Exemple d'utilisation :
- Le module reçoit un ensemble de déclarations ou hypothèses (facts),
  puis génère une conclusion logique ou détecte une contradiction potentielle.
- Il peut également fournir des pistes d'amélioration ou de résolution 
  lorsque plusieurs hypothèses s'affrontent.
"""

from typing import List, Dict, Union, Any
import json
import uuid


class LogicDeduction:
    """
    Classe principale pour gérer les opérations de raisonnement logique.

    - facts : stocke un ensemble de déclarations considérées comme vraies à un instant t.
    - contradictions : liste des contradictions détectées durant le chainage logique.
    - generated_hypotheses : ensemble d'hypothèses formulées à partir de l'existant.
    """

    def __init__(self):
        self.facts: List[Dict[str, Union[str, float]]] = []
        self.contradictions: List[str] = []
        self.generated_hypotheses: List[Dict[str, Union[str, float]]] = []

    def add_fact(self, statement: str, confidence: float = 1.0) -> Dict[str, Union[str, float]]:
        """
        Ajoute un fait (statement) avec un niveau de confiance.
        Format du statement : "Si X alors Y", ou "X est vrai", etc.
        """
        fact_id = str(uuid.uuid4())
        new_fact = {
            "id": fact_id,
            "statement": statement,
            "confidence": confidence
        }
        self.facts.append(new_fact)
        return new_fact

    def generate_hypothesis(self, base_facts: List[Dict[str, Union[str, float]]]) -> Dict[str, Union[str, float]]:
        """
        Génère une hypothèse à partir d'un ensemble de faits.
        Exemple simple : "Si la majorité des faits pointe vers un résultat, 
        proposer une hypothèse correspondante."
        """
        if not base_facts:
            return {"error": "Liste de faits vide, impossible de générer une hypothèse."}
        
        hypothesis_text = f"Hypothèse basée sur {len(base_facts)} fait(s) : probable corrélation."
        # Génération basique d'hypothèse
        hypothesis_id = str(uuid.uuid4())
        hypothesis = {
            "id": hypothesis_id,
            "hypothesis": hypothesis_text,
            "confidence": 0.7  # Valeur par défaut, calcul simpliste
        }
        self.generated_hypotheses.append(hypothesis)
        return hypothesis

    def check_for_logical_errors(self):
        """
        Parcourt la liste de faits à la recherche de contradictions.
        Implémentation simplifiée : on vérifie si un même statement a un niveau
        de confiance trop différent ou si deux faits clairement opposés coexistent.
        """
        statements_seen = {}
        for fact in self.facts:
            st = fact["statement"].lower().strip()
            if st in statements_seen:
                # Détecter si le même statement a un autre confidence trop divergente
                confidence_diff = abs(statements_seen[st]["confidence"] - fact["confidence"])
                if confidence_diff > 0.8:
                    msg = (f"Contradiction potentielle : '{fact['statement']}' "
                           f"({fact['confidence']}) vs. "
                           f"{statements_seen[st]['confidence']}")
                    self.contradictions.append(msg)
            else:
                statements_seen[st] = fact

    def logic_chain(self) -> List[str]:
        """
        Effectue un chainage simple (modus ponens) :
        Ex: Si A implique B, et A est vrai, alors B est vrai.
        
        On détecte aussi si B est en contradiction avec un fait préexistant.
        """
        derived_conclusions = []
        for fact in self.facts:
            st = fact["statement"].lower()
            if "si " in st and " alors " in st:
                # Parse élémentaire
                part_if = st.split("si")[1].split("alors")[0].strip()
                part_then = st.split("alors")[1].strip()
                # On cherche un fait "part_if" est vrai
                for other_fact in self.facts:
                    if other_fact["statement"].lower().strip() == part_if:
                        # Alors on déduit part_then
                        conclusion = f"Déduit : {part_then}"
                        derived_conclusions.append(conclusion)
        return derived_conclusions

    def summarize_state(self) -> Dict[str, Any]:
        """
        Produit un résumé JSON de l'état actuel des faits, hypothèses et contradictions.
        """
        summary = {
            "facts_count": len(self.facts),
            "contradictions_count": len(self.contradictions),
            "generated_hypotheses_count": len(self.generated_hypotheses),
            "contradictions": self.contradictions,
        }
        return summary

    def export_logic_state(self, filepath: str):
        """
        Exporte l'état logique complet dans un fichier au format JSON
        """
        output = {
            "facts": self.facts,
            "contradictions": self.contradictions,
            "generated_hypotheses": self.generated_hypotheses
        }
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(output, f, ensure_ascii=False, indent=2)
