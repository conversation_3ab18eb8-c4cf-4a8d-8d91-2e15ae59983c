"""
OpenAI Service

Handles communication with the OpenAI API, including error handling,
token usage, retries/fallback, etc.
"""

import os
import httpx
import asyncio

class OpenAIService:
    def __init__(self, model: str = "gpt-4"):
        """
        Initialize with default model or specify another model if needed.
        """
        self.model = model
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.url = "https://api.openai.com/v1/chat/completions"
        self.headers = {
            "Content-Type": "application/json"
        }

    async def query_openai(self, prompt: str, max_tokens: int = 500, temperature: float = 0.7) -> str:
        """
        Perform an asynchronous call to the OpenAI ChatCompletion endpoint,
        returning the model's response.

        :param prompt: The user prompt to be fed into the model.
        :param max_tokens: Maximum tokens to generate.
        :param temperature: Sampling temperature for creative responses.
        :return: The content of the assistant's response.
        """

        if not self.api_key:
            return "Erreur : la clé API OpenAI n'est pas configurée."

        request_payload = {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": max_tokens,
            "temperature": temperature
        }

        # Authorization is set separately to avoid exposing the key in logs if desired
        request_headers = dict(self.headers)
        request_headers["Authorization"] = f"Bearer {self.api_key}"

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(self.url, headers=request_headers, json=request_payload)
                response.raise_for_status()
                data = response.json()
                return data["choices"][0]["message"]["content"]
        except httpx.HTTPError as e:
            return f"Erreur lors de la requête à l'API OpenAI : {str(e)}"
        except KeyError:
            return "Erreur : format de réponse inattendu de l'API OpenAI."
