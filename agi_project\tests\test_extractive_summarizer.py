import unittest
from agi_project.modules.nlp.extractive_summarizer import TextSummarizer

class TestExtractiveSummarizer(unittest.TestCase):
    def setUp(self):
        self.summarizer = TextSummarizer()

    def test_summarize_text(self):
        text = """
        Le machine learning est une branche de l'intelligence artificielle qui se concentre sur le développement 
        d'algorithmes permettant aux ordinateurs d'apprendre à partir de données. Ces algorithmes peuvent être 
        supervisés, non supervisés ou par renforcement. Les applications vont de la reconnaissance vocale à la 
        vision par ordinateur.
        """
        summary = self.summarizer.summarize(text, ratio=0.3)
        self.assertLessEqual(len(summary.split()), len(text.split()) * 0.4)
        self.assertIn("machine learning", summary)
        self.assertIn("algorithmes", summary)

    def test_empty_input(self):
        with self.assertRaises(ValueError):
            self.summarizer.summarize("", ratio=0.3)

    def test_invalid_ratio(self):
        text = "Texte de test normal."
        with self.assertRaises(ValueError):
            self.summarizer.summarize(text, ratio=1.5)
        with self.assertRaises(ValueError):
            self.summarizer.summarize(text, ratio=-0.5)

    def test_multilingual_support(self):
        text = """
        Artificial intelligence is transforming industries worldwide. From healthcare to finance, 
        AI applications are becoming increasingly sophisticated. Natural language processing 
        enables machines to understand human language.
        """
        summary = self.summarizer.summarize(text, ratio=0.3)
        self.assertIn("Artificial intelligence", summary)
        self.assertIn("applications", summary)

    def test_very_short_text(self):
        text = "Ceci est un texte très court."
        summary = self.summarizer.summarize(text, ratio=0.5)
        self.assertEqual(summary, text)

if __name__ == '__main__':
    unittest.main()
