@echo off
echo.
echo ========================================
echo   🔮 Nephiris AI - Interface React
echo ========================================
echo.

REM Vérifier si Node.js est installé
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js n'est pas installé !
    echo.
    echo Veuillez installer Node.js depuis : https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js détecté
echo.

REM Vérifier si les dépendances sont installées
if not exist "node_modules" (
    echo 📦 Installation des dépendances...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Erreur lors de l'installation des dépendances
        pause
        exit /b 1
    )
    echo.
    echo ✅ Dépendances installées avec succès !
    echo.
)

echo 🚀 Démarrage de l'interface Nephiris AI...
echo.
echo 📱 L'application s'ouvrira automatiquement dans votre navigateur
echo 🌐 URL : http://localhost:3000
echo.
echo ⚡ Fonctionnalités disponibles :
echo   - Interface ChatGPT moderne
echo   - Thème sombre/clair
echo   - Historique des conversations
echo   - Suggestions intelligentes
echo   - Support Markdown
echo   - PWA (installable)
echo.
echo 🛑 Pour arrêter : Ctrl+C
echo.

REM Démarrer l'application React
npm start

pause
