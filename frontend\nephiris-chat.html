<!DOCTYPE html>
<html lang="fr" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nephiris AI</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔮</text></svg>">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Reset et variables */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --bg-primary: #0f0f23;
            --bg-secondary: #1a1a2e;
            --bg-tertiary: #16213e;
            --bg-input: #2d2d3a;
            --text-primary: #ffffff;
            --text-secondary: #a1a1aa;
            --text-muted: #52525b;
            --border: rgba(255, 255, 255, 0.1);
            --primary: #6366f1;
            --primary-hover: #5855eb;
            --radius: 12px;
        }

        [data-theme="light"] {
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --bg-input: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #475569;
            --text-muted: #94a3b8;
            --border: rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
        }

        /* Layout principal */
        .chat-container {
            display: flex;
            height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 260px;
            background: var(--bg-secondary);
            border-right: 1px solid var(--border);
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 16px;
            border-bottom: 1px solid var(--border);
        }

        .new-chat-btn {
            width: 100%;
            background: transparent;
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 12px 16px;
            color: var(--text-primary);
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s;
        }

        .new-chat-btn:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .conversations {
            flex: 1;
            overflow-y: auto;
            padding: 8px;
        }

        .conversation-item {
            padding: 12px;
            margin: 2px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .conversation-item:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .conversation-item.active {
            background: rgba(99, 102, 241, 0.1);
            color: var(--text-primary);
        }

        .sidebar-footer {
            padding: 16px;
            border-top: 1px solid var(--border);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-size: 14px;
            font-weight: 500;
        }

        .user-plan {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .theme-toggle {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: all 0.2s;
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
        }

        /* Zone principale */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            height: 60px;
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            padding: 0 24px;
            justify-content: space-between;
        }

        .chat-title {
            font-size: 18px;
            font-weight: 600;
        }

        .model-badge {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .menu-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            display: none;
        }

        /* Messages */
        .messages-area {
            flex: 1;
            overflow-y: auto;
            padding: 24px 0;
        }

        .welcome-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            padding: 0 24px;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 24px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .welcome-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .welcome-subtitle {
            font-size: 18px;
            color: var(--text-secondary);
            margin-bottom: 32px;
        }

        .suggestions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            max-width: 800px;
            width: 100%;
        }

        .suggestion-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 20px;
            cursor: pointer;
            transition: all 0.2s;
            text-align: left;
        }

        .suggestion-card:hover {
            border-color: var(--primary);
            transform: translateY(-2px);
        }

        .suggestion-icon {
            font-size: 24px;
            margin-bottom: 12px;
        }

        .suggestion-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .suggestion-desc {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .messages {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 24px;
        }

        .message {
            margin-bottom: 32px;
            display: flex;
            gap: 16px;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: var(--primary);
            color: white;
        }

        .message.ai .message-avatar {
            background: var(--bg-secondary);
            border: 1px solid var(--border);
        }

        .message-content {
            flex: 1;
            line-height: 1.6;
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .message-author {
            font-weight: 600;
            font-size: 14px;
        }

        .message-time {
            font-size: 12px;
            color: var(--text-muted);
        }

        .message-text {
            color: var(--text-primary);
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-secondary);
            font-style: italic;
            margin-top: 8px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: var(--text-secondary);
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        .typing-dot:nth-child(3) { animation-delay: 0s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        /* Zone de saisie */
        .input-area {
            padding: 24px;
            border-top: 1px solid var(--border);
        }

        .input-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .input-wrapper {
            background: var(--bg-input);
            border: 1px solid var(--border);
            border-radius: 24px;
            padding: 12px 20px;
            display: flex;
            align-items: flex-end;
            gap: 12px;
            transition: border-color 0.2s;
        }

        .input-wrapper:focus-within {
            border-color: var(--primary);
        }

        .message-input {
            flex: 1;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 16px;
            line-height: 1.5;
            resize: none;
            outline: none;
            min-height: 24px;
            max-height: 200px;
            font-family: inherit;
        }

        .message-input::placeholder {
            color: var(--text-muted);
        }

        .send-btn {
            background: var(--primary);
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            color: white;
        }

        .send-btn:hover:not(:disabled) {
            background: var(--primary-hover);
            transform: scale(1.05);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .input-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 12px;
            font-size: 12px;
            color: var(--text-muted);
        }

        .usage-info {
            color: var(--text-secondary);
        }

        .upgrade-link {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }

        .upgrade-link:hover {
            text-decoration: underline;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transform: translateX(-100%);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .chat-header {
                padding: 0 16px;
            }

            .menu-btn {
                display: block;
            }

            .suggestions {
                grid-template-columns: 1fr;
            }

            .messages {
                padding: 0 16px;
            }

            .input-area {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <button class="new-chat-btn" id="newChatBtn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 5v14M5 12h14"/>
                    </svg>
                    Nouvelle conversation
                </button>
            </div>
            
            <div class="conversations" id="conversations">
                <!-- Conversations dynamiques -->
            </div>
            
            <div class="sidebar-footer">
                <div class="user-avatar">U</div>
                <div class="user-info">
                    <div class="user-name">Utilisateur</div>
                    <div class="user-plan">Plan Gratuit</div>
                </div>
                <button class="theme-toggle" id="themeToggle">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Contenu principal -->
        <div class="main-content">
            <div class="chat-header">
                <button class="menu-btn" id="menuBtn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="3" y1="6" x2="21" y2="6"/>
                        <line x1="3" y1="12" x2="21" y2="12"/>
                        <line x1="3" y1="18" x2="21" y2="18"/>
                    </svg>
                </button>
                <div class="chat-title">Nephiris AI</div>
                <div class="model-badge">GPT-4 Turbo</div>
            </div>

            <div class="messages-area" id="messagesArea">
                <div class="welcome-screen" id="welcomeScreen">
                    <div class="logo">🔮</div>
                    <h1 class="welcome-title">Bonjour ! Je suis Nephiris AI</h1>
                    <p class="welcome-subtitle">Comment puis-je vous aider aujourd'hui ?</p>
                    
                    <div class="suggestions">
                        <div class="suggestion-card" data-prompt="Explique-moi le concept d'intelligence artificielle">
                            <div class="suggestion-icon">🧠</div>
                            <div class="suggestion-title">Expliquer l'IA</div>
                            <div class="suggestion-desc">Comprendre l'intelligence artificielle</div>
                        </div>
                        
                        <div class="suggestion-card" data-prompt="Aide-moi à rédiger un email professionnel">
                            <div class="suggestion-icon">✉️</div>
                            <div class="suggestion-title">Rédiger un email</div>
                            <div class="suggestion-desc">Communication professionnelle</div>
                        </div>
                        
                        <div class="suggestion-card" data-prompt="Crée-moi un plan d'étude pour apprendre Python">
                            <div class="suggestion-icon">📚</div>
                            <div class="suggestion-title">Plan d'apprentissage</div>
                            <div class="suggestion-desc">Organiser mes études</div>
                        </div>
                        
                        <div class="suggestion-card" data-prompt="Génère des idées créatives pour un projet">
                            <div class="suggestion-icon">💡</div>
                            <div class="suggestion-title">Brainstorming</div>
                            <div class="suggestion-desc">Idées créatives et innovation</div>
                        </div>
                    </div>
                </div>

                <div class="messages" id="messages" style="display: none;">
                    <!-- Messages dynamiques -->
                </div>
            </div>

            <div class="input-area">
                <div class="input-container">
                    <div class="input-wrapper">
                        <textarea 
                            class="message-input" 
                            id="messageInput" 
                            placeholder="Envoyez un message à Nephiris AI..."
                            rows="1"
                        ></textarea>
                        <button class="send-btn" id="sendBtn" disabled>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 2L11 13"/>
                                <path d="M22 2L15 22L11 13L2 9L22 2Z"/>
                            </svg>
                        </button>
                    </div>
                    <div class="input-footer">
                        <div class="usage-info">Plan Gratuit: <span id="messageCount">10</span>/15 messages aujourd'hui</div>
                        <a href="#" class="upgrade-link">Passer au Premium</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="nephiris-script.js"></script>
</body>
</html>
