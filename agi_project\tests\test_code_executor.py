"""
Tests for tools/code_executor.py
Verifies that the CodeExecutor can run basic code and handle errors.
"""


import pytest
from agi_project.tools.code_executor import CodeExecutor

def test_run_valid_code():
    executor = CodeExecutor()
    code = "x = 10\nassert x == 10"
    result = executor.run_code(code)
    assert "successfully" in result.lower()

def test_run_invalid_code():
    executor = CodeExecutor()
    code = "raise ValueError('Test Error')"
    result = executor.run_code(code)
    assert "Execution error:" in result
