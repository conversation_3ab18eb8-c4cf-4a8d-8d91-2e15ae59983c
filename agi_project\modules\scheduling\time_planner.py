"""
Module: time_planner.py

Ce module gère la planification et la gestion du temps pour diverses tâches ou projets,
que ce soit dans un cadre personnel (to-do list, rappels) ou professionnel 
(planification de milestones, deadlines, etc.).

Fonctionnalités :
- Création de tâches avec deadlines et priorités.
- Possibilité de définir des sous-tâches et dépendances entre tâches.
- Notifications (placeholder) ou retours en cas d’approche de la deadline.
- Ajustement dynamique du planning en fonction des avancements (gestion proactive
  des retards ou des opportunités pour terminer plus vite).
- Intégration possible avec un module de multi-agent (ex. multi_agent_coordinator)
  pour attribuer automatiquement des tâches à différents agents au fil du temps.

Exemple d’utilisation :
1. Instancier un TimePlanner.
2. Ajouter des tâches (ex.: “Créer documentation”, date limite, priorité haute).
3. Mettre à jour l’avancement ou marquer les tâches comme terminées.
4. Exporter ou synchroniser avec d’autres modules (ex. affichage dans le frontend).

Approche simplifiée :
- Utilise un stockage interne (dictionnaire); la gestion de notifications ou
  de rappels est simulée via un simple log. Dans une implémentation réelle,
  on ferait appel à un service d’alertes ou un module de scheduling dédié
  (cron, APScheduler, etc.).
"""

import datetime
from typing import List, Dict, Any, Optional

class TimePlanner:
    def __init__(self):
        """
        Initialise la structure de planification, stockée dans self.tasks, 
        qui contient une liste de dictionnaires décrivant chaque tâche.
        """
        self.tasks: List[Dict[str, Any]] = []

    def add_task(
        self,
        title: str,
        due_date: Optional[datetime.date] = None,
        priority: int = 1,
        description: str = ""
    ) -> str:
        """
        Ajoute une nouvelle tâche au planning.
          - title : nom de la tâche
          - due_date : date limite (peut être None si pas de deadline)
          - priority : niveau de priorité (1 = normal, plus c’est bas, plus c’est urgent)
          - description : détails supplémentaires
        Retourne l’ID unique de la tâche généré.
        """
        task_id = f"task_{len(self.tasks) + 1}"
        new_task = {
            "id": task_id,
            "title": title,
            "due_date": due_date.isoformat() if due_date else None,
            "priority": priority,
            "description": description,
            "completed": False,
            "created_at": datetime.datetime.now().isoformat()
        }
        self.tasks.append(new_task)
        return task_id

    def mark_completed(self, task_id: str) -> bool:
        """
        Marque la tâche spécifiée comme terminée. Retourne True en cas de succès.
        """
        for t in self.tasks:
            if t["id"] == task_id:
                t["completed"] = True
                return True
        return False

    def get_pending_tasks(self) -> List[Dict[str, Any]]:
        """
        Retourne la liste des tâches non terminées, triée par ordre de priorité.
        """
        pending = [t for t in self.tasks if not t["completed"]]
        # Tri du plus urgent (priority basse) vers moins urgent
        pending.sort(key=lambda x: x["priority"])
        return pending

    def reschedule_task(self, task_id: str, new_due_date: datetime.date) -> bool:
        """
        Met à jour la date limite d’une tâche. Retourne True si la tâche est trouvée.
        """
        for t in self.tasks:
            if t["id"] == task_id:
                t["due_date"] = new_due_date.isoformat()
                return True
        return False

    def remove_task(self, task_id: str) -> bool:
        """
        Supprime une tâche du planning. Retourne True si la tâche est trouvée et supprimée.
        """
        initial_count = len(self.tasks)
        self.tasks = [t for t in self.tasks if t["id"] != task_id]
        return len(self.tasks) < initial_count

    def get_task_by_id(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Retourne la tâche correspondant à l’ID passé en paramètre, ou None si elle est introuvable.
        """
        for t in self.tasks:
            if t["id"] == task_id:
                return t
        return None

    def check_deadlines(self) -> List[str]:
        """
        Vérifie les tâches dont la deadline est proche ou dépassée, 
        et émet une liste de messages de rappel.
        """
        reminders = []
        now = datetime.datetime.now().date()
        for t in self.tasks:
            if not t["completed"] and t["due_date"]:
                due_date = datetime.datetime.fromisoformat(t["due_date"]).date()
                delta = (due_date - now).days
                if delta < 0:
                    reminders.append(f"La tâche '{t['title']}' est en retard !")
                elif delta <= 1:
                    reminders.append(f"La tâche '{t['title']}' atteint sa deadline très bientôt !")
        return reminders
