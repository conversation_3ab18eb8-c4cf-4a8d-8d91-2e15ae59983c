"""
Module : interactive_story_generator.py

Ce module gère la génération de scénarios ou d’histoires interactives,
permettant à l’utilisateur de choisir son propre chemin narratif
(Jeu / Story mode).

Fonctionnalités clés :
- Génération d’un récit avec des embranchements (choices).
- Possibilité d’inclure des variables contextuelles (inventaire, stats).
- Mise en scène d’éléments narratifs (personnages, décor, événements).
- Extension possible pour se connecter à un moteur de jeu ou UI interactive.

Exemple d’utilisation :
story = InteractiveStoryGenerator.generate_story(
    topic="Aventure spatiale",
    branching_points=3,
    style="Science-Fiction"
)

Approche simplifiée :
- On simule la génération du contenu narratif avec du texte placeholder.
- On pourrait l’enrichir avec un moteur IA (GPT, Bloom, etc.) dans un contexte réel.
"""

import random
from typing import Dict, Any, List

class InteractiveStoryGenerator:
    @staticmethod
    def generate_story(
        topic: str,
        branching_points: int = 2,
        style: str = "Fantasy"
    ) -> Dict[str, Any]:
        """
        Génère le squelette d'une histoire interactive avec un certain nombre
        de points de branchement.
        - topic : thème général de l’histoire
        - branching_points : nombre d’embranchements narratifs
        - style : ton ou genre (fantastique, SF, polar, etc.)

        Retourne un dictionnaire de la forme :
        {
          "topic": topic,
          "style": style,
          "story_nodes": [
            {
              "id": 0,
              "text": "...",
              "choices": [
                { "choice_text": "...", "next_id": 1 },
                ...
              ]
            },
            { "id": 1, "text": "...", "choices": ... },
            ...
          ]
        }
        """
        story_nodes = []
        node_id = 0

        # Créer des embranchements simplifiés
        for i in range(branching_points + 1):
            node_text = (
                f"Node {i}: Voici un segment de l’histoire '{topic}' "
                f"en style {style}.\n"
                "Vous êtes face à un choix..."
            )
            # Générer quelques choix
            if i < branching_points:
                choices_count = random.randint(2, 3)
            else:
                choices_count = 1

            choices = []
            for c in range(choices_count):
                choice_text = f"Choix {chr(65 + c)}"
                # Le dernier node se boucle vers lui-même
                next_id = i + 1 if i < branching_points else i
                choices.append({"choice_text": choice_text, "next_id": next_id})

            story_nodes.append({
                "id": node_id,
                "text": node_text,
                "choices": choices
            })
            node_id += 1

        return {
            "topic": topic,
            "style": style,
            "story_nodes": story_nodes
        }

    @staticmethod
    def expand_story_branch(
        story_data: Dict[str, Any],
        branch_id: int
    ) -> Dict[str, Any]:
        """
        Permet d'étendre une branche de l’histoire afin d’y ajouter 
        plus de détails ou de nouveaux embranchements.
        """
        # Ajoute simplement un passage textuel pour la démo
        for node in story_data["story_nodes"]:
            if node["id"] == branch_id:
                node["text"] += "\nDes événements étranges se produisent, enrichissant l’histoire à ce point."
        return story_data
