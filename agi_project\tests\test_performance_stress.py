import pytest
import asyncio
import time
import random
from unittest.mock import AsyncMock, patch

"""
Test de performance et robustesse : 
  - Lancement simultané de multiples tâches sur divers agents 
  - Mesure de la charge CPU/mémoire (en simulation)
  - Vérification temps d’exécution 
"""

from agi_project.agent.multi_agent_system import Task
from agi_project.agent.creative_agent import CreativeAgent
from agi_project.agent.education_agent import EducationAgent
from agi_project.agent.code_agent import CodeAgent  # Suppose qu'il exécute du code
# On pourrait également inclure WebAgent, etc.

@pytest.mark.asyncio
class TestPerformanceStress:

    @pytest.fixture
    def creative_agent(self):
        return CreativeAgent(agent_id="creative_stress")

    @pytest.fixture
    def edu_agent(self):
        return EducationAgent(agent_id="edu_stress")

    @pytest.fixture
    def code_agent(self):
        # Suppose qu'on se moque de la partie exécution
        return CodeAgent(agent_id="code_stress")

    async def _simulate_tasks(self, agents, num_tasks=10):
        tasks = []
        for i in range(num_tasks):
            # Sélectionne un agent au hasard pour varier
            agent = random.choice(agents)
            task_type = random.choice(["image", "video", "quiz", "assessment"])
            desc = f"{task_type}:Sujet aléatoire:{i}"
            tasks.append(agent.execute_task(Task(id=f"task_{i}", description=desc)))

        start_time = time.time()
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        duration = end_time - start_time
        return results, duration

    @patch("agi_project.modules.generation.image_generator.ImageGenerator.generate", new_callable=AsyncMock)
    @patch("agi_project.modules.generation.video_script_generator.VideoScriptGenerator.generate", new_callable=AsyncMock)
    @patch("agi_project.modules.generation.voice_generator.VoiceGenerator.generate", new_callable=AsyncMock)
    @patch("agi_project.modules.generation.music_generator.MusicGenerator.generate", new_callable=AsyncMock)
    @patch("agi_project.modules.generation.quiz_generator.QuizGenerator.generate_quiz", new_callable=AsyncMock)
    @patch("agi_project.modules.nlp.question_answering.QuestionAnswering.generate_lesson", new_callable=AsyncMock)
    @patch("agi_project.modules.nlp.question_answering.QuestionAnswering.evaluate_answer", new_callable=AsyncMock)
    async def test_stress_agents(
        self,
        mock_eval_ans,
        mock_gen_lesson,
        mock_gen_quiz,
        mock_music,
        mock_voice,
        mock_video,
        mock_img,
        creative_agent,
        edu_agent,
        code_agent
    ):
        """
        Teste la performance en lançant plusieurs tâches simultanées 
        (Creative, Education, Code). On se contente de mesurer 
        le temps d'exécution et s'assurer qu'on n'a pas d'erreurs majeures.
        """
        # Mocks renvoyant des résultats simples
        mock_img.return_value = "img_data"
        mock_video.return_value = "video_data"
        mock_voice.return_value = "voice_data"
        mock_music.return_value = "music_data"
        mock_gen_quiz.return_value = [{"question": "Q1", "answer": "A1"}]
        mock_gen_lesson.return_value = "Some lesson content"
        mock_eval_ans.return_value = {
            "score": 0.7,
            "feedback": "OK",
            "improvement_suggestions": []
        }

        agents = [creative_agent, edu_agent, code_agent]

        # On lance 20 tâches au total. 
        results, duration = await self._simulate_tasks(agents, num_tasks=20)

        # Vérification de base : pas d'échecs massifs
        assert all(r["status"] in ("completed", "failed") for r in results)
        # Mesure du temps pour vérifier qu'on n'a pas un blocage 
        # (valeur indicative, ne pas bloquer si > x secondes, 
        #  c'est juste pour l'exemple)
        print(f"Executed 20 tasks in {duration:.2f} seconds.")

        # On pourrait analyser la distribution "completed"/"failed"
        # S'il y a trop de "failed", c'est suspect
        completed_count = sum(r["status"] == "completed" for r in results)
        assert completed_count >= 15, "Trop de tâches ont échoué sous la charge."

        # Vérifications additionnelles : 
        # On pourrait checker la mémoire, CPU, etc. 
        # ou simuler un stress test plus poussé via locust / etc.
