<!DOCTYPE html>
<html lang="fr" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nephiris AI - Chat</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔮</text></svg>">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="chat-style.css">
</head>
<body>
    <!-- Interface principale de chat -->
    <div class="chat-container">
        <!-- Sidebar avec historique des conversations -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <button class="new-chat-btn" id="newChatBtn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 5v14M5 12h14"/>
                    </svg>
                    Nouvelle conversation
                </button>
            </div>

            <div class="conversations-list" id="conversationsList">
                <!-- Les conversations seront ajoutées dynamiquement -->
            </div>

            <div class="sidebar-footer">
                <div class="user-menu">
                    <div class="user-info" id="userInfo">
                        <div class="user-avatar" id="userAvatar">U</div>
                        <div class="user-details">
                            <div class="user-name" id="userName">Utilisateur</div>
                            <div class="user-plan" id="userPlan">Plan Gratuit</div>
                        </div>
                    </div>
                    <button class="settings-btn" id="settingsBtn" title="Paramètres">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                        </svg>
                    </button>
                    <button class="theme-toggle" id="themeToggle" title="Changer le thème">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </aside>

        <!-- Zone principale de chat -->
        <main class="chat-main">
            <!-- Header -->
            <header class="chat-header">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="3" y1="6" x2="21" y2="6"/>
                        <line x1="3" y1="12" x2="21" y2="12"/>
                        <line x1="3" y1="18" x2="21" y2="18"/>
                    </svg>
                </button>
                <div class="chat-title">
                    <h1>Nephiris AI</h1>
                    <span class="model-info">Modèle GPT-4 Turbo</span>
                </div>
                <div class="header-actions">
                    <button class="action-btn" id="shareBtn" title="Partager la conversation">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
                            <polyline points="16,6 12,2 8,6"/>
                            <line x1="12" y1="2" x2="12" y2="15"/>
                        </svg>
                    </button>
                </div>
            </header>

            <!-- Zone des messages -->
            <div class="messages-container" id="messagesContainer">
                <div class="welcome-screen" id="welcomeScreen">
                    <div class="welcome-content">
                        <div class="nephiris-logo">
                            <svg width="80" height="80" viewBox="0 0 80 80" class="logo-animation">
                                <defs>
                                    <radialGradient id="logoGradient" cx="50%" cy="50%" r="50%">
                                        <stop offset="0%" style="stop-color:#6366f1;stop-opacity:0.9">
                                            <animate attributeName="stop-color" values="#6366f1;#8b5cf6;#06b6d4;#6366f1" dur="4s" repeatCount="indefinite"/>
                                        </stop>
                                        <stop offset="100%" style="stop-color:#1e1b4b;stop-opacity:1"/>
                                    </radialGradient>
                                </defs>
                                <circle cx="40" cy="40" r="35" fill="url(#logoGradient)"/>
                                <circle cx="40" cy="40" r="15" fill="#60a5fa" opacity="0.8">
                                    <animate attributeName="r" values="15;18;15" dur="2s" repeatCount="indefinite"/>
                                </circle>
                                <circle cx="30" cy="35" r="1.5" fill="#ffffff">
                                    <animate attributeName="opacity" values="0.5;1;0.5" dur="1.5s" repeatCount="indefinite"/>
                                </circle>
                                <circle cx="50" cy="45" r="1" fill="#ffffff">
                                    <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
                                </circle>
                            </svg>
                        </div>
                        <h2>Bonjour ! Je suis Nephiris AI</h2>
                        <p>Comment puis-je vous aider aujourd'hui ?</p>
                        
                        <div class="suggestion-cards">
                            <button class="suggestion-card" data-prompt="Explique-moi le concept d'intelligence artificielle de manière simple">
                                <div class="suggestion-icon">🧠</div>
                                <div class="suggestion-text">
                                    <h4>Expliquer l'IA</h4>
                                    <p>Comprendre l'intelligence artificielle</p>
                                </div>
                            </button>
                            
                            <button class="suggestion-card" data-prompt="Aide-moi à rédiger un email professionnel pour présenter un projet">
                                <div class="suggestion-icon">✉️</div>
                                <div class="suggestion-text">
                                    <h4>Rédiger un email</h4>
                                    <p>Communication professionnelle</p>
                                </div>
                            </button>
                            
                            <button class="suggestion-card" data-prompt="Crée-moi un plan d'étude pour apprendre Python en 30 jours">
                                <div class="suggestion-icon">📚</div>
                                <div class="suggestion-text">
                                    <h4>Plan d'apprentissage</h4>
                                    <p>Organiser mes études</p>
                                </div>
                            </button>
                            
                            <button class="suggestion-card" data-prompt="Génère des idées créatives pour un projet de startup tech">
                                <div class="suggestion-icon">💡</div>
                                <div class="suggestion-text">
                                    <h4>Brainstorming</h4>
                                    <p>Idées créatives et innovation</p>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="messages" id="messages">
                    <!-- Les messages seront ajoutés dynamiquement -->
                </div>
            </div>

            <!-- Zone de saisie -->
            <div class="input-area">
                <div class="input-container">
                    <div class="input-wrapper">
                        <textarea 
                            id="messageInput" 
                            placeholder="Envoyez un message à Nephiris AI..."
                            rows="1"
                            maxlength="4000"
                        ></textarea>
                        <button class="send-btn" id="sendBtn" disabled>
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 2L11 13"/>
                                <path d="M22 2L15 22L11 13L2 9L22 2Z"/>
                            </svg>
                        </button>
                    </div>
                    <div class="input-footer">
                        <div class="character-count">
                            <span id="charCount">0</span>/4000
                        </div>
                        <div class="input-actions">
                            <button class="action-btn" id="attachBtn" title="Joindre un fichier">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"/>
                                </svg>
                            </button>
                            <button class="action-btn" id="voiceBtn" title="Message vocal">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/>
                                    <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                                    <line x1="12" y1="19" x2="12" y2="23"/>
                                    <line x1="8" y1="23" x2="16" y2="23"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="usage-info" id="usageInfo">
                    <span class="usage-text">Plan Gratuit: <span id="usageCount">5</span>/15 messages aujourd'hui</span>
                    <button class="upgrade-btn" id="upgradeBtn">Passer au Premium</button>
                </div>
            </div>
        </main>
    </div>

    <!-- Modales -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal" id="modal">
            <div class="modal-header">
                <h3 id="modalTitle">Titre</h3>
                <button class="modal-close" id="modalClose">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-content" id="modalContent">
                <!-- Contenu dynamique -->
            </div>
        </div>
    </div>

    <!-- Notifications -->
    <div class="notifications-container" id="notifications"></div>

    <!-- Scripts -->
    <script src="chat-script.js"></script>
</body>
</html>
