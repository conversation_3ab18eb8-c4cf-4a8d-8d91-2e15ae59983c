import unittest
from agi_project.modules.generation.image_generator import ImageGenerator

class TestImageGenerator(unittest.TestCase):
    def setUp(self):
        self.generator = ImageGenerator()

    def test_generate_image_basic(self):
        prompt = "Un paysage montagneux au coucher du soleil"
        result = self.generator.generate(prompt)
        self.assertIn("image_url", result)
        self.assertIn("prompt", result)
        self.assertEqual(result["prompt"], prompt)

    def test_empty_prompt(self):
        with self.assertRaises(ValueError):
            self.generator.generate("")

    def test_size_options(self):
        prompt = "Un chat noir sur un canapé rouge"
        sizes = ["256x256", "512x512", "1024x1024"]
        for size in sizes:
            result = self.generator.generate(prompt, size=size)
            self.assertEqual(result["size"], size)

    def test_style_options(self):
        prompt = "Portrait d'une femme"
        styles = ["realistic", "cartoon", "abstract"]
        for style in styles:
            result = self.generator.generate(prompt, style=style)
            self.assertEqual(result["style"], style)

    def test_negative_prompt(self):
        prompt = "Une ville futuriste"
        negative = "voitures, personnes"
        result = self.generator.generate(prompt, negative_prompt=negative)
        self.assertEqual(result["negative_prompt"], negative)

if __name__ == '__main__':
    unittest.main()
