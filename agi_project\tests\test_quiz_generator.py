import unittest
from agi_project.modules.generation.quiz_generator import QuizGenerator

class TestQuizGenerator(unittest.TestCase):
    def setUp(self):
        self.generator = QuizGenerator()

    def test_generate_quiz_basic(self):
        text = "Paris est la capitale de la France."
        quiz = self.generator.generate(text)
        self.assertIn("questions", quiz)
        self.assertGreater(len(quiz["questions"]), 0)
        self.assertIn("Paris", quiz["questions"][0]["question"])

    def test_empty_input(self):
        with self.assertRaises(ValueError):
            self.generator.generate("")

    def test_difficulty_levels(self):
        text = "Le système solaire contient 8 planètes."
        for difficulty in ["easy", "medium", "hard"]:
            quiz = self.generator.generate(text, difficulty=difficulty)
            self.assertEqual(quiz["difficulty"], difficulty)

    def test_multiple_questions(self):
        text = "Python est un langage de programmation. Il est interprété et typé dynamiquement."
        quiz = self.generator.generate(text, num_questions=3)
        self.assertEqual(len(quiz["questions"]), 3)

    def test_answer_validation(self):
        text = "La Terre tourne autour du Soleil."
        quiz = self.generator.generate(text)
        self.assertTrue(any(q["correct_answer"] for q in quiz["questions"]))

if __name__ == '__main__':
    unittest.main()
