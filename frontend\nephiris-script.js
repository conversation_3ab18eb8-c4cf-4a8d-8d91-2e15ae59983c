class NephirisAI {
    constructor() {
        this.conversations = JSON.parse(localStorage.getItem('nephiris_conversations') || '[]');
        this.currentConversationId = null;
        this.messageCount = parseInt(localStorage.getItem('nephiris_message_count') || '0');
        this.maxMessages = 15;
        this.isTyping = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupTheme();
        this.loadConversations();
        this.updateMessageCount();
        this.setupTextareaResize();
        
        // Charger la dernière conversation ou afficher l'écran d'accueil
        if (this.conversations.length > 0) {
            this.loadConversation(this.conversations[0].id);
        }
    }

    setupEventListeners() {
        // Boutons principaux
        document.getElementById('newChatBtn').addEventListener('click', () => this.newConversation());
        document.getElementById('sendBtn').addEventListener('click', () => this.sendMessage());
        document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());
        document.getElementById('menuBtn').addEventListener('click', () => this.toggleSidebar());

        // Input de message
        const messageInput = document.getElementById('messageInput');
        messageInput.addEventListener('input', () => this.handleInputChange());
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Cartes de suggestion
        document.querySelectorAll('.suggestion-card').forEach(card => {
            card.addEventListener('click', () => {
                const prompt = card.dataset.prompt;
                messageInput.value = prompt;
                this.handleInputChange();
                this.sendMessage();
            });
        });
    }

    setupTextareaResize() {
        const textarea = document.getElementById('messageInput');
        textarea.addEventListener('input', () => {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
        });
    }

    setupTheme() {
        const savedTheme = localStorage.getItem('nephiris_theme') || 'dark';
        document.documentElement.setAttribute('data-theme', savedTheme);
        this.updateThemeIcon(savedTheme);
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('nephiris_theme', newTheme);
        this.updateThemeIcon(newTheme);
    }

    updateThemeIcon(theme) {
        const themeToggle = document.getElementById('themeToggle');
        const icon = theme === 'dark' 
            ? '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="5"/><path d="M12 1v2m0 18v2M4.22 4.22l1.42 1.42m12.72 12.72l1.42 1.42M1 12h2m18 0h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/></svg>'
            : '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/></svg>';
        themeToggle.innerHTML = icon;
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('open');
    }

    handleInputChange() {
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        
        sendBtn.disabled = !messageInput.value.trim() || this.isTyping;
    }

    newConversation() {
        const conversationId = this.generateId();
        const conversation = {
            id: conversationId,
            title: 'Nouvelle conversation',
            messages: [],
            createdAt: new Date().toISOString()
        };
        
        this.conversations.unshift(conversation);
        this.saveConversations();
        this.loadConversations();
        this.loadConversation(conversationId);
        
        // Focus sur l'input
        document.getElementById('messageInput').focus();
    }

    async sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const content = messageInput.value.trim();
        
        if (!content || this.isTyping) return;
        
        // Vérifier la limite de messages
        if (this.messageCount >= this.maxMessages) {
            alert('Limite de messages atteinte ! Passez au Premium pour continuer.');
            return;
        }

        // Créer une conversation si nécessaire
        if (!this.currentConversationId) {
            this.newConversation();
        }

        // Ajouter le message utilisateur
        this.addMessage('user', content);
        messageInput.value = '';
        this.handleInputChange();
        this.hideWelcomeScreen();

        // Incrémenter le compteur
        this.messageCount++;
        localStorage.setItem('nephiris_message_count', this.messageCount.toString());
        this.updateMessageCount();

        // Afficher l'indicateur de frappe
        this.showTypingIndicator();

        try {
            // Simuler l'appel API
            const response = await this.callAPI(content);
            this.hideTypingIndicator();
            this.addMessage('ai', response);
        } catch (error) {
            this.hideTypingIndicator();
            this.addMessage('ai', 'Désolé, une erreur s\'est produite. Veuillez réessayer.');
        }

        // Mettre à jour le titre de la conversation
        this.updateConversationTitle();
    }

    async callAPI(message) {
        // Simulation d'appel API - remplacer par vraie intégration
        return new Promise((resolve) => {
            setTimeout(() => {
                const responses = [
                    "Je comprends votre question. Voici une réponse détaillée qui prend en compte tous les aspects de votre demande. L'intelligence artificielle est un domaine fascinant qui continue d'évoluer rapidement.",
                    "Excellente question ! Laissez-moi vous expliquer cela de manière claire et structurée. Il y a plusieurs points importants à considérer dans cette situation.",
                    "Voici ce que je peux vous dire à ce sujet. Il y a plusieurs approches possibles, et je vais vous présenter les plus efficaces selon votre contexte.",
                    "Je vais vous aider avec cela. Voici une approche étape par étape pour résoudre votre problème de manière efficace et durable.",
                    "C'est un sujet très intéressant ! Permettez-moi de vous donner une explication complète avec des exemples concrets pour mieux illustrer le concept."
                ];
                
                const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                resolve(randomResponse);
            }, 1500 + Math.random() * 2000);
        });
    }

    addMessage(type, content) {
        const conversation = this.getCurrentConversation();
        if (!conversation) return;

        const message = {
            id: this.generateId(),
            type,
            content,
            timestamp: new Date().toISOString()
        };

        conversation.messages.push(message);
        this.saveConversations();
        this.renderMessage(message);
        this.scrollToBottom();
    }

    renderMessage(message) {
        const messagesContainer = document.getElementById('messages');
        const messageElement = document.createElement('div');
        messageElement.className = `message ${message.type}`;

        const avatar = message.type === 'user' ? 'U' : '🔮';
        const author = message.type === 'user' ? 'Vous' : 'Nephiris AI';
        const time = this.formatTime(message.timestamp);

        messageElement.innerHTML = `
            <div class="message-avatar">${avatar}</div>
            <div class="message-content">
                <div class="message-header">
                    <div class="message-author">${author}</div>
                    <div class="message-time">${time}</div>
                </div>
                <div class="message-text">${this.formatContent(message.content)}</div>
            </div>
        `;

        messagesContainer.appendChild(messageElement);
    }

    formatContent(content) {
        // Formatage basique du contenu
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code style="background: var(--bg-secondary); padding: 2px 6px; border-radius: 4px; font-family: monospace;">$1</code>')
            .replace(/\n/g, '<br>');
    }

    showTypingIndicator() {
        this.isTyping = true;
        const messagesContainer = document.getElementById('messages');
        
        const typingElement = document.createElement('div');
        typingElement.className = 'message ai';
        typingElement.id = 'typingIndicator';
        
        typingElement.innerHTML = `
            <div class="message-avatar">🔮</div>
            <div class="message-content">
                <div class="message-header">
                    <div class="message-author">Nephiris AI</div>
                </div>
                <div class="typing-indicator">
                    <span>est en train d'écrire</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        `;
        
        messagesContainer.appendChild(typingElement);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        this.isTyping = false;
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    hideWelcomeScreen() {
        document.getElementById('welcomeScreen').style.display = 'none';
        document.getElementById('messages').style.display = 'block';
    }

    showWelcomeScreen() {
        document.getElementById('welcomeScreen').style.display = 'flex';
        document.getElementById('messages').style.display = 'none';
    }

    loadConversations() {
        const conversationsContainer = document.getElementById('conversations');
        conversationsContainer.innerHTML = '';

        this.conversations.forEach(conversation => {
            const conversationElement = document.createElement('div');
            conversationElement.className = 'conversation-item';
            conversationElement.textContent = conversation.title;
            
            if (conversation.id === this.currentConversationId) {
                conversationElement.classList.add('active');
            }

            conversationElement.addEventListener('click', () => {
                this.loadConversation(conversation.id);
            });

            conversationsContainer.appendChild(conversationElement);
        });
    }

    loadConversation(conversationId) {
        this.currentConversationId = conversationId;
        const conversation = this.getCurrentConversation();
        
        if (!conversation) return;

        // Mettre à jour l'interface
        this.updateActiveConversation();
        this.renderMessages(conversation.messages);
        
        if (conversation.messages.length === 0) {
            this.showWelcomeScreen();
        } else {
            this.hideWelcomeScreen();
        }
    }

    updateActiveConversation() {
        document.querySelectorAll('.conversation-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const activeItems = document.querySelectorAll('.conversation-item');
        const activeIndex = this.conversations.findIndex(conv => conv.id === this.currentConversationId);
        if (activeItems[activeIndex]) {
            activeItems[activeIndex].classList.add('active');
        }
    }

    renderMessages(messages) {
        const messagesContainer = document.getElementById('messages');
        messagesContainer.innerHTML = '';
        
        messages.forEach(message => {
            this.renderMessage(message);
        });
        
        this.scrollToBottom();
    }

    updateConversationTitle() {
        const conversation = this.getCurrentConversation();
        if (!conversation || conversation.messages.length === 0) return;

        const firstUserMessage = conversation.messages.find(msg => msg.type === 'user');
        if (firstUserMessage && conversation.title === 'Nouvelle conversation') {
            conversation.title = firstUserMessage.content.length > 30 ? 
                firstUserMessage.content.substring(0, 30) + '...' : 
                firstUserMessage.content;
            
            this.saveConversations();
            this.loadConversations();
        }
    }

    updateMessageCount() {
        const messageCountElement = document.getElementById('messageCount');
        const remaining = Math.max(0, this.maxMessages - this.messageCount);
        messageCountElement.textContent = remaining;
        
        if (remaining <= 3) {
            messageCountElement.style.color = '#f59e0b';
        }
        if (remaining === 0) {
            messageCountElement.style.color = '#ef4444';
        }
    }

    scrollToBottom() {
        const messagesArea = document.getElementById('messagesArea');
        messagesArea.scrollTop = messagesArea.scrollHeight;
    }

    getCurrentConversation() {
        return this.conversations.find(conv => conv.id === this.currentConversationId);
    }

    saveConversations() {
        localStorage.setItem('nephiris_conversations', JSON.stringify(this.conversations));
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('fr-FR', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}

// Initialiser l'application
document.addEventListener('DOMContentLoaded', () => {
    new NephirisAI();
});
