<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Nephiris AI - L'intelligence qui vous comprend. Votre nouvelle intelligence artificielle personnelle.">
    <meta name="keywords" content="IA, intelligence artificielle, assistant, automation, Nephiris">
    <meta name="author" content="Nephiris AI">
    <meta name="robots" content="index, follow">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://nephiris.ai/">
    <meta property="og:title" content="Nephiris AI - L'intelligence qui vous comprend">
    <meta property="og:description" content="Votre nouvelle intelligence artificielle personnelle. Puissante, intelligente et visionnaire.">
    <meta property="og:image" content="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 630'><rect width='1200' height='630' fill='%230f0f23'/><text y='350' font-size='72' text-anchor='middle' x='600' fill='white'>🔮 Nephiris AI</text></svg>">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://nephiris.ai/">
    <meta property="twitter:title" content="Nephiris AI - L'intelligence qui vous comprend">
    <meta property="twitter:description" content="Votre nouvelle intelligence artificielle personnelle. Puissante, intelligente et visionnaire.">
    <meta property="twitter:image" content="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 630'><rect width='1200' height='630' fill='%230f0f23'/><text y='350' font-size='72' text-anchor='middle' x='600' fill='white'>🔮 Nephiris AI</text></svg>">

    <!-- PWA -->
    <meta name="theme-color" content="#6366f1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Nephiris AI">
    <meta name="msapplication-TileColor" content="#6366f1">
    <meta name="msapplication-config" content="browserconfig.xml">

    <title>Nephiris AI - L'intelligence qui vous comprend</title>

    <!-- Styles et fonts -->
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Space+Grotesk:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🔮</text></svg>">
    <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 180 180'><rect width='180' height='180' fill='%236366f1' rx='40'/><text y='125' font-size='100' text-anchor='middle' x='90' fill='white'>🔮</text></svg>">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Scripts IA -->
    <script src="ai-engine.js"></script>
    <script src="markdown-renderer.js"></script>
</head>

<body>
    <header class="main-header">
        <div class="nav-container">
            <div class="logo">
                <span class="logo-icon">🔮</span>
                Nephiris AI
            </div>
            <nav class="nav-links">
                <a href="#features" class="nav-link">Fonctionnalités</a>
                <a href="#pricing" class="nav-link">Tarifs</a>
                <a href="#benefits" class="nav-link">Avantages</a>
                <a href="#demo" class="nav-link">Démo</a>
            </nav>
            <div class="nav-actions">
                <button class="theme-toggle" id="theme-toggle" aria-label="Changer de thème">
                    <span class="theme-icon">🌙</span>
                </button>
                <button class="btn-google" id="google-login">
                    <svg width="18" height="18" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Connexion Google
                </button>
                <button class="btn-primary" id="try-ai-btn">Essayer l'IA</button>
            </div>
        </div>
    </header>

    <section class="hero" id="hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1>L'intelligence artificielle qui vous comprend</h1>
                <p class="hero-subtitle">Nephiris AI - Votre assistant intelligent personnel</p>
                <p class="hero-description">
                    Automatisez vos tâches, analysez vos données et boostez votre productivité
                    avec une IA qui s'adapte parfaitement à votre façon de travailler.
                </p>

                <div class="hero-actions">
                    <button class="btn-primary btn-large" id="start-ai-btn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        Utiliser l'IA maintenant
                    </button>
                </div>
            </div>

            <div class="hero-visual" id="demo">
                <div class="nephiris-showcase">
                    <!-- Visualisation principale -->
                    <svg width="500" height="400" viewBox="0 0 500 400" class="nephiris-main-visual">
                        <defs>
                            <!-- Gradients animés -->
                            <radialGradient id="mainGradient" cx="50%" cy="50%" r="60%">
                                <stop offset="0%" style="stop-color:#6366f1;stop-opacity:0.9">
                                    <animate attributeName="stop-color" values="#6366f1;#8b5cf6;#06b6d4;#10b981;#6366f1" dur="8s" repeatCount="indefinite"/>
                                </stop>
                                <stop offset="100%" style="stop-color:#1e1b4b;stop-opacity:0.3"/>
                            </radialGradient>

                            <linearGradient id="neuralGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:0.8"/>
                                <stop offset="50%" style="stop-color:#a78bfa;stop-opacity:0.6"/>
                                <stop offset="100%" style="stop-color:#34d399;stop-opacity:0.8"/>
                            </linearGradient>

                            <!-- Filtres pour effets -->
                            <filter id="glow">
                                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                                <feMerge>
                                    <feMergeNode in="coloredBlur"/>
                                    <feMergeNode in="SourceGraphic"/>
                                </feMerge>
                            </filter>
                        </defs>

                        <!-- Fond avec particules -->
                        <rect width="500" height="400" fill="url(#mainGradient)" opacity="0.1" rx="20"/>

                        <!-- Réseau neuronal central -->
                        <g class="neural-network" transform="translate(250, 200)">
                            <!-- Nœud central principal -->
                            <circle cx="0" cy="0" r="40" fill="url(#neuralGradient)" filter="url(#glow)">
                                <animate attributeName="r" values="40;50;40" dur="3s" repeatCount="indefinite"/>
                            </circle>

                            <!-- Nœuds secondaires -->
                            <g class="secondary-nodes">
                                <!-- Nœud 1 -->
                                <circle cx="-80" cy="-60" r="20" fill="#60a5fa" opacity="0.8">
                                    <animate attributeName="cy" values="-60;-70;-60" dur="4s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" values="0.8;1;0.8" dur="4s" repeatCount="indefinite"/>
                                </circle>

                                <!-- Nœud 2 -->
                                <circle cx="80" cy="-60" r="18" fill="#a78bfa" opacity="0.7">
                                    <animate attributeName="cy" values="-60;-50;-60" dur="3.5s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" values="0.7;1;0.7" dur="3.5s" repeatCount="indefinite"/>
                                </circle>

                                <!-- Nœud 3 -->
                                <circle cx="-100" cy="40" r="22" fill="#34d399" opacity="0.9">
                                    <animate attributeName="cy" values="40;50;40" dur="4.5s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" values="0.9;1;0.9" dur="4.5s" repeatCount="indefinite"/>
                                </circle>

                                <!-- Nœud 4 -->
                                <circle cx="90" cy="50" r="16" fill="#f59e0b" opacity="0.8">
                                    <animate attributeName="cy" values="50;40;50" dur="3.8s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" values="0.8;1;0.8" dur="3.8s" repeatCount="indefinite"/>
                                </circle>

                                <!-- Nœud 5 -->
                                <circle cx="0" cy="-100" r="14" fill="#ef4444" opacity="0.7">
                                    <animate attributeName="cy" values="-100;-110;-100" dur="3.2s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" values="0.7;1;0.7" dur="3.2s" repeatCount="indefinite"/>
                                </circle>

                                <!-- Nœud 6 -->
                                <circle cx="0" cy="90" r="19" fill="#8b5cf6" opacity="0.8">
                                    <animate attributeName="cy" values="90;100;90" dur="4.2s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" values="0.8;1;0.8" dur="4.2s" repeatCount="indefinite"/>
                                </circle>
                            </g>

                            <!-- Connexions animées -->
                            <g class="connections" stroke-width="2" fill="none">
                                <!-- Connexion vers nœud 1 -->
                                <path d="M 0 0 Q -40 -30 -80 -60" stroke="#60a5fa" opacity="0.6">
                                    <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
                                    <animate attributeName="stroke-dasharray" values="0,100;50,50;100,0" dur="3s" repeatCount="indefinite"/>
                                </path>

                                <!-- Connexion vers nœud 2 -->
                                <path d="M 0 0 Q 40 -30 80 -60" stroke="#a78bfa" opacity="0.5">
                                    <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.5s" repeatCount="indefinite"/>
                                    <animate attributeName="stroke-dasharray" values="0,100;50,50;100,0" dur="3.5s" repeatCount="indefinite"/>
                                </path>

                                <!-- Connexion vers nœud 3 -->
                                <path d="M 0 0 Q -50 20 -100 40" stroke="#34d399" opacity="0.7">
                                    <animate attributeName="opacity" values="0.7;1;0.7" dur="1.8s" repeatCount="indefinite"/>
                                    <animate attributeName="stroke-dasharray" values="0,100;50,50;100,0" dur="2.8s" repeatCount="indefinite"/>
                                </path>

                                <!-- Connexion vers nœud 4 -->
                                <path d="M 0 0 Q 45 25 90 50" stroke="#f59e0b" opacity="0.6">
                                    <animate attributeName="opacity" values="0.6;1;0.6" dur="2.2s" repeatCount="indefinite"/>
                                    <animate attributeName="stroke-dasharray" values="0,100;50,50;100,0" dur="3.2s" repeatCount="indefinite"/>
                                </path>

                                <!-- Connexion vers nœud 5 -->
                                <path d="M 0 0 L 0 -100" stroke="#ef4444" opacity="0.5">
                                    <animate attributeName="opacity" values="0.5;0.9;0.5" dur="1.5s" repeatCount="indefinite"/>
                                    <animate attributeName="stroke-dasharray" values="0,100;50,50;100,0" dur="2.5s" repeatCount="indefinite"/>
                                </path>

                                <!-- Connexion vers nœud 6 -->
                                <path d="M 0 0 L 0 90" stroke="#8b5cf6" opacity="0.6">
                                    <animate attributeName="opacity" values="0.6;1;0.6" dur="2.8s" repeatCount="indefinite"/>
                                    <animate attributeName="stroke-dasharray" values="0,100;50,50;100,0" dur="3.8s" repeatCount="indefinite"/>
                                </path>
                            </g>

                            <!-- Particules flottantes autour du réseau -->
                            <g class="floating-particles">
                                <circle cx="-150" cy="-80" r="3" fill="#60a5fa" opacity="0.6">
                                    <animate attributeName="cy" values="-80;-100;-80" dur="5s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" values="0.6;1;0.6" dur="5s" repeatCount="indefinite"/>
                                </circle>

                                <circle cx="140" cy="-90" r="2.5" fill="#a78bfa" opacity="0.7">
                                    <animate attributeName="cy" values="-90;-70;-90" dur="4.5s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" values="0.7;1;0.7" dur="4.5s" repeatCount="indefinite"/>
                                </circle>

                                <circle cx="-160" cy="70" r="4" fill="#34d399" opacity="0.5">
                                    <animate attributeName="cy" values="70;90;70" dur="6s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" values="0.5;1;0.5" dur="6s" repeatCount="indefinite"/>
                                </circle>

                                <circle cx="150" cy="80" r="2" fill="#f59e0b" opacity="0.8">
                                    <animate attributeName="cy" values="80;60;80" dur="3.5s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" values="0.8;1;0.8" dur="3.5s" repeatCount="indefinite"/>
                                </circle>
                            </g>
                        </g>

                        <!-- Texte animé -->
                        <text x="250" y="350" text-anchor="middle" fill="#6366f1" font-family="Space Grotesk, sans-serif" font-size="24" font-weight="700">
                            Intelligence Artificielle Avancée
                            <animate attributeName="opacity" values="0.7;1;0.7" dur="4s" repeatCount="indefinite"/>
                        </text>

                        <!-- Indicateurs de performance -->
                        <g class="performance-indicators" transform="translate(50, 50)">
                            <rect x="0" y="0" width="120" height="60" rx="10" fill="rgba(99, 102, 241, 0.1)" stroke="#6366f1" stroke-width="1"/>
                            <text x="60" y="20" text-anchor="middle" fill="#6366f1" font-size="12" font-weight="600">Vitesse</text>
                            <text x="60" y="40" text-anchor="middle" fill="#60a5fa" font-size="18" font-weight="700">
                                <animate attributeName="fill" values="#60a5fa;#34d399;#60a5fa" dur="2s" repeatCount="indefinite"/>
                                1.2s
                            </text>
                        </g>

                        <g class="accuracy-indicator" transform="translate(330, 50)">
                            <rect x="0" y="0" width="120" height="60" rx="10" fill="rgba(52, 211, 153, 0.1)" stroke="#34d399" stroke-width="1"/>
                            <text x="60" y="20" text-anchor="middle" fill="#34d399" font-size="12" font-weight="600">Précision</text>
                            <text x="60" y="40" text-anchor="middle" fill="#10b981" font-size="18" font-weight="700">
                                <animate attributeName="fill" values="#10b981;#6366f1;#10b981" dur="2.5s" repeatCount="indefinite"/>
                                99.7%
                            </text>
                        </g>
                    </svg>

                    <!-- Overlay avec informations -->
                    <div class="visual-overlay">
                        <div class="feature-highlight">
                            <div class="highlight-item">
                                <div class="highlight-icon">🧠</div>
                                <div class="highlight-text">
                                    <h4>IA Neuronale</h4>
                                    <p>Réseau de neurones avancé</p>
                                </div>
                            </div>

                            <div class="highlight-item">
                                <div class="highlight-icon">⚡</div>
                                <div class="highlight-text">
                                    <h4>Ultra Rapide</h4>
                                    <p>Réponses en temps réel</p>
                                </div>
                            </div>

                            <div class="highlight-item">
                                <div class="highlight-icon">🎯</div>
                                <div class="highlight-text">
                                    <h4>Précision</h4>
                                    <p>Résultats optimaux</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Particules interactives -->
        <div class="particles-container" id="particles"></div>
    </section>

    <!-- Section Statistiques -->
    <section class="stats" id="stats">
        <div class="container">
            <div class="section-header">
                <h2>Pourquoi choisir Nephiris AI</h2>
                <p>Une technologie de pointe au service de votre productivité</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">⚡</div>
                    <div class="stat-number" data-target="95">0</div>
                    <div class="stat-unit">%</div>
                    <div class="stat-label">Gain de productivité</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">🚀</div>
                    <div class="stat-number" data-target="1.2">0</div>
                    <div class="stat-unit">s</div>
                    <div class="stat-label">Temps de réponse</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number" data-target="1200">0</div>
                    <div class="stat-unit">+</div>
                    <div class="stat-label">Utilisateurs bêta</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">🎯</div>
                    <div class="stat-number" data-target="24">0</div>
                    <div class="stat-unit">/7</div>
                    <div class="stat-label">Support disponible</div>
                </div>
            </div>
        </div>
    </section>

    <section class="features" id="features">
        <div class="container">
            <div class="section-header">
                <h2>Découvrez Nephiris en action</h2>
                <p>Explorez des exemples concrets de la façon dont Nephiris accomplit des tâches complexes de manière autonome</p>
            </div>

            <div class="features-grid">
                <div class="feature-card" data-feature="research">
                    <div class="feature-icon">🔍</div>
                    <h3>Recherche & Analyse</h3>
                    <p>Trouve les meilleures options selon vos critères, compare les prix et analyse les avis utilisateurs</p>
                    <button class="feature-btn">Voir l'exemple</button>
                </div>

                <div class="feature-card" data-feature="strategy">
                    <div class="feature-icon">📊</div>
                    <h3>Stratégie Marketing</h3>
                    <p>Développe des stratégies go-to-market complètes avec analyse de marché et timeline de lancement</p>
                    <button class="feature-btn">Voir l'exemple</button>
                </div>

                <div class="feature-card" data-feature="planning">
                    <div class="feature-icon">✈️</div>
                    <h3>Planification Voyage</h3>
                    <p>Organise des voyages complets avec itinéraires, hébergements et activités personnalisées</p>
                    <button class="feature-btn">Voir l'exemple</button>
                </div>

                <div class="feature-card" data-feature="automation">
                    <div class="feature-icon">⚡</div>
                    <h3>Automatisation</h3>
                    <p>Automatise vos tâches répétitives et optimise vos processus de travail</p>
                    <button class="feature-btn">Voir l'exemple</button>
                </div>

                <div class="feature-card" data-feature="analysis">
                    <div class="feature-icon">📈</div>
                    <h3>Analyse de Données</h3>
                    <p>Traite et analyse vos données pour générer des insights actionnables</p>
                    <button class="feature-btn">Voir l'exemple</button>
                </div>

                <div class="feature-card" data-feature="communication">
                    <div class="feature-icon">�</div>
                    <h3>Communication</h3>
                    <p>Rédige des emails personnalisés et gère vos communications professionnelles</p>
                    <button class="feature-btn">Voir l'exemple</button>
                </div>
            </div>
        </div>
    </section>


    <section class="pricing" id="pricing">
        <div class="container">
            <div class="section-header">
                <h2>Commencez avec Nephiris aujourd'hui</h2>
                <p>Choisissez le plan qui correspond à vos besoins</p>
            </div>

            <div class="pricing-grid">
                <div class="price-card">
                    <div class="plan-header">
                        <h3>Découverte</h3>
                        <p class="plan-description">Parfait pour commencer</p>
                    </div>
                    <div class="price">
                        <span class="currency">CAD</span>
                        <span class="amount">0</span>
                        <span class="period">/mois</span>
                    </div>
                    <ul class="features-list">
                        <li>✓ 15 requêtes par jour</li>
                        <li>✓ Fonctionnalités de base</li>
                        <li>✓ Support communautaire</li>
                        <li>✓ Accès à la documentation</li>
                    </ul>
                    <button class="price-button" data-plan="free">Commencer gratuitement</button>
                </div>

                <div class="price-card featured">
                    <div class="popular-badge">Le plus populaire</div>
                    <div class="plan-header">
                        <h3>Pro</h3>
                        <p class="plan-description">Pour les professionnels</p>
                    </div>
                    <div class="price">
                        <span class="currency">CAD</span>
                        <span class="amount">39</span>
                        <span class="period">/mois</span>
                    </div>
                    <ul class="features-list">
                        <li>✓ Requêtes illimitées</li>
                        <li>✓ Toutes les fonctionnalités</li>
                        <li>✓ Support prioritaire</li>
                        <li>✓ Intégrations avancées</li>
                        <li>✓ Analyses détaillées</li>
                    </ul>
                    <button class="price-button" data-plan="pro">Choisir Pro</button>
                </div>

                <div class="price-card">
                    <div class="plan-header">
                        <h3>Entreprise</h3>
                        <p class="plan-description">Solutions sur mesure</p>
                    </div>
                    <div class="price">
                        <span class="currency">CAD</span>
                        <span class="amount">129</span>
                        <span class="period">/mois</span>
                    </div>
                    <ul class="features-list">
                        <li>✓ Tout du plan Pro</li>
                        <li>✓ Support 24/7</li>
                        <li>✓ Déploiement privé</li>
                        <li>✓ Formation personnalisée</li>
                        <li>✓ SLA garanti</li>
                    </ul>
                    <button class="price-button" data-plan="enterprise">Contacter l'équipe</button>
                </div>
            </div>
        </div>
    </section>


    <!-- Section Avantages -->
    <section class="benefits" id="benefits">
        <div class="container">
            <div class="section-header">
                <h2>Pourquoi choisir Nephiris AI</h2>
                <p>Une intelligence artificielle conçue pour votre réussite</p>
            </div>

            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-icon">🚀</div>
                    <h3>Performance optimale</h3>
                    <p>Réponses en moins de 1.2 seconde avec une précision exceptionnelle pour tous vos besoins professionnels.</p>
                    <div class="benefit-metric">
                        <span class="metric-value">95%</span>
                        <span class="metric-label">de satisfaction</span>
                    </div>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">🔒</div>
                    <h3>Sécurité garantie</h3>
                    <p>Vos données restent privées et sécurisées. Aucune information n'est partagée avec des tiers.</p>
                    <div class="benefit-metric">
                        <span class="metric-value">100%</span>
                        <span class="metric-label">confidentiel</span>
                    </div>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">💡</div>
                    <h3>Innovation continue</h3>
                    <p>Mises à jour régulières avec de nouvelles fonctionnalités basées sur les dernières avancées en IA.</p>
                    <div class="benefit-metric">
                        <span class="metric-value">24/7</span>
                        <span class="metric-label">disponible</span>
                    </div>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">🎯</div>
                    <h3>Résultats mesurables</h3>
                    <p>Augmentez votre productivité de manière quantifiable avec des outils d'analyse intégrés.</p>
                    <div class="benefit-metric">
                        <span class="metric-value">3x</span>
                        <span class="metric-label">plus rapide</span>
                    </div>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">🌐</div>
                    <h3>Multilingue</h3>
                    <p>Support de 5 langues principales avec traduction contextuelle et adaptation culturelle.</p>
                    <div class="benefit-metric">
                        <span class="metric-value">5</span>
                        <span class="metric-label">langues</span>
                    </div>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">⚡</div>
                    <h3>Intégration facile</h3>
                    <p>Déployez Nephiris AI en quelques minutes dans votre workflow existant sans formation complexe.</p>
                    <div class="benefit-metric">
                        <span class="metric-value">5min</span>
                        <span class="metric-label">setup</span>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <footer class="main-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="footer-logo">
                        <span class="logo-icon">🔮</span>
                        Nephiris AI
                    </div>
                    <p class="footer-tagline">L'intelligence qui vous comprend</p>
                    <div class="social-links">
                        <a href="#" class="social-link" aria-label="Twitter">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link" aria-label="LinkedIn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link" aria-label="GitHub">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <div class="footer-links">
                    <div class="footer-column">
                        <h4>Produit</h4>
                        <a href="#features">Fonctionnalités</a>
                        <a href="#pricing">Tarifs</a>
                        <a href="#demo">Démo</a>
                        <a href="#" id="changelog-link">Nouveautés</a>
                    </div>

                    <div class="footer-column">
                        <h4>Ressources</h4>
                        <a href="#" id="docs-link">Documentation</a>
                        <a href="#" id="blog-link">Blog</a>
                        <a href="#" id="support-link">Support</a>
                        <a href="#" id="community-link">Communauté</a>
                    </div>

                    <div class="footer-column">
                        <h4>Entreprise</h4>
                        <a href="#" id="about-link">À propos</a>
                        <a href="#" id="careers-link">Carrières</a>
                        <a href="#" id="contact-link">Contact</a>
                        <a href="#" id="press-link">Presse</a>
                    </div>

                    <div class="footer-column">
                        <h4>Légal</h4>
                        <a href="#" id="privacy-link">Confidentialité</a>
                        <a href="#" id="terms-link">Conditions</a>
                        <a href="#" id="cookies-link">Cookies</a>
                        <a href="#" id="security-link">Sécurité</a>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 Nephiris AI. Tous droits réservés.</p>
                <div class="footer-bottom-links">
                    <span class="status-indicator">
                        <span class="status-dot"></span>
                        Tous les systèmes opérationnels
                    </span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Interface IA Principale -->
    <div class="ai-interface" id="ai-interface">
        <div class="ai-header">
            <div class="ai-title">
                <div class="ai-logo">🔮</div>
                <div class="ai-info">
                    <h3>Nephiris AI</h3>
                    <span class="ai-status" id="ai-status">Connectez-vous pour commencer</span>
                </div>
            </div>
            <div class="ai-controls">
                <button class="ai-control-btn" id="ai-export-btn" title="Exporter la conversation">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                </button>
                <button class="ai-control-btn" id="ai-clear-btn" title="Effacer la conversation">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                    </svg>
                </button>
                <button class="ai-control-btn" id="ai-settings-btn" title="Paramètres">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                    </svg>
                </button>
                <button class="ai-control-btn" id="ai-close-btn" title="Fermer">×</button>
            </div>
        </div>

        <!-- Zone de connexion -->
        <div class="ai-login-section" id="ai-login-section">
            <div class="ai-login-content">
                <div class="ai-login-visual">
                    <svg width="200" height="200" viewBox="0 0 200 200" class="nephiris-avatar">
                        <!-- Fond avec gradient animé -->
                        <defs>
                            <radialGradient id="bgGradient" cx="50%" cy="50%" r="50%">
                                <stop offset="0%" style="stop-color:#6366f1;stop-opacity:0.8">
                                    <animate attributeName="stop-color" values="#6366f1;#8b5cf6;#06b6d4;#6366f1" dur="4s" repeatCount="indefinite"/>
                                </stop>
                                <stop offset="100%" style="stop-color:#1e1b4b;stop-opacity:1"/>
                            </radialGradient>
                            <linearGradient id="orbGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#60a5fa"/>
                                <stop offset="50%" style="stop-color:#a78bfa"/>
                                <stop offset="100%" style="stop-color:#34d399"/>
                            </linearGradient>
                        </defs>

                        <!-- Cercle de fond -->
                        <circle cx="100" cy="100" r="90" fill="url(#bgGradient)" opacity="0.9"/>

                        <!-- Particules flottantes -->
                        <circle cx="60" cy="70" r="3" fill="#60a5fa" opacity="0.7">
                            <animate attributeName="cy" values="70;50;70" dur="3s" repeatCount="indefinite"/>
                            <animate attributeName="opacity" values="0.7;1;0.7" dur="3s" repeatCount="indefinite"/>
                        </circle>
                        <circle cx="140" cy="130" r="2" fill="#a78bfa" opacity="0.6">
                            <animate attributeName="cy" values="130;110;130" dur="4s" repeatCount="indefinite"/>
                            <animate attributeName="opacity" values="0.6;1;0.6" dur="4s" repeatCount="indefinite"/>
                        </circle>
                        <circle cx="80" cy="150" r="2.5" fill="#34d399" opacity="0.8">
                            <animate attributeName="cy" values="150;130;150" dur="3.5s" repeatCount="indefinite"/>
                            <animate attributeName="opacity" values="0.8;1;0.8" dur="3.5s" repeatCount="indefinite"/>
                        </circle>

                        <!-- Orbe central (cerveau IA) -->
                        <circle cx="100" cy="100" r="35" fill="url(#orbGradient)" opacity="0.9">
                            <animate attributeName="r" values="35;40;35" dur="2s" repeatCount="indefinite"/>
                        </circle>

                        <!-- Connexions neuronales -->
                        <g stroke="#60a5fa" stroke-width="1.5" fill="none" opacity="0.6">
                            <path d="M 70 80 Q 85 90 100 85 Q 115 80 130 90">
                                <animate attributeName="opacity" values="0.6;1;0.6" dur="2.5s" repeatCount="indefinite"/>
                            </path>
                            <path d="M 80 120 Q 90 105 100 115 Q 110 125 120 110">
                                <animate attributeName="opacity" values="0.4;0.9;0.4" dur="3s" repeatCount="indefinite"/>
                            </path>
                            <path d="M 85 95 Q 100 100 115 95">
                                <animate attributeName="opacity" values="0.5;1;0.5" dur="1.8s" repeatCount="indefinite"/>
                            </path>
                        </g>

                        <!-- Points de connexion -->
                        <circle cx="70" cy="80" r="2" fill="#60a5fa">
                            <animate attributeName="fill" values="#60a5fa;#ffffff;#60a5fa" dur="2.5s" repeatCount="indefinite"/>
                        </circle>
                        <circle cx="130" cy="90" r="2" fill="#a78bfa">
                            <animate attributeName="fill" values="#a78bfa;#ffffff;#a78bfa" dur="3s" repeatCount="indefinite"/>
                        </circle>
                        <circle cx="100" cy="85" r="1.5" fill="#34d399">
                            <animate attributeName="fill" values="#34d399;#ffffff;#34d399" dur="1.8s" repeatCount="indefinite"/>
                        </circle>

                        <!-- Texte Nephiris -->
                        <text x="100" y="180" text-anchor="middle" fill="#6366f1" font-family="Space Grotesk, sans-serif" font-size="16" font-weight="600">
                            Nephiris AI
                            <animate attributeName="opacity" values="0.7;1;0.7" dur="3s" repeatCount="indefinite"/>
                        </text>
                    </svg>
                </div>
                <h3>Connectez-vous pour utiliser Nephiris AI</h3>
                <p>Accédez à toutes les fonctionnalités d'intelligence artificielle</p>
                <button class="btn-google-modern" id="ai-google-login">
                    <div class="google-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                    </div>
                    <span>Continuer avec Google</span>
                </button>
                <div class="ai-login-features">
                    <div class="feature-item">✓ Conversations illimitées</div>
                    <div class="feature-item">✓ Historique sauvegardé</div>
                    <div class="feature-item">✓ Modèles IA avancés</div>
                </div>
            </div>
        </div>

        <!-- Interface IA principale -->
        <div class="ai-main-interface" id="ai-main-interface" style="display: none;">
            <div class="ai-sidebar">
                <div class="ai-user-info" id="ai-user-info">
                    <div class="user-avatar" id="user-avatar"></div>
                    <div class="user-details">
                        <div class="user-name" id="user-name">Utilisateur</div>
                        <div class="user-plan" id="user-plan">Plan Gratuit</div>
                    </div>
                    <button class="upgrade-btn" id="upgrade-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                        </svg>
                        Premium
                    </button>
                </div>

                <div class="ai-conversations" id="ai-conversations">
                    <div class="conversations-header">
                        <h4>Conversations</h4>
                        <button class="new-conversation-btn" id="new-conversation-btn">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                            </svg>
                        </button>
                    </div>
                    <div class="conversations-list" id="conversations-list">
                        <!-- Les conversations seront ajoutées dynamiquement -->
                    </div>
                </div>
            </div>

            <div class="ai-chat-area">
                <div class="ai-messages" id="ai-messages">
                    <div class="welcome-message">
                        <div class="welcome-avatar">
                            <svg width="80" height="80" viewBox="0 0 80 80" class="nephiris-mini-avatar">
                                <defs>
                                    <radialGradient id="miniGradient" cx="50%" cy="50%" r="50%">
                                        <stop offset="0%" style="stop-color:#6366f1;stop-opacity:0.9">
                                            <animate attributeName="stop-color" values="#6366f1;#8b5cf6;#06b6d4;#6366f1" dur="3s" repeatCount="indefinite"/>
                                        </stop>
                                        <stop offset="100%" style="stop-color:#1e1b4b;stop-opacity:1"/>
                                    </radialGradient>
                                </defs>
                                <circle cx="40" cy="40" r="35" fill="url(#miniGradient)"/>
                                <circle cx="40" cy="40" r="15" fill="#60a5fa" opacity="0.8">
                                    <animate attributeName="r" values="15;18;15" dur="2s" repeatCount="indefinite"/>
                                </circle>
                                <circle cx="30" cy="35" r="1.5" fill="#ffffff">
                                    <animate attributeName="opacity" values="0.5;1;0.5" dur="1.5s" repeatCount="indefinite"/>
                                </circle>
                                <circle cx="50" cy="45" r="1" fill="#ffffff">
                                    <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
                                </circle>
                            </svg>
                        </div>
                        <h3>Bienvenue dans Nephiris AI</h3>
                        <p>Posez-moi n'importe quelle question ou demandez-moi d'effectuer une tâche.</p>
                        <div class="quick-actions">
                            <button class="quick-action-btn" data-prompt="Aide-moi à rédiger un email professionnel pour [destinataire] concernant [sujet]">✉️ Rédiger un email</button>
                            <button class="quick-action-btn" data-prompt="Analyse ces données et donne-moi des insights détaillés avec des recommandations">📊 Analyser des données</button>
                            <button class="quick-action-btn" data-prompt="Crée-moi un plan de projet détaillé avec timeline et ressources pour [nom du projet]">📋 Planifier un projet</button>
                            <button class="quick-action-btn" data-prompt="Écris-moi du code JavaScript/Python pour [fonctionnalité] avec commentaires">💻 Générer du code</button>
                            <button class="quick-action-btn" data-prompt="Traduis ce texte en [langue] en gardant le ton professionnel">🌍 Traduire</button>
                            <button class="quick-action-btn" data-prompt="Résume ce document/article en points clés avec les informations essentielles">📄 Résumer</button>
                            <button class="quick-action-btn" data-prompt="Génère des idées créatives pour [projet/campagne] avec des concepts innovants">💡 Brainstorming</button>
                            <button class="quick-action-btn" data-prompt="Explique-moi [concept] de manière simple avec des exemples concrets">🎓 Expliquer</button>
                        </div>
                    </div>
                </div>

                <div class="ai-input-area">
                    <div class="ai-input-container">
                        <textarea id="ai-input" placeholder="Tapez votre message... (Shift+Entrée pour nouvelle ligne)" rows="1"></textarea>
                        <div class="ai-input-actions">
                            <button class="ai-input-btn" id="ai-attach-btn" title="Joindre un fichier">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M16.5,6V17.5A4,4 0 0,1 12.5,21.5A4,4 0 0,1 8.5,17.5V5A2.5,2.5 0 0,1 11,2.5A2.5,2.5 0 0,1 13.5,5V15.5A1,1 0 0,1 12.5,16.5A1,1 0 0,1 11.5,15.5V6H10V15.5A2.5,2.5 0 0,0 12.5,18A2.5,2.5 0 0,0 15,15.5V5A4,4 0 0,0 11,1A4,4 0 0,0 7,5V17.5A5.5,5.5 0 0,0 12.5,23A5.5,5.5 0 0,0 18,17.5V6H16.5Z"/>
                                </svg>
                            </button>
                            <button class="ai-input-btn" id="ai-send-btn" title="Envoyer">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="ai-usage-info" id="ai-usage-info">
                        <span class="usage-text">Plan Gratuit: <span id="usage-count">5</span>/15 messages aujourd'hui</span>
                        <button class="upgrade-link" id="upgrade-link">Passer au Premium</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Support Chat (Assistant) -->
    <div class="support-chat" id="support-chat">
        <div class="support-header">
            <div class="support-avatar">💬</div>
            <div class="support-info">
                <div class="support-name">Support Nephiris</div>
                <div class="support-status">En ligne</div>
            </div>
            <button class="support-close" id="support-close">×</button>
        </div>

        <div class="support-messages" id="support-messages">
            <div class="message bot-message">
                <div class="message-avatar">💬</div>
                <div class="message-content">
                    Bonjour ! Je suis ici pour vous aider avec toutes vos questions sur Nephiris AI. Comment puis-je vous assister ?
                </div>
            </div>
        </div>

        <div class="support-input">
            <input type="text" id="support-input" placeholder="Posez votre question..." />
            <button id="support-send">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                </svg>
            </button>
        </div>
    </div>

    <!-- Support Toggle -->
    <button class="support-toggle" id="support-toggle">
        <span class="support-toggle-icon">💬</span>
        <span class="support-notification" id="support-notification" style="display: none;">1</span>
    </button>

    <script src="script.js"></script>
</body>
</html>
