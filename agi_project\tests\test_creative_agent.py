import pytest
import asyncio
from unittest.mock import AsyncMock, patch

from agi_project.agent.creative_agent import CreativeAgent
from agi_project.agent.multi_agent_system import Task

@pytest.mark.asyncio
class TestCreativeAgent:

    @pytest.fixture
    def creative_agent(self):
        return CreativeAgent(agent_id="creative1")

    @pytest.fixture
    def valid_image_task(self):
        return Task(id="task_img_1", description="image:Des montagnes enneigées")

    @pytest.fixture
    def invalid_task_format(self):
        # Missing prompt after colon
        return Task(id="task_invalid_1", description="image:")

    @pytest.fixture
    def unknown_type_task(self):
        # The type "animation" is not supported
        return Task(id="task_unknown_1", description="animation:montagnes enneigées")

    @pytest.fixture
    def multiple_tasks(self):
        # Example of two tasks for performance testing
        return [
            Task(id="task1", description="image:un chat"),
            Task(id="task2", description="video:Une présentation sur l’IA")
        ]

    @patch("agi_project.modules.generation.image_generator.ImageGenerator.generate", new_callable=AsyncMock)
    @patch("agi_project.modules.generation.video_script_generator.VideoScriptGenerator.generate", new_callable=AsyncMock)
    @patch("agi_project.modules.generation.voice_generator.VoiceGenerator.generate", new_callable=AsyncMock)
    @patch("agi_project.modules.generation.music_generator.MusicGenerator.generate", new_callable=AsyncMock)
    async def test_valid_image_task(
        self, mock_music_gen, mock_voice_gen, mock_video_gen, mock_image_gen, creative_agent, valid_image_task
    ):
        mock_image_gen.return_value = "image_data"
        result = await creative_agent.execute_task(valid_image_task)

        assert result["status"] == "completed"
        assert "image_data" in result["result"]
        assert creative_agent.get_generated_content(valid_image_task.id) == result["result"]

    @pytest.mark.parametrize("description", [
        "image:",
        "video:",
        "",
        "voice"
    ])
    async def test_invalid_task_format(self, creative_agent, description):
        task = Task(id="bad_format", description=description)
        result = await creative_agent.execute_task(task)

        assert result["status"] == "failed"
        assert "Format" in result["error"] or "Type de contenu non supporté" in result["error"]

    async def test_unknown_content_type(self, creative_agent, unknown_type_task):
        result = await creative_agent.execute_task(unknown_type_task)
        assert result["status"] == "failed"
        assert "non supporté" in result["error"]

    @patch("agi_project.modules.generation.image_generator.ImageGenerator.generate", new_callable=AsyncMock)
    @patch("agi_project.modules.generation.video_script_generator.VideoScriptGenerator.generate", new_callable=AsyncMock)
    async def test_multiple_tasks_performance(self, mock_video_gen, mock_image_gen, creative_agent, multiple_tasks):
        """
        Teste la gestion de plusieurs tâches simultanées.
        """
        mock_image_gen.return_value = "img_data"
        mock_video_gen.return_value = "video_data"

        results = await asyncio.gather(*(creative_agent.execute_task(t) for t in multiple_tasks))

        assert len(results) == 2
        for r in results:
            assert r["status"] == "completed"

    def test_security_prompt(self, creative_agent):
        """
        Exemples de tests de sécurité ou sandboxing.
        Ici on vérifie simplement qu'aucune exception n'est levée
        pour un prompt potentiellement malveillant.
        """
        malicious_task = Task(id="security_test", description="voice:; rm -rf / --simulate-attack")
        # L'appel asynchrone serait patché IRL
        # On s'assure juste que le code ne s’exécute pas dangereusement
        result = asyncio.run(creative_agent.execute_task(malicious_task))
        assert result["status"] in ["failed", "completed"]
        # Dans un vrai test de sécu, on vérifierait qu'aucune commande n'a été exécutée
