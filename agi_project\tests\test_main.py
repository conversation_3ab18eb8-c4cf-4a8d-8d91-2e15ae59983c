"""
Comprehensive tests for main.py (FastAPI application).
Ensures the "/" and "/chat" endpoints behave correctly, including edge cases.
"""

import pytest
from fastapi.testclient import TestClient
from agi_project.main import app

client = TestClient(app)

def test_root_endpoint():
    """
    Test the GET '/' endpoint to ensure it returns expected status and JSON.
    """
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "Bienvenue dans le projet AGI" in data["message"]

def test_chat_endpoint_no_api_key(monkeypatch):
    """
    Test the POST '/chat' endpoint when the OPENAI_API_KEY is missing or empty.
    """
    monkeypatch.delenv("OPENAI_API_KEY", raising=False)
    response = client.post("/chat", json={"message": "Hello"})
    assert response.status_code == 200
    data = response.json()
    assert "Erreur : la clé API OpenAI n'est pas configurée." in data["response"]

def test_chat_endpoint_valid_api_key(monkeypatch):
    """
    Test the '/chat' endpoint with a mock valid API key and mock the query_openai function.
    """
    # Provide a dummy API key
    monkeypatch.setenv("OPENAI_API_KEY", "test_key")

    # We mock the query_openai function in main to avoid real HTTP calls
    # Using patch from unittest.mock
    from unittest.mock import patch

    with patch("agi_project.main.query_openai", return_value="Mocked response") as mock_query:
        response = client.post("/chat", json={"message": "Hello"})
        assert response.status_code == 200
        data = response.json()
        assert data["response"] == "Mocked response"
        mock_query.assert_called_once_with("Hello")

def test_chat_endpoint_error_response(monkeypatch):
    """
    Test the '/chat' endpoint and handle an Exception from query_openai.
    """
    # Provide a dummy API key
    monkeypatch.setenv("OPENAI_API_KEY", "test_key")

    from unittest.mock import patch

    with patch("agi_project.main.query_openai", side_effect=Exception("Simulated error")):
        response = client.post("/chat", json={"message": "Hello"})
        assert response.status_code == 200
        data = response.json()
        assert "Erreur lors de la requête à l'API OpenAI : Simulated error" in data["response"]
