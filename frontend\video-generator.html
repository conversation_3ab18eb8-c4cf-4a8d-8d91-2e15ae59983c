<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur Vidéo Nephiris AI</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0f0f23;
            color: white;
            font-family: 'Inter', sans-serif;
        }

        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
        }

        .controls button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }

        .video-canvas {
            width: 1920px;
            height: 1080px;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            position: relative;
            overflow: hidden;
            margin: 0 auto;
            transform: scale(0.5);
            transform-origin: top center;
        }

        .scene {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 1s ease-in-out;
        }

        .scene.active {
            opacity: 1;
        }

        .text-content {
            text-align: center;
            z-index: 10;
        }

        .main-title {
            font-size: 120px;
            font-weight: 700;
            background: linear-gradient(135deg, #6366f1, #8b5cf6, #06b6d4);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 40px;
            animation: fadeInUp 1.5s ease;
        }

        .subtitle {
            font-size: 60px;
            color: #ffffff;
            margin-bottom: 30px;
            animation: fadeInUp 1.5s ease 0.5s both;
        }

        .description {
            font-size: 40px;
            color: #a1a1aa;
            max-width: 1200px;
            line-height: 1.4;
            animation: fadeInUp 1.5s ease 1s both;
        }

        .logo {
            font-size: 200px;
            margin-bottom: 60px;
            animation: logoAppear 2s ease;
        }

        .tagline {
            font-size: 50px;
            color: #8b5cf6;
            font-style: italic;
            animation: fadeInUp 1.5s ease 1.5s both;
        }

        /* Effets de fond */
        .bg-effects {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #6366f1;
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        .glow-orb {
            position: absolute;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(99, 102, 241, 0.3) 0%, transparent 70%);
            animation: pulse 4s infinite ease-in-out;
        }

        .neural-network {
            position: absolute;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 20% 30%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(99, 102, 241, 0.05) 0%, transparent 50%);
            animation: networkPulse 8s infinite ease-in-out;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes logoAppear {
            0% {
                opacity: 0;
                transform: scale(0.5) rotate(-10deg);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.1) rotate(5deg);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.3;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.6;
            }
        }

        @keyframes networkPulse {
            0%, 100% {
                opacity: 0.3;
            }
            50% {
                opacity: 0.7;
            }
        }

        .progress-bar {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 300px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #6366f1, #8b5cf6);
            border-radius: 2px;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="controls">
        <button onclick="startVideo()">▶️ Démarrer</button>
        <button onclick="pauseVideo()">⏸️ Pause</button>
        <button onclick="resetVideo()">🔄 Reset</button>
        <button onclick="exportVideo()">💾 Exporter</button>
        <div>Durée: <span id="timer">0s</span></div>
    </div>

    <div class="progress-bar">
        <div class="progress-fill" id="progress"></div>
    </div>

    <div class="video-canvas" id="canvas">
        <!-- Effets de fond -->
        <div class="bg-effects">
            <div class="neural-network"></div>
            <div class="glow-orb" style="top: 20%; left: 10%;"></div>
            <div class="glow-orb" style="top: 60%; right: 15%; animation-delay: 2s;"></div>
            <div class="glow-orb" style="bottom: 30%; left: 70%; animation-delay: 4s;"></div>
        </div>

        <!-- Scène 1: Bienvenue dans l'avenir -->
        <div class="scene active" id="scene1">
            <div class="text-content">
                <div class="main-title">Bienvenue dans l'avenir.</div>
            </div>
        </div>

        <!-- Scène 2: Logo Nephiris AI -->
        <div class="scene" id="scene2">
            <div class="text-content">
                <div class="logo">🔮</div>
                <div class="subtitle">Voici Nephiris AI</div>
                <div class="description">votre nouvelle intelligence artificielle personnelle.</div>
            </div>
        </div>

        <!-- Scène 3: Caractéristiques -->
        <div class="scene" id="scene3">
            <div class="text-content">
                <div class="main-title">Puissante. Intelligente. Visionnaire.</div>
            </div>
        </div>

        <!-- Scène 4: Mission -->
        <div class="scene" id="scene4">
            <div class="text-content">
                <div class="description">Pensée pour vous aider, apprendre avec vous… et évoluer avec vous.</div>
            </div>
        </div>

        <!-- Scène 5: Call to action -->
        <div class="scene" id="scene5">
            <div class="text-content">
                <div class="subtitle">Rejoignez l'expérience Nephiris.</div>
            </div>
        </div>

        <!-- Scène 6: Logo final -->
        <div class="scene" id="scene6">
            <div class="text-content">
                <div class="logo">🔮</div>
                <div class="subtitle">Nephiris AI</div>
                <div class="tagline">"L'intelligence qui vous comprend."</div>
            </div>
        </div>
    </div>

    <script>
        let currentScene = 0;
        let isPlaying = false;
        let startTime = 0;
        let animationId;
        
        const scenes = document.querySelectorAll('.scene');
        const totalDuration = 30000; // 30 secondes
        const sceneDuration = totalDuration / scenes.length;

        // Créer des particules flottantes
        function createParticles() {
            const bgEffects = document.querySelector('.bg-effects');
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (4 + Math.random() * 4) + 's';
                bgEffects.appendChild(particle);
            }
        }

        function startVideo() {
            if (!isPlaying) {
                isPlaying = true;
                startTime = Date.now();
                playVideo();
            }
        }

        function pauseVideo() {
            isPlaying = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
        }

        function resetVideo() {
            pauseVideo();
            currentScene = 0;
            scenes.forEach((scene, index) => {
                scene.classList.toggle('active', index === 0);
            });
            document.getElementById('timer').textContent = '0s';
            document.getElementById('progress').style.width = '0%';
        }

        function playVideo() {
            if (!isPlaying) return;

            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / totalDuration, 1);
            
            // Mettre à jour la barre de progression
            document.getElementById('progress').style.width = (progress * 100) + '%';
            document.getElementById('timer').textContent = Math.floor(elapsed / 1000) + 's';

            // Changer de scène
            const newScene = Math.floor(elapsed / sceneDuration);
            if (newScene !== currentScene && newScene < scenes.length) {
                scenes[currentScene].classList.remove('active');
                currentScene = newScene;
                scenes[currentScene].classList.add('active');
            }

            // Continuer ou arrêter
            if (progress < 1) {
                animationId = requestAnimationFrame(playVideo);
            } else {
                isPlaying = false;
                alert('Vidéo terminée ! Durée: 30 secondes');
            }
        }

        function exportVideo() {
            alert('Pour exporter la vidéo:\n\n1. Utilisez un logiciel de capture d\'écran comme OBS Studio\n2. Enregistrez la zone du canvas pendant 30 secondes\n3. Exportez en MP4 1920x1080\n\nOu utilisez l\'API MediaRecorder pour capturer automatiquement.');
        }

        // Initialiser
        createParticles();
        
        // Auto-start après 2 secondes
        setTimeout(() => {
            startVideo();
        }, 2000);
    </script>
</body>
</html>
