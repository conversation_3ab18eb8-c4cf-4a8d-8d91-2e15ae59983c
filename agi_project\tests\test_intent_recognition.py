import unittest
from agi_project.modules.nlp.intent_recognition import IntentRecognizer

class TestIntentRecognition(unittest.TestCase):
    def setUp(self):
        self.recognizer = IntentRecognizer()

    def test_recognize_intent_question(self):
        text = "Quel est le temps aujourd'hui ?"
        result = self.recognizer.detect_intent(text)
        self.assertEqual(result["intent"], "weather_query")
        self.assertGreater(result["confidence"], 0.7)

    def test_recognize_intent_command(self):
        text = "Allume la lumière dans le salon"
        result = self.recognizer.detect_intent(text)
        self.assertEqual(result["intent"], "device_control")
        self.assertGreater(result["confidence"], 0.7)

    def test_unknown_intent(self):
        text = "asdfghjkl"
        result = self.recognizer.detect_intent(text)
        self.assertEqual(result["intent"], "unknown")
        self.assertLess(result["confidence"], 0.3)

    def test_empty_input(self):
        with self.assertRaises(ValueError):
            self.recognizer.detect_intent("")

    def test_multilingual_intent(self):
        text = "What's the weather today?"
        result = self.recognizer.detect_intent(text)
        self.assertEqual(result["intent"], "weather_query")
        self.assertGreater(result["confidence"], 0.7)

if __name__ == '__main__':
    unittest.main()
