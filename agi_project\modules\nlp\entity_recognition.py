"""
Module : entity_recognition.py

Ce module gère la reconnaissance d’entités nommées (NER) dans un texte,
permettant d’identifier des personnes, lieux, organisations, dates, etc.

Fonctionnalités :
- Extraction d’entités basique par motifs regex ou listes de mots.
- Classification des entités en catégories standards.
- Retourne une liste d’entités avec leur type et position dans le texte.
- Extensible pour intégrer des modèles NLP avancés.

Exemple d’utilisation :
entities = EntityRecognition.extract_entities("Barack Obama est né à Honolulu en 1961.")

Approche simplifiée :
- Utilisation de regex pour détecter les entités courantes.
- Pas d’apprentissage automatique dans cette version.
"""

import re
from typing import List, Dict, Tuple

class EntityRecognition:
    ENTITY_PATTERNS = {
        "PERSON": r"\b([A-Z][a-z]+(?:\s[A-Z][a-z]+)*)\b",
        "DATE": r"\b(\d{4}|\d{1,2}/\d{1,2}/\d{2,4})\b",
        "LOCATION": r"\b(Paris|Londres|New York|Honolulu|Tokyo)\b",
        "ORGANIZATION": r"\b(ONU|Google|Microsoft|Apple|NASA)\b"
    }

    @staticmethod
    def extract_entities(text: str) -> List[Dict[str, Tuple[int, int]]]:
        """
        Extrait les entités nommées du texte.
        Retourne une liste de dictionnaires : { "type": (start_pos, end_pos) }
        """
        entities = []
        for ent_type, pattern in EntityRecognition.ENTITY_PATTERNS.items():
            for match in re.finditer(pattern, text):
                entities.append({
                    "type": ent_type,
                    "entity": match.group(),
                    "start": match.start(),
                    "end": match.end()
                })
        return entities
