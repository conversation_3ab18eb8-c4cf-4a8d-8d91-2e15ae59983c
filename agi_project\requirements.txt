# Core Application Dependencies
fastapi==0.95.2  # Downgraded for better compatibility
uvicorn==0.27.0
python-dotenv==1.0.1
openai==1.12.0
httpx==0.26.0

# Web Scraping & Processing
aiohttp==3.9.3
beautifulsoup4==4.12.3
wikipedia==1.4.0

# Data Processing & Storage
numpy==1.26.4
pandas==2.2.1
tqdm==4.66.2
ray==2.10.0
faiss-cpu==1.7.4

# NLP Processing
spacy==3.7.4
nltk==3.8.1
spacy-transformers==1.2.4

# Security & Authentication
python-jose==3.3.0
passlib==1.7.4
bcrypt==4.1.2

# Monitoring & Logging
prometheus-client==0.20.0
python-json-logger==2.0.7
loguru==0.7.2

# Testing
pytest==8.0.2
pytest-asyncio==0.23.5
pytest-cov==4.1.0
pytest-mock==3.12.0

# Development & Code Quality
black==24.3.0
flake8==7.0.0
isort==5.13.2
mypy==1.8.0

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# Optional ML (commented out)
# torch==2.2.1
# transformers==4.38.2
# scikit-learn==1.4.1.post1
