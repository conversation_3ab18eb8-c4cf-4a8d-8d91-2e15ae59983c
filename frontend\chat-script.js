class NephirisChat {
    constructor() {
        this.conversations = JSON.parse(localStorage.getItem('nephiris_conversations') || '[]');
        this.currentConversationId = null;
        this.isTyping = false;
        this.messageCount = parseInt(localStorage.getItem('nephiris_message_count') || '0');
        this.maxFreeMessages = 15;
        this.userPlan = localStorage.getItem('nephiris_user_plan') || 'free';
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupTheme();
        this.updateUI();
        this.loadConversations();
        this.updateUsageInfo();
        
        // Auto-resize textarea
        this.setupTextareaAutoResize();
        
        // Charger la dernière conversation ou afficher l'écran d'accueil
        if (this.conversations.length > 0) {
            this.loadConversation(this.conversations[0].id);
        } else {
            this.showWelcomeScreen();
        }
    }

    setupEventListeners() {
        // Boutons principaux
        document.getElementById('newChatBtn').addEventListener('click', () => this.startNewConversation());
        document.getElementById('sendBtn').addEventListener('click', () => this.sendMessage());
        document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());
        document.getElementById('sidebarToggle').addEventListener('click', () => this.toggleSidebar());
        
        // Input de message
        const messageInput = document.getElementById('messageInput');
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        messageInput.addEventListener('input', () => {
            this.updateCharCount();
            this.updateSendButton();
        });

        // Cartes de suggestion
        document.querySelectorAll('.suggestion-card').forEach(card => {
            card.addEventListener('click', () => {
                const prompt = card.dataset.prompt;
                messageInput.value = prompt;
                this.updateCharCount();
                this.updateSendButton();
                this.sendMessage();
            });
        });

        // Modal
        document.getElementById('modalClose').addEventListener('click', () => this.closeModal());
        document.getElementById('modalOverlay').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) this.closeModal();
        });

        // Boutons d'action
        document.getElementById('upgradeBtn').addEventListener('click', () => this.showUpgradeModal());
        document.getElementById('settingsBtn').addEventListener('click', () => this.showSettingsModal());
        document.getElementById('shareBtn').addEventListener('click', () => this.shareConversation());
    }

    setupTextareaAutoResize() {
        const textarea = document.getElementById('messageInput');
        textarea.addEventListener('input', () => {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
        });
    }

    setupTheme() {
        const savedTheme = localStorage.getItem('nephiris_theme') || 'dark';
        document.documentElement.setAttribute('data-theme', savedTheme);
        this.updateThemeIcon(savedTheme);
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('nephiris_theme', newTheme);
        this.updateThemeIcon(newTheme);
        
        this.showNotification('Thème changé', `Thème ${newTheme === 'dark' ? 'sombre' : 'clair'} activé`);
    }

    updateThemeIcon(theme) {
        const themeToggle = document.getElementById('themeToggle');
        const icon = theme === 'dark' 
            ? '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="5"/><path d="M12 1v2m0 18v2M4.22 4.22l1.42 1.42m12.72 12.72l1.42 1.42M1 12h2m18 0h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/></svg>'
            : '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/></svg>';
        themeToggle.innerHTML = icon;
    }

    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        sidebar.classList.toggle('open');
    }

    startNewConversation() {
        const conversationId = this.generateId();
        const conversation = {
            id: conversationId,
            title: 'Nouvelle conversation',
            messages: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        this.conversations.unshift(conversation);
        this.saveConversations();
        this.loadConversations();
        this.loadConversation(conversationId);
        this.hideWelcomeScreen();
        
        // Focus sur l'input
        document.getElementById('messageInput').focus();
    }

    async sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const content = messageInput.value.trim();
        
        if (!content || this.isTyping) return;
        
        // Vérifier la limite de messages gratuits
        if (this.userPlan === 'free' && this.messageCount >= this.maxFreeMessages) {
            this.showUpgradeModal();
            return;
        }

        // Créer une conversation si nécessaire
        if (!this.currentConversationId) {
            this.startNewConversation();
        }

        // Ajouter le message utilisateur
        this.addMessage('user', content);
        messageInput.value = '';
        this.updateCharCount();
        this.updateSendButton();
        this.hideWelcomeScreen();

        // Incrémenter le compteur de messages
        this.messageCount++;
        localStorage.setItem('nephiris_message_count', this.messageCount.toString());
        this.updateUsageInfo();

        // Afficher l'indicateur de frappe
        this.showTypingIndicator();

        try {
            // Simuler l'appel API (remplacer par vraie API)
            const response = await this.callNephirisAPI(content);
            this.hideTypingIndicator();
            this.addMessage('ai', response);
        } catch (error) {
            this.hideTypingIndicator();
            this.addMessage('ai', 'Désolé, une erreur s\'est produite. Veuillez réessayer.');
            this.showNotification('Erreur', 'Impossible de contacter Nephiris AI', 'error');
        }

        // Mettre à jour le titre de la conversation
        this.updateConversationTitle();
    }

    async callNephirisAPI(message) {
        // Simulation d'appel API - remplacer par vraie intégration
        return new Promise((resolve) => {
            setTimeout(() => {
                const responses = [
                    "Je comprends votre question. Voici une réponse détaillée qui prend en compte tous les aspects de votre demande...",
                    "Excellente question ! Laissez-moi vous expliquer cela de manière claire et structurée...",
                    "Voici ce que je peux vous dire à ce sujet. Il y a plusieurs points importants à considérer...",
                    "Je vais vous aider avec cela. Voici une approche étape par étape pour résoudre votre problème...",
                    "C'est un sujet fascinant ! Permettez-moi de vous donner une explication complète..."
                ];
                
                const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                resolve(randomResponse);
            }, 1500 + Math.random() * 2000); // Délai réaliste
        });
    }

    addMessage(type, content) {
        const conversation = this.getCurrentConversation();
        if (!conversation) return;

        const message = {
            id: this.generateId(),
            type,
            content,
            timestamp: new Date().toISOString()
        };

        conversation.messages.push(message);
        conversation.updatedAt = new Date().toISOString();
        this.saveConversations();
        this.renderMessage(message);
        this.scrollToBottom();
    }

    renderMessage(message) {
        const messagesContainer = document.getElementById('messages');
        const messageElement = document.createElement('div');
        messageElement.className = `message ${message.type}`;
        messageElement.dataset.messageId = message.id;

        const avatar = message.type === 'user' ? 'U' : '🔮';
        const author = message.type === 'user' ? 'Vous' : 'Nephiris AI';
        const time = this.formatTime(message.timestamp);

        messageElement.innerHTML = `
            <div class="message-header">
                <div class="message-avatar">${avatar}</div>
                <div class="message-author">${author}</div>
                <div class="message-time">${time}</div>
            </div>
            <div class="message-content">${this.formatMessageContent(message.content)}</div>
            <div class="message-actions">
                <button class="message-action-btn copy-message" title="Copier">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                    </svg>
                </button>
                ${message.type === 'ai' ? `
                    <button class="message-action-btn like-message" title="J'aime">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"/>
                        </svg>
                    </button>
                    <button class="message-action-btn dislike-message" title="Je n'aime pas">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17"/>
                        </svg>
                    </button>
                ` : ''}
            </div>
        `;

        messagesContainer.appendChild(messageElement);

        // Ajouter les event listeners pour les actions
        this.setupMessageActions(messageElement);
    }

    setupMessageActions(messageElement) {
        const copyBtn = messageElement.querySelector('.copy-message');
        const likeBtn = messageElement.querySelector('.like-message');
        const dislikeBtn = messageElement.querySelector('.dislike-message');

        copyBtn?.addEventListener('click', () => {
            const content = messageElement.querySelector('.message-content').textContent;
            navigator.clipboard.writeText(content).then(() => {
                this.showNotification('Copié', 'Message copié dans le presse-papiers');
            });
        });

        likeBtn?.addEventListener('click', () => {
            likeBtn.style.color = 'var(--success)';
            this.showNotification('Merci !', 'Votre feedback nous aide à améliorer Nephiris AI');
        });

        dislikeBtn?.addEventListener('click', () => {
            dislikeBtn.style.color = 'var(--error)';
            this.showNotification('Feedback reçu', 'Nous prenons note de votre retour');
        });
    }

    formatMessageContent(content) {
        // Formatage basique du contenu (markdown simple)
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    showTypingIndicator() {
        this.isTyping = true;
        const messagesContainer = document.getElementById('messages');
        
        const typingElement = document.createElement('div');
        typingElement.className = 'typing-indicator';
        typingElement.id = 'typingIndicator';
        
        typingElement.innerHTML = `
            <div class="message-header">
                <div class="message-avatar">🔮</div>
                <div class="message-author">Nephiris AI</div>
            </div>
            <div class="typing-content">
                <span>est en train d'écrire</span>
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        `;
        
        messagesContainer.appendChild(typingElement);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        this.isTyping = false;
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    showWelcomeScreen() {
        document.getElementById('welcomeScreen').style.display = 'flex';
        document.getElementById('messages').style.display = 'none';
    }

    hideWelcomeScreen() {
        document.getElementById('welcomeScreen').style.display = 'none';
        document.getElementById('messages').style.display = 'block';
    }

    updateCharCount() {
        const messageInput = document.getElementById('messageInput');
        const charCount = document.getElementById('charCount');
        charCount.textContent = messageInput.value.length;
    }

    updateSendButton() {
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        sendBtn.disabled = !messageInput.value.trim() || this.isTyping;
    }

    updateUsageInfo() {
        const usageCount = document.getElementById('usageCount');
        const remaining = Math.max(0, this.maxFreeMessages - this.messageCount);
        usageCount.textContent = remaining;
        
        if (remaining <= 3) {
            usageCount.style.color = 'var(--warning)';
        }
        if (remaining === 0) {
            usageCount.style.color = 'var(--error)';
        }
    }

    scrollToBottom() {
        const messagesContainer = document.getElementById('messagesContainer');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('fr-FR', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    }

    getCurrentConversation() {
        return this.conversations.find(conv => conv.id === this.currentConversationId);
    }

    saveConversations() {
        localStorage.setItem('nephiris_conversations', JSON.stringify(this.conversations));
    }

    updateUI() {
        // Mettre à jour l'interface utilisateur
        this.loadConversations();
        this.updateUsageInfo();
    }

    loadConversations() {
        const conversationsList = document.getElementById('conversationsList');
        conversationsList.innerHTML = '';

        this.conversations.forEach(conversation => {
            const conversationElement = document.createElement('div');
            conversationElement.className = 'conversation-item';
            conversationElement.dataset.conversationId = conversation.id;

            if (conversation.id === this.currentConversationId) {
                conversationElement.classList.add('active');
            }

            const lastMessage = conversation.messages[conversation.messages.length - 1];
            const preview = lastMessage ?
                (lastMessage.content.length > 50 ? lastMessage.content.substring(0, 50) + '...' : lastMessage.content) :
                'Nouvelle conversation';

            conversationElement.innerHTML = `
                <div class="conversation-title">${conversation.title}</div>
                <div class="conversation-preview">${preview}</div>
                <div class="conversation-time">${this.formatRelativeTime(conversation.updatedAt)}</div>
            `;

            conversationElement.addEventListener('click', () => {
                this.loadConversation(conversation.id);
            });

            conversationsList.appendChild(conversationElement);
        });
    }

    loadConversation(conversationId) {
        this.currentConversationId = conversationId;
        const conversation = this.getCurrentConversation();

        if (!conversation) return;

        // Mettre à jour l'interface
        this.updateActiveConversation();
        this.renderMessages(conversation.messages);

        if (conversation.messages.length === 0) {
            this.showWelcomeScreen();
        } else {
            this.hideWelcomeScreen();
        }
    }

    updateActiveConversation() {
        document.querySelectorAll('.conversation-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeItem = document.querySelector(`[data-conversation-id="${this.currentConversationId}"]`);
        if (activeItem) {
            activeItem.classList.add('active');
        }
    }

    renderMessages(messages) {
        const messagesContainer = document.getElementById('messages');
        messagesContainer.innerHTML = '';

        messages.forEach(message => {
            this.renderMessage(message);
        });

        this.scrollToBottom();
    }

    updateConversationTitle() {
        const conversation = this.getCurrentConversation();
        if (!conversation || conversation.messages.length === 0) return;

        const firstUserMessage = conversation.messages.find(msg => msg.type === 'user');
        if (firstUserMessage && conversation.title === 'Nouvelle conversation') {
            conversation.title = firstUserMessage.content.length > 30 ?
                firstUserMessage.content.substring(0, 30) + '...' :
                firstUserMessage.content;

            this.saveConversations();
            this.loadConversations();
        }
    }

    formatRelativeTime(timestamp) {
        const now = new Date();
        const date = new Date(timestamp);
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'À l\'instant';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
        if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}j`;

        return date.toLocaleDateString('fr-FR', {
            day: 'numeric',
            month: 'short'
        });
    }

    showNotification(title, message, type = 'info') {
        const notificationsContainer = document.getElementById('notifications');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        notification.innerHTML = `
            <div class="notification-header">
                <div class="notification-title">${title}</div>
                <button class="notification-close">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="notification-content">${message}</div>
        `;

        notificationsContainer.appendChild(notification);

        // Auto-remove après 5 secondes
        setTimeout(() => {
            notification.remove();
        }, 5000);

        // Bouton de fermeture
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });
    }

    showModal(title, content) {
        const modal = document.getElementById('modal');
        const modalOverlay = document.getElementById('modalOverlay');
        const modalTitle = document.getElementById('modalTitle');
        const modalContent = document.getElementById('modalContent');

        modalTitle.textContent = title;
        modalContent.innerHTML = content;
        modalOverlay.classList.add('show');
    }

    closeModal() {
        const modalOverlay = document.getElementById('modalOverlay');
        modalOverlay.classList.remove('show');
    }

    showUpgradeModal() {
        const content = `
            <div style="text-align: center;">
                <h4 style="margin-bottom: 1rem; color: var(--text-primary);">Passez au Premium</h4>
                <p style="margin-bottom: 2rem; color: var(--text-secondary);">
                    Débloquez toutes les fonctionnalités de Nephiris AI
                </p>

                <div style="background: var(--bg-card); border-radius: var(--radius-lg); padding: 1.5rem; margin-bottom: 2rem;">
                    <h5 style="color: var(--primary); margin-bottom: 1rem;">Plan Premium - 19.99$ CAD/mois</h5>
                    <ul style="text-align: left; color: var(--text-secondary); line-height: 1.8;">
                        <li>✨ Messages illimités</li>
                        <li>🚀 Réponses prioritaires</li>
                        <li>🧠 Modèles IA avancés</li>
                        <li>📁 Historique illimité</li>
                        <li>🎨 Génération d'images</li>
                        <li>📊 Analytics détaillées</li>
                        <li>🛡️ Support prioritaire</li>
                    </ul>
                </div>

                <button style="background: var(--primary); color: white; border: none; padding: 0.75rem 2rem; border-radius: var(--radius-lg); font-weight: 600; cursor: pointer; margin-right: 1rem;">
                    Passer au Premium
                </button>
                <button style="background: none; border: 1px solid var(--border-primary); color: var(--text-primary); padding: 0.75rem 2rem; border-radius: var(--radius-lg); cursor: pointer;">
                    Plus tard
                </button>
            </div>
        `;

        this.showModal('Upgrade vers Premium', content);
    }

    showSettingsModal() {
        const content = `
            <div>
                <h4 style="margin-bottom: 1.5rem; color: var(--text-primary);">Paramètres</h4>

                <div style="margin-bottom: 2rem;">
                    <h5 style="margin-bottom: 1rem; color: var(--text-primary);">Apparence</h5>
                    <label style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary); cursor: pointer;">
                        <input type="checkbox" id="autoTheme" style="margin: 0;">
                        Thème automatique selon l'heure
                    </label>
                </div>

                <div style="margin-bottom: 2rem;">
                    <h5 style="margin-bottom: 1rem; color: var(--text-primary);">Notifications</h5>
                    <label style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary); cursor: pointer; margin-bottom: 0.5rem;">
                        <input type="checkbox" checked style="margin: 0;">
                        Notifications de réponse
                    </label>
                    <label style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-secondary); cursor: pointer;">
                        <input type="checkbox" style="margin: 0;">
                        Sons de notification
                    </label>
                </div>

                <div style="margin-bottom: 2rem;">
                    <h5 style="margin-bottom: 1rem; color: var(--text-primary);">Données</h5>
                    <button style="background: var(--error); color: white; border: none; padding: 0.5rem 1rem; border-radius: var(--radius-md); cursor: pointer; margin-right: 1rem;">
                        Effacer l'historique
                    </button>
                    <button style="background: none; border: 1px solid var(--border-primary); color: var(--text-primary); padding: 0.5rem 1rem; border-radius: var(--radius-md); cursor: pointer;">
                        Exporter les données
                    </button>
                </div>
            </div>
        `;

        this.showModal('Paramètres', content);
    }

    shareConversation() {
        const conversation = this.getCurrentConversation();
        if (!conversation || conversation.messages.length === 0) {
            this.showNotification('Erreur', 'Aucune conversation à partager', 'error');
            return;
        }

        // Simuler le partage
        const shareUrl = `https://nephiris.ai/share/${conversation.id}`;
        navigator.clipboard.writeText(shareUrl).then(() => {
            this.showNotification('Partagé', 'Lien de partage copié dans le presse-papiers');
        });
    }
}

// Initialiser l'application quand le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
    new NephirisChat();
});
