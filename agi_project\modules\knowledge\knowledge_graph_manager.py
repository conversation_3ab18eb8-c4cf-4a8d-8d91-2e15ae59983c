"""
Module : knowledge_graph_manager.py

Ce module gère la création et la manipulation d’un graphe de connaissances
(chaînages entre entités, attributs, relations), permettant à l’AGI de disposer
d’un stockage sémantique.

Fonctionnalités clés :
- Ajout ou mise à jour d’entités et de relations.
- Gestion de différents types de relations (hiérarchiques, associatives).
- Support de requêtes simples (ex. trouver les entités reliées à X).
- Export/import au format JSON ou RDF (placeholder).
- Mécanismes basiques de mise à jour incrémentale (ajout, suppression).

Exemple d’utilisation :
1. Créer un KnowledgeGraphManager
2. Ajouter des entités (films, auteurs, etc.) et des relations (“a réalisé”, “est né en”)
3. Interroger le graphe pour en déduire des liens ou chaînages

Approche simplifiée :
Ce module n’effectue pas de traitement sémantique lourd. Il sert de structure
de données évolutive pour de futurs usages.
"""

import json
import uuid
from typing import Dict, Any, List

class KnowledgeGraphManager:
    def __init__(self):
        """
        Initialise le graphe de connaissances, stocké dans self.graph, 
        qui contient un dictionary { entity_id: { 'name': ..., 'relations': [...] } }
        """
        self.graph: Dict[str, Dict[str, Any]] = {}

    def add_entity(self, name: str, attributes: Dict[str, Any] = None) -> str:
        """
        Ajoute une entité au graphe, avec un nom et éventuellement d’autres attributs.
        Retourne l’ID unique de l’entité.
        """
        entity_id = str(uuid.uuid4())
        self.graph[entity_id] = {
            "name": name,
            "attributes": attributes or {},
            "relations": []
        }
        return entity_id

    def add_relation(self, source_id: str, target_id: str, relation_type: str):
        """
        Crée une relation entre deux entités existantes.
        Exemple : 'source_id' (a réalisé) -> 'target_id'
        """
        if source_id not in self.graph or target_id not in self.graph:
            return False

        self.graph[source_id]["relations"].append({
            "target": target_id,
            "type": relation_type
        })
        return True

    def find_entities_by_name(self, name: str) -> List[str]:
        """
        Retourne la liste des IDs d’entités correspondant à un certain nom.
        """
        return [
            eid for eid, data in self.graph.items()
            if data["name"].lower() == name.lower()
        ]

    def query_relations(self, entity_id: str) -> List[Dict[str, Any]]:
        """
        Retourne la liste des relations sortantes de l’entité donnée.
        """
        if entity_id not in self.graph:
            return []
        return self.graph[entity_id]["relations"]

    def remove_entity(self, entity_id: str):
        """
        Supprime une entité et toutes les relations associées.
        """
        if entity_id in self.graph:
            del self.graph[entity_id]
            # Supprimer les relations pointant vers cette entité
            for eid, data in self.graph.items():
                new_rel = [r for r in data["relations"] if r["target"] != entity_id]
                data["relations"] = new_rel

    def export_graph_json(self) -> str:
        """
        Exporte l’intégralité du graphe au format JSON.
        """
        return json.dumps(self.graph, indent=2, ensure_ascii=False)
