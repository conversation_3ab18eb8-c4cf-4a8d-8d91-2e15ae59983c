"""
Module : music_generator.py

Ce module gère la génération de musiques ou séquences musicales à partir de descriptions textuelles
ou de paramètres musicaux (tempo, tonalité, style, instruments). Il s'agit d'un placeholder offrant
une simulation du contenu musical binaire.

Fonctionnalités clés :
- Spécification de style (classique, jazz, électro, rock, etc.).
- Paramètres possibles : tempo (bpm), tonalité (ex. Do majeur), durée approximative.
- Génération d'un flux binaire simulé (base64) représentant un fichier audio.
- Commandes de base pour inclure ou omettre certains instruments.
- Extension possible pour s'intégrer à un moteur IA dédié (magenta, jukebox, etc.).

Exemple d’utilisation :
music_data = MusicGenerator.generate_music(
    style="jazz",
    tempo=120,
    key="C major",
    duration=30,
    instruments=["piano", "saxophone"]
)

"""

import base64
import random
from typing import Dict, Any, List
from agi_project.modules.nlp.sentiment_analysis import SentimentAnalysis


class MusicGenerator:
    @staticmethod
    def generate_music(
        style: str = "classique",
        tempo: int = 120,
        key: str = "C major",
        duration: int = 30,
        instruments: List[str] = None,
        context_text: str = ""
    ) -> Dict[str, Any]:

        """
        Génère une séquence musicale simulée.

        Paramètres :
        - style : style musical (classique, jazz, rock, électro...)
        - tempo : vitesse en bpm
        - key : tonalité, ex. "C major"
        - duration : durée approximative en secondes
        - instruments : liste d'instruments à mettre en avant

        Retourne un dictionnaire contenant :
        {
          "style": ...,
          "tempo": ...,
          "key": ...,
          "duration": ...,
          "instruments": ...,
          "audio_data_base64": ... (contenu audio simulé),
          "info": ...
        }
        """
        if instruments is None:
            instruments = ["piano"]

        # Ajustement du style selon le sentiment
        sentiment_result = SentimentAnalysis.analyze_sentiment(context_text)
        if sentiment_result["sentiment"] == "positif":
            style += "_joyful"
        elif sentiment_result["sentiment"] == "négatif":
            style += "_dark"

        # Simulation d'un flux binaire audio
        audio_data = MusicGenerator._simulate_audio_data(duration)

        info_message = (
            f"Musique simulée générée. Style={style}, Tempo={tempo} bpm, "
            f"Tonalité={key}, Durée={duration}s, Instruments={instruments}"
        )

        return {
            "style": style,
            "tempo": tempo,
            "key": key,
            "duration": duration,
            "instruments": instruments,
            "audio_data_base64": audio_data,
            "info": info_message
        }

    @staticmethod
    def _simulate_audio_data(duration: int) -> str:
        """
        Génére un contenu binaire audio simulé de taille proportionnelle à 'duration',
        encodé en base64.
        """
        size = duration * 1000  # simple règle pour varier la taille
        dummy_bytes = bytes([random.randint(0, 255) for _ in range(size)])
        encoded = base64.b64encode(dummy_bytes).decode('utf-8')
        return encoded
