import unittest
from agi_project.modules.nlp.sentiment_analysis import SentimentAnalyzer

class TestSentimentAnalysis(unittest.TestCase):
    def setUp(self):
        self.analyzer = SentimentAnalyzer()

    def test_analyze_positive_sentiment(self):
        text = "Je suis très heureux aujourd'hui !"
        result = self.analyzer.analyze(text)
        self.assertEqual(result['sentiment'], 'positive')
        self.assertGreater(result['score'], 0.5)

    def test_analyze_negative_sentiment(self):
        text = "Je déteste cette situation."
        result = self.analyzer.analyze(text)
        self.assertEqual(result['sentiment'], 'negative')
        self.assertLess(result['score'], -0.5)

    def test_analyze_neutral_sentiment(self):
        text = "C'est un fait objectif."
        result = self.analyzer.analyze(text)
        self.assertEqual(result['sentiment'], 'neutral')
        self.assertAlmostEqual(result['score'], 0, delta=0.2)

    def test_analyze_empty_text(self):
        with self.assertRaises(ValueError):
            self.analyzer.analyze("")

    def test_analyze_non_string_input(self):
        with self.assertRaises(TypeError):
            self.analyzer.analyze(123)

if __name__ == '__main__':
    unittest.main()
