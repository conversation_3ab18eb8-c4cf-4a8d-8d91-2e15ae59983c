{
  // Disable the built-in CSS validation for rules like @tailwind and @apply.
  // This prevents the "Unknown at rule" errors in the VSCode editor.
  "css.validate": false,
  
  // Enable Tailwind CSS IntelliSense extension features if installed.
  "tailwindCSS.includeLanguages": {
    "plaintext": "html"
  },
  "editor.quickSuggestions": {
    "strings": true
  },
  
  // (Optional) If you'd like to use the Tailwind CSS IntelliSense plugin:
  // "tailwindCSS.experimental.classRegex": [
  //   "tw`([^`]*)",
  //   "tw=\\\"([^\\\"]*)",
  //   "tw={'([^'}]*)"
  // ],
  // "tailwindCSS.emmetCompletions": true
}
