:root {
    /* Colors */
    --primary: #6366f1;
    --primary-dark: #4f46e5;
    --secondary: #8b5cf6;
    --accent: #06b6d4;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;

    /* Backgrounds */
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-card: rgba(255, 255, 255, 0.03);
    --bg-card-hover: rgba(255, 255, 255, 0.06);

    /* Text */
    --text-primary: #ffffff;
    --text-secondary: #a1a1aa;
    --text-tertiary: #71717a;

    /* Borders */
    --border-primary: rgba(255, 255, 255, 0.1);
    --border-secondary: rgba(255, 255, 255, 0.05);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
    --gradient-accent: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;

    /* Border radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Typography */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-family-display: 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Thème clair */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-card: rgba(0, 0, 0, 0.03);
    --bg-card-hover: rgba(0, 0, 0, 0.06);

    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #64748b;

    --border-primary: rgba(0, 0, 0, 0.1);
    --border-secondary: rgba(0, 0, 0, 0.05);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    cursor: default;
    transition: background-color 0.3s ease, color 0.3s ease;
}



.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

/* Buttons */
.btn-primary,
.btn-secondary,
.btn-ghost {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius-lg);
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-primary);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
}

.btn-secondary:hover {
    background: var(--bg-card-hover);
    border-color: var(--border-primary);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid transparent;
}

.btn-ghost:hover {
    color: var(--text-primary);
    background: var(--bg-card);
}

.btn-large {
    padding: var(--space-md) var(--space-xl);
    font-size: 1rem;
    border-radius: var(--radius-xl);
}

/* Header & Navigation */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(15, 15, 35, 0.8);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-secondary);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-md) var(--space-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-family: var(--font-family-display);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    text-decoration: none;
}

.logo-icon {
    font-size: 1.75rem;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: var(--space-xl);
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.nav-link:hover {
    color: var(--text-primary);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

/* Theme Toggle */
.theme-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.theme-toggle:hover {
    background: var(--bg-card-hover);
    color: var(--text-primary);
    transform: rotate(180deg);
}

.theme-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

/* Bouton Google */
.btn-google {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    background: white;
    color: #1f2937;
    border: 1px solid #d1d5db;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.btn-google:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-google-large {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-md);
    background: white;
    color: #1f2937;
    border: 1px solid #d1d5db;
    padding: var(--space-lg) var(--space-xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    width: 100%;
}

.btn-google-large:hover {
    background: #f9fafb;
    border-color: #6b7280;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-google-modern {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-md);
    background: white;
    color: #1f2937;
    border: 1px solid #e5e7eb;
    padding: var(--space-lg) var(--space-2xl);
    border-radius: var(--radius-xl);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    width: 100%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.btn-google-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.btn-google-modern:hover::before {
    left: 100%;
}

.btn-google-modern:hover {
    background: #f8fafc;
    border-color: #d1d5db;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-google-modern:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.google-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
}

.btn-google-modern span {
    font-weight: 500;
    letter-spacing: 0.025em;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    padding-top: 80px;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
    z-index: -1;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3xl);
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.hero-text {
    max-width: 500px;
}

.hero h1 {
    font-family: var(--font-family-display);
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--space-lg);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
    font-weight: 500;
}

.hero-description {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: var(--space-2xl);
    line-height: 1.7;
}

.hero-actions {
    display: flex;
    gap: var(--space-md);
    flex-wrap: wrap;
}

.hero-video {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Vidéo simple */
.hero-video {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-video video {
    width: 100%;
    max-width: 600px;
    height: auto;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-primary);
}

/* Nouvelle visualisation Nephiris */
.hero-visual {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 2rem;
}

.nephiris-showcase {
    position: relative;
    width: 100%;
    max-width: 600px;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 50%, rgba(6, 182, 212, 0.05) 100%);
    border-radius: 20px;
    border: 1px solid rgba(99, 102, 241, 0.2);
    backdrop-filter: blur(10px);
    overflow: hidden;
    transition: all 0.3s ease;
}

.nephiris-showcase:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(99, 102, 241, 0.2);
    border-color: rgba(99, 102, 241, 0.4);
}

.nephiris-main-visual {
    width: 100%;
    height: 100%;
    filter: drop-shadow(0 10px 30px rgba(99, 102, 241, 0.3));
}

.visual-overlay {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.visual-overlay:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
}

.feature-highlight {
    display: flex;
    gap: 2rem;
    justify-content: space-around;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
    transition: all 0.3s ease;
}

.highlight-item:hover {
    transform: scale(1.05);
}

.highlight-icon {
    font-size: 2rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    transition: all 0.3s ease;
}

.highlight-icon:hover {
    transform: scale(1.2);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.4));
}

.highlight-text h4 {
    font-family: var(--font-family-display);
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.highlight-text p {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
}

/* Animations pour le réseau neuronal */
.neural-network {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translate(250px, 200px) translateY(0px); }
    50% { transform: translate(250px, 200px) translateY(-10px); }
}

.secondary-nodes circle {
    transition: all 0.3s ease;
    cursor: pointer;
}

.secondary-nodes circle:hover {
    transform: scale(1.2);
    filter: brightness(1.3);
}

.connections path {
    transition: all 0.3s ease;
}

.floating-particles circle {
    animation: sparkle 4s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
}

/* Particules interactives */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary);
    border-radius: 50%;
    opacity: 0.6;
    animation: float 8s infinite linear;
}

@keyframes float {
    0% {
        transform: translateY(100vh) translateX(0) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(100px) rotate(360deg);
        opacity: 0;
    }
}

/* Anciens styles vidéo supprimés - remplacés par la vidéo promotionnelle intégrée */

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--space-3xl);
}

.section-header h2 {
    font-family: var(--font-family-display);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: var(--space-md);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.section-header p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Section Statistiques */
.stats {
    padding: var(--space-3xl) 0;
    background: var(--bg-primary);
    position: relative;
    overflow: hidden;
}

.stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(99, 102, 241, 0.05) 0%, transparent 70%);
    z-index: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-xl);
    position: relative;
    z-index: 1;
}

.stat-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-2xl);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-8px);
    background: var(--bg-card-hover);
    box-shadow: var(--shadow-xl);
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-icon {
    font-size: 3rem;
    margin-bottom: var(--space-lg);
    display: block;
}

.stat-number {
    font-family: var(--font-family-display);
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
    display: inline-block;
}

.stat-unit {
    font-size: 1.5rem;
    color: var(--primary);
    font-weight: 600;
    margin-left: var(--space-xs);
}

.stat-label {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-top: var(--space-md);
    font-weight: 500;
}

/* Features Section */
.features {
    padding: var(--space-3xl) 0;
    background: linear-gradient(to bottom, var(--bg-primary), var(--bg-secondary));
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-xl);
}

.feature-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-4px);
    background: var(--bg-card-hover);
    border-color: var(--border-primary);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: var(--space-lg);
    display: block;
}

.feature-card h3 {
    font-family: var(--font-family-display);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--space-md);
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
}

.feature-btn {
    background: transparent;
    border: 1px solid var(--border-primary);
    color: var(--text-secondary);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.feature-btn:hover {
    background: var(--bg-card);
    color: var(--text-primary);
    border-color: var(--primary);
}

/* Pricing Section */
.pricing {
    padding: var(--space-3xl) 0;
    background: var(--bg-secondary);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-xl);
    align-items: start;
}

.price-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-2xl);
    position: relative;
    transition: all 0.3s ease;
}

.price-card:hover {
    transform: translateY(-4px);
    border-color: var(--primary);
    box-shadow: var(--shadow-xl);
}

.price-card.featured {
    border-color: var(--primary);
    background: var(--bg-card-hover);
    transform: scale(1.05);
}

.price-card.featured:hover {
    transform: scale(1.05) translateY(-4px);
}

.popular-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    color: var(--text-primary);
    padding: var(--space-xs) var(--space-md);
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.plan-header {
    text-align: center;
    margin-bottom: var(--space-xl);
}

.plan-header h3 {
    font-family: var(--font-family-display);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--space-sm);
    color: var(--text-primary);
}

.plan-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: var(--space-xl);
}

.currency {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-right: var(--space-xs);
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-primary);
}

.period {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-left: var(--space-xs);
}

.features-list {
    list-style: none;
    margin-bottom: var(--space-xl);
}

.features-list li {
    padding: var(--space-sm) 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
}

.features-list li::before {
    content: '✓';
    color: var(--success);
    font-weight: bold;
    margin-right: var(--space-sm);
}

.price-button {
    width: 100%;
    background: var(--gradient-primary);
    border: none;
    padding: var(--space-md);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.price-button:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.price-card:not(.featured) .price-button {
    background: transparent;
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.price-card:not(.featured) .price-button:hover {
    background: var(--bg-card);
    border-color: var(--primary);
}

/* Footer */
.main-footer {
    background: var(--bg-tertiary);
    border-top: 1px solid var(--border-secondary);
    padding: var(--space-3xl) 0 var(--space-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--space-3xl);
    margin-bottom: var(--space-2xl);
}

.footer-brand {
    max-width: 300px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-family: var(--font-family-display);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

.footer-tagline {
    color: var(--text-secondary);
    margin-bottom: var(--space-lg);
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: var(--space-md);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.2s ease;
}

.social-link:hover {
    background: var(--bg-card-hover);
    color: var(--text-primary);
    border-color: var(--primary);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-xl);
}

.footer-column h4 {
    font-family: var(--font-family-display);
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

.footer-column a {
    display: block;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    margin-bottom: var(--space-sm);
    transition: color 0.2s ease;
}

.footer-column a:hover {
    color: var(--text-primary);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-xl);
    border-top: 1px solid var(--border-secondary);
    font-size: 0.875rem;
    color: var(--text-tertiary);
}

.footer-bottom-links {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 0.75rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--success);
    border-radius: 50%;
    animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Chatbot */
.chatbot-toggle {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: var(--shadow-xl);
    z-index: 1500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.chatbot-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 20px 40px rgba(99, 102, 241, 0.3);
}

.chatbot-notification {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--error);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.chatbot-container {
    position: fixed;
    bottom: 100px;
    right: 30px;
    width: 350px;
    height: 500px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    z-index: 1500;
    display: none;
    flex-direction: column;
    overflow: hidden;
    animation: slideInUp 0.3s ease;
}

.chatbot-container.show {
    display: flex;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.chatbot-header {
    padding: var(--space-lg);
    background: var(--bg-card);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.chatbot-avatar {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.chatbot-info {
    flex: 1;
}

.chatbot-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.chatbot-status {
    font-size: 0.75rem;
    color: var(--success);
}

.chatbot-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.chatbot-close:hover {
    background: var(--bg-card);
    color: var(--text-primary);
}

.chatbot-messages {
    flex: 1;
    padding: var(--space-lg);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.message {
    display: flex;
    gap: var(--space-sm);
    align-items: flex-start;
}

.message.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: var(--gradient-primary);
}

.user-message .message-avatar {
    background: var(--bg-card);
    color: var(--text-primary);
}

.message-content {
    background: var(--bg-card);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-lg);
    max-width: 80%;
    font-size: 0.875rem;
    line-height: 1.4;
    color: var(--text-primary);
}

.user-message .message-content {
    background: var(--gradient-primary);
    color: white;
}

.chatbot-input {
    padding: var(--space-lg);
    border-top: 1px solid var(--border-primary);
    display: flex;
    gap: var(--space-sm);
}

.chatbot-input input {
    flex: 1;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-sm) var(--space-md);
    color: var(--text-primary);
    font-size: 0.875rem;
}

.chatbot-input input:focus {
    outline: none;
    border-color: var(--primary);
}

.chatbot-input button {
    background: var(--gradient-primary);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--space-sm);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.chatbot-input button:hover {
    transform: scale(1.05);
}

/* Interface IA Principale */
.ai-interface {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    z-index: 2000;
    display: none;
    flex-direction: column;
}

.ai-interface.show {
    display: flex;
}

.ai-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-lg) var(--space-xl);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
}

.ai-title {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.ai-logo {
    font-size: 2rem;
}

.ai-info h3 {
    font-family: var(--font-family-display);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.ai-status {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.ai-controls {
    display: flex;
    gap: var(--space-sm);
}

.ai-control-btn {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--space-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-control-btn:hover {
    background: var(--bg-card-hover);
    color: var(--text-primary);
    border-color: var(--primary);
}

/* Zone de connexion */
.ai-login-section {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-2xl);
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
}

.ai-login-content {
    text-align: center;
    max-width: 450px;
    width: 100%;
    padding: 2rem;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(99, 102, 241, 0.2);
    box-shadow: 0 20px 40px rgba(99, 102, 241, 0.1);
    transition: transform 0.3s ease;
}

.ai-login-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(99, 102, 241, 0.15);
}

.ai-login-visual {
    margin-bottom: 2rem;
    display: flex;
    justify-content: center;
}

.nephiris-avatar {
    filter: drop-shadow(0 10px 20px rgba(99, 102, 241, 0.3));
    transition: transform 0.3s ease;
}

.nephiris-avatar:hover {
    transform: scale(1.05);
}

.ai-login-icon {
    font-size: 4rem;
    margin-bottom: var(--space-lg);
}

.ai-login-content h3 {
    font-family: var(--font-family-display);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

.ai-login-content p {
    color: var(--text-secondary);
    margin-bottom: var(--space-xl);
    line-height: 1.6;
}

.ai-login-features {
    margin-top: var(--space-xl);
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.feature-item {
    color: var(--text-secondary);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
}

/* Interface principale IA */
.ai-main-interface {
    flex: 1;
    display: flex;
    height: calc(100vh - 80px);
}

.ai-sidebar {
    width: 300px;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
}

.ai-user-info {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.user-plan {
    color: var(--text-secondary);
    font-size: 0.75rem;
}

.upgrade-btn {
    background: var(--gradient-primary);
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-xs) var(--space-sm);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    transition: all 0.2s ease;
}

.upgrade-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.ai-conversations {
    flex: 1;
    padding: var(--space-lg);
    overflow-y: auto;
}

.conversations-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-lg);
}

.conversations-header h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.new-conversation-btn {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--space-xs);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.new-conversation-btn:hover {
    background: var(--bg-card-hover);
    color: var(--text-primary);
    border-color: var(--primary);
}

.conversations-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.conversation-item {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--space-md);
    cursor: pointer;
    transition: all 0.2s ease;
}

.conversation-item:hover {
    background: var(--bg-card-hover);
    border-color: var(--primary);
}

.conversation-item.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.conversation-title {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: var(--space-xs);
}

.conversation-preview {
    font-size: 0.75rem;
    opacity: 0.7;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.conversation-time {
    font-size: 0.7rem;
    opacity: 0.5;
    margin-top: var(--space-xs);
}

/* Zone de chat IA */
.ai-chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.ai-messages {
    flex: 1;
    padding: var(--space-xl);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.welcome-message {
    text-align: center;
    max-width: 600px;
    margin: auto;
    padding: var(--space-2xl);
}

.welcome-avatar {
    display: flex;
    justify-content: center;
    margin-bottom: var(--space-lg);
}

.nephiris-mini-avatar {
    filter: drop-shadow(0 5px 15px rgba(99, 102, 241, 0.4));
    transition: transform 0.3s ease;
}

.nephiris-mini-avatar:hover {
    transform: scale(1.1);
}

.welcome-icon {
    font-size: 4rem;
    margin-bottom: var(--space-lg);
}

.welcome-message h3 {
    font-family: var(--font-family-display);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

.welcome-message p {
    color: var(--text-secondary);
    margin-bottom: var(--space-xl);
    line-height: 1.6;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
    margin-top: var(--space-xl);
}

.quick-action-btn {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-md);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    font-size: 0.875rem;
}

.quick-action-btn:hover {
    background: var(--bg-card-hover);
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.ai-message {
    display: flex;
    gap: var(--space-md);
    max-width: 80%;
}

.ai-message.user {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.ai-message.assistant {
    align-self: flex-start;
}

.ai-message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.ai-message.user .ai-message-avatar {
    background: var(--gradient-primary);
    color: white;
}

.ai-message.assistant .ai-message-avatar {
    background: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
}

.ai-message-content {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-md);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 0.875rem;
}

.ai-message.user .ai-message-content {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--primary);
}

.ai-message-time {
    font-size: 0.7rem;
    color: var(--text-tertiary);
    margin-top: var(--space-xs);
}

/* Zone de saisie IA */
.ai-input-area {
    padding: var(--space-xl);
    border-top: 1px solid var(--border-primary);
    background: var(--bg-secondary);
}

.ai-input-container {
    display: flex;
    align-items: flex-end;
    gap: var(--space-sm);
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-md);
    transition: border-color 0.2s ease;
}

.ai-input-container:focus-within {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.ai-input-container textarea {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 0.875rem;
    line-height: 1.5;
    resize: none;
    outline: none;
    min-height: 20px;
    max-height: 120px;
}

.ai-input-container textarea::placeholder {
    color: var(--text-secondary);
}

.ai-input-actions {
    display: flex;
    gap: var(--space-xs);
}

.ai-input-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-input-btn:hover {
    background: var(--bg-card-hover);
    color: var(--text-primary);
}

.ai-input-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.ai-usage-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--space-md);
    font-size: 0.75rem;
}

.usage-text {
    color: var(--text-secondary);
}

.upgrade-link {
    background: none;
    border: none;
    color: var(--primary);
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 500;
    text-decoration: underline;
}

.upgrade-link:hover {
    color: var(--primary-dark);
}

/* Support Chat (différent de l'IA) */
.support-chat {
    position: fixed;
    bottom: 100px;
    right: 30px;
    width: 350px;
    height: 500px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    z-index: 1500;
    display: none;
    flex-direction: column;
    overflow: hidden;
    animation: slideInUp 0.3s ease;
}

.support-chat.show {
    display: flex;
}

.support-header {
    padding: var(--space-lg);
    background: var(--bg-card);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.support-avatar {
    width: 40px;
    height: 40px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.support-info {
    flex: 1;
}

.support-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.support-status {
    font-size: 0.75rem;
    color: var(--success);
}

.support-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.support-close:hover {
    background: var(--bg-card);
    color: var(--text-primary);
}

.support-messages {
    flex: 1;
    padding: var(--space-lg);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.support-input {
    padding: var(--space-lg);
    border-top: 1px solid var(--border-primary);
    display: flex;
    gap: var(--space-sm);
}

.support-input input {
    flex: 1;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-sm) var(--space-md);
    color: var(--text-primary);
    font-size: 0.875rem;
}

.support-input input:focus {
    outline: none;
    border-color: var(--primary);
}

.support-input button {
    background: var(--gradient-secondary);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--space-sm);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.support-input button:hover {
    transform: scale(1.05);
}

.support-toggle {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: var(--gradient-secondary);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: var(--shadow-xl);
    z-index: 1500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.support-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
}

.support-notification {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--error);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    animation: bounce 2s infinite;
}

/* Styles Markdown - Style ChatGPT */
.md-paragraph {
    margin: 0 0 1rem 0;
    line-height: 1.6;
    color: var(--text-primary);
}

.md-h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 1.5rem 0 1rem 0;
    border-bottom: 1px solid var(--border-primary);
    padding-bottom: 0.5rem;
}

.md-h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 1.25rem 0 0.75rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.md-h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 1rem 0 0.5rem 0;
}

.md-bold {
    font-weight: 600;
    color: var(--text-primary);
}

.md-italic {
    font-style: italic;
    color: var(--text-secondary);
}

.md-code-inline {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 4px;
    padding: 2px 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    color: var(--text-primary);
}

.md-link {
    color: var(--primary);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.2s ease;
}

.md-link:hover {
    border-bottom-color: var(--primary);
}

.md-unordered-list,
.md-ordered-list {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.md-list-item {
    margin: 0.5rem 0;
    line-height: 1.5;
    color: var(--text-primary);
}

.md-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0.5rem 0;
    color: var(--text-primary);
}

.md-checkbox input {
    margin: 0;
}

/* Blocs de code style ChatGPT */
.md-code-block {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    margin: 1rem 0;
    overflow: hidden;
    font-family: 'Courier New', monospace;
}

.md-code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
}

.md-code-language {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.md-code-copy {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.md-code-copy:hover {
    background: var(--bg-card);
    color: var(--text-primary);
}

.md-code-content {
    padding: 1rem;
    margin: 0;
    background: none;
    border: none;
    font-size: 0.875rem;
    line-height: 1.5;
    overflow-x: auto;
    color: var(--text-primary);
}

.md-code-content code {
    background: none;
    padding: 0;
    border: none;
    font-family: inherit;
}

/* Syntax highlighting */
.keyword {
    color: #8b5cf6;
    font-weight: 600;
}

.literal {
    color: #06b6d4;
}

.number {
    color: #f59e0b;
}

.string {
    color: #10b981;
}

.comment {
    color: var(--text-tertiary);
    font-style: italic;
}

.property {
    color: #ef4444;
}

.tag {
    color: #8b5cf6;
}

.bracket {
    color: var(--text-primary);
    font-weight: 600;
}

/* Animation de typing */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    color: var(--text-secondary);
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 0.25rem;
}

.typing-dot {
    width: 6px;
    height: 6px;
    background: var(--text-secondary);
    border-radius: 50%;
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dot:nth-child(3) { animation-delay: 0s; }

@keyframes typingDot {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Message streaming effect */
.message-streaming {
    border-right: 2px solid var(--primary);
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { border-right-color: var(--primary); }
    51%, 100% { border-right-color: transparent; }
}

/* Benefits Section */
.benefits {
    padding: var(--space-2xl) 0;
    background: var(--bg-secondary);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-xl);
    margin-top: var(--space-2xl);
}

.benefit-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.benefit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.benefit-card:hover::before {
    transform: scaleX(1);
}

.benefit-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary);
}

.benefit-icon {
    font-size: 3rem;
    margin-bottom: var(--space-lg);
    display: block;
}

.benefit-card h3 {
    font-family: var(--font-family-display);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

.benefit-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
}

.benefit-metric {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    font-family: var(--font-family-display);
}

.metric-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

@media (max-width: 768px) {
    .benefits-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }

    .benefit-card {
        padding: var(--space-lg);
    }

    .benefit-icon {
        font-size: 2.5rem;
    }
}

/* Notifications */
.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
    max-width: 400px;
}

.notification {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    box-shadow: var(--shadow-xl);
    animation: slideInRight 0.3s ease;
    position: relative;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-primary);
}

.notification.success::before {
    background: var(--success);
}

.notification.warning::before {
    background: var(--warning);
}

.notification.error::before {
    background: var(--error);
}

.notification-header {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-sm);
}

.notification-icon {
    font-size: 1.2rem;
}

.notification-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.notification-close {
    margin-left: auto;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: var(--bg-card);
    color: var(--text-primary);
}

.notification-content {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
}

.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    background: var(--gradient-primary);
    animation: progress 5s linear;
}

@keyframes progress {
    from { width: 100%; }
    to { width: 0%; }
}

/* Effets sonores */
.sound-toggle {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 50%;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 1000;
}

.sound-toggle:hover {
    background: var(--bg-card-hover);
    color: var(--text-primary);
    transform: scale(1.1);
}

.sound-toggle.muted {
    opacity: 0.5;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
    position: relative;
    z-index: 2;
}

.loading-logo {
    font-size: 4rem;
    margin-bottom: var(--space-lg);
    animation: loadingPulse 2s infinite ease-in-out;
}

.loading-text {
    font-family: var(--font-family-display);
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
}

.loading-subtitle {
    color: var(--text-secondary);
    margin-bottom: var(--space-xl);
}

.loading-bar {
    width: 300px;
    height: 4px;
    background: var(--bg-card);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto var(--space-md);
}

.loading-progress {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 2px;
    width: 0%;
    transition: width 0.3s ease;
}

.loading-percentage {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.loading-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

@keyframes loadingPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Floating Action Menu */
.floating-menu {
    position: fixed;
    bottom: 120px;
    left: 30px;
    z-index: 1400;
}

.floating-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    background: var(--gradient-primary);
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-sm);
}

.floating-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

.main-floating-btn {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    background: var(--gradient-secondary);
}

.floating-options {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.floating-menu.open .floating-options {
    opacity: 1;
    transform: translateY(0);
    pointer-events: all;
}

.floating-menu.open .main-floating-btn {
    transform: rotate(45deg);
}

/* Search Overlay */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    z-index: 2500;
    display: none;
    align-items: flex-start;
    justify-content: center;
    padding-top: 10vh;
}

.search-overlay.show {
    display: flex;
}

.search-container {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    animation: searchSlideDown 0.3s ease;
}

@keyframes searchSlideDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-header {
    display: flex;
    align-items: center;
    padding: var(--space-lg);
    border-bottom: 1px solid var(--border-primary);
}

.search-header input {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1.125rem;
    outline: none;
}

.search-header input::placeholder {
    color: var(--text-secondary);
}

.search-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.search-close:hover {
    background: var(--bg-card);
    color: var(--text-primary);
}

.search-results {
    max-height: 400px;
    overflow-y: auto;
    padding: var(--space-lg);
}

.search-category h4 {
    color: var(--text-secondary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--space-md);
}

.search-item {
    padding: var(--space-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: var(--space-sm);
    color: var(--text-primary);
}

.search-item:hover {
    background: var(--bg-card);
    transform: translateX(4px);
}

/* Testimonials */
.testimonials {
    padding: var(--space-3xl) 0;
    background: var(--bg-secondary);
    position: relative;
    overflow: hidden;
}

.testimonials-carousel {
    position: relative;
    height: 300px;
    margin-bottom: var(--space-xl);
}

.testimonial-card {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-2xl);
    opacity: 0;
    transform: translateX(100px);
    transition: all 0.5s ease;
}

.testimonial-card.active {
    opacity: 1;
    transform: translateX(0);
}

.testimonial-content {
    margin-bottom: var(--space-xl);
}

.testimonial-stars {
    font-size: 1.2rem;
    margin-bottom: var(--space-md);
}

.testimonial-content p {
    font-size: 1.125rem;
    line-height: 1.6;
    color: var(--text-primary);
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.author-name {
    font-weight: 600;
    color: var(--text-primary);
}

.author-role {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.carousel-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-lg);
}

.carousel-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 1px solid var(--border-primary);
    background: var(--bg-card);
    color: var(--text-primary);
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.carousel-btn:hover {
    background: var(--bg-card-hover);
    border-color: var(--primary);
}

.carousel-dots {
    display: flex;
    gap: var(--space-sm);
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.dot.active {
    background: var(--primary);
    border-color: var(--primary);
}

/* Blog Section */
.blog {
    padding: var(--space-3xl) 0;
    background: var(--bg-primary);
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-xl);
    margin-bottom: var(--space-2xl);
}

.blog-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.blog-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary);
}

.blog-card.featured {
    grid-column: span 2;
}

.blog-image {
    position: relative;
    height: 200px;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.blog-placeholder {
    font-size: 3rem;
    opacity: 0.5;
}

.blog-category {
    position: absolute;
    top: var(--space-md);
    left: var(--space-md);
    background: var(--gradient-primary);
    color: white;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.blog-content {
    padding: var(--space-xl);
}

.blog-meta {
    display: flex;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.blog-content h3 {
    font-family: var(--font-family-display);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
    line-height: 1.3;
}

.blog-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
}

.blog-read-more {
    background: none;
    border: none;
    color: var(--primary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.blog-read-more:hover {
    color: var(--primary-dark);
    transform: translateX(4px);
}

.blog-actions {
    display: flex;
    gap: var(--space-md);
    justify-content: center;
    flex-wrap: wrap;
}

/* Cookie Consent */
.cookie-consent {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-primary);
    padding: var(--space-lg);
    z-index: 1600;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.cookie-consent.show {
    transform: translateY(0);
}

.cookie-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.cookie-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.cookie-text {
    flex: 1;
}

.cookie-text h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
}

.cookie-text p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
}

.cookie-actions {
    display: flex;
    gap: var(--space-sm);
    flex-shrink: 0;
}

/* Performance Monitor */
.performance-monitor {
    position: fixed;
    top: 20px;
    left: 20px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-md);
    z-index: 1300;
    min-width: 150px;
    opacity: 0.8;
    transition: all 0.3s ease;
    font-family: 'Courier New', monospace;
}

.performance-monitor:hover {
    opacity: 1;
    transform: scale(1.05);
}

.perf-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-sm);
    padding-bottom: var(--space-xs);
    border-bottom: 1px solid var(--border-primary);
}

.perf-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
}

.perf-toggle {
    background: none;
    border: none;
    font-size: 0.875rem;
    cursor: pointer;
    padding: 2px;
    border-radius: 2px;
    transition: background 0.2s ease;
}

.perf-toggle:hover {
    background: var(--bg-card);
}

.perf-metrics {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.perf-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
}

.perf-label {
    color: var(--text-secondary);
}

.perf-value {
    color: var(--success);
    font-weight: 600;
}

/* Easter Egg */
.easter-egg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(8px);
    z-index: 3000;
    display: none;
    align-items: center;
    justify-content: center;
}

.easter-egg.show {
    display: flex;
    animation: easterFadeIn 0.5s ease;
}

@keyframes easterFadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.easter-content {
    background: var(--bg-secondary);
    border: 2px solid var(--primary);
    border-radius: var(--radius-xl);
    padding: var(--space-3xl);
    text-align: center;
    max-width: 400px;
    position: relative;
    overflow: hidden;
}

.easter-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: easterShine 2s infinite;
}

@keyframes easterShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.easter-animation {
    font-size: 4rem;
    margin-bottom: var(--space-lg);
    animation: easterBounce 1s infinite ease-in-out;
}

@keyframes easterBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-20px); }
}

.easter-content h3 {
    font-family: var(--font-family-display);
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

.easter-content p {
    color: var(--text-secondary);
    margin-bottom: var(--space-xl);
    line-height: 1.6;
}

/* Animations globales */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes wiggle {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-3deg); }
    75% { transform: rotate(3deg); }
}

/* Responsive amélioré */
@media (max-width: 1024px) {
    .blog-card.featured {
        grid-column: span 1;
    }

    .floating-menu {
        bottom: 100px;
        left: 20px;
    }

    .performance-monitor {
        top: 10px;
        left: 10px;
        font-size: 0.75rem;
    }
}

@media (max-width: 768px) {
    .testimonials-carousel {
        height: 350px;
    }

    .testimonial-card {
        padding: var(--space-lg);
    }

    .blog-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }

    .cookie-content {
        flex-direction: column;
        text-align: center;
        gap: var(--space-md);
    }

    .cookie-actions {
        justify-content: center;
    }

    .search-container {
        width: 95%;
        margin: 0 auto;
    }

    .floating-menu {
        bottom: 80px;
        left: 15px;
    }

    .floating-btn {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }

    .main-floating-btn {
        width: 55px;
        height: 55px;
        font-size: 1.3rem;
    }
}

@media (max-width: 480px) {
    .easter-content {
        margin: var(--space-lg);
        padding: var(--space-xl);
    }

    .performance-monitor {
        position: relative;
        margin: var(--space-md);
        opacity: 1;
    }

    .testimonial-content p {
        font-size: 1rem;
    }

    .blog-content {
        padding: var(--space-lg);
    }
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    z-index: 2000;
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-2xl);
    max-width: 500px;
    width: 90%;
    position: relative;
    animation: slideUp 0.3s ease;
}

.modal-close {
    position: absolute;
    top: var(--space-md);
    right: var(--space-md);
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: color 0.2s ease;
}

.modal-close:hover {
    color: var(--text-primary);
}

.modal-body h3 {
    font-family: var(--font-family-display);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--space-md);
    color: var(--text-primary);
}

.modal-body p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--space-xl);
}

.modal-actions {
    display: flex;
    gap: var(--space-md);
    justify-content: flex-end;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--space-2xl);
        text-align: center;
    }

    .hero-text {
        max-width: none;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--space-2xl);
    }

    .footer-links {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: var(--space-md);
        padding: var(--space-md);
    }

    .nav-links {
        order: 1;
        gap: var(--space-lg);
    }

    .nav-actions {
        order: 2;
        flex-direction: column;
        width: 100%;
        gap: var(--space-sm);
    }

    .nav-actions .btn-primary,
    .nav-actions .btn-secondary {
        width: 100%;
        justify-content: center;
    }

    .hero {
        padding-top: 120px;
        min-height: auto;
        padding-bottom: var(--space-3xl);
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.125rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: stretch;
    }

    /* Vidéo responsive */
    .hero-video video {
        max-width: 100%;
        margin: 0 auto;
    }

    /* Visualisation responsive */
    .hero-visual {
        padding: 1rem;
    }

    .nephiris-showcase {
        height: 400px;
        max-width: 100%;
    }

    .feature-highlight {
        flex-direction: column;
        gap: 1rem;
    }

    .highlight-item {
        justify-content: center;
        text-align: center;
    }

    .highlight-icon {
        font-size: 1.5rem;
    }

    .highlight-text h4 {
        font-size: 0.8rem;
    }

    .highlight-text p {
        font-size: 0.7rem;
    }

    .visual-overlay {
        bottom: 10px;
        left: 10px;
        right: 10px;
        padding: 1rem;
    }

    .nephiris-main-visual {
        transform: scale(0.9);
    }

    .promo-main-title {
        font-size: 1.8rem;
    }

    /* Modales responsive */
    .community-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .job-details {
        flex-direction: column;
        gap: 0.5rem;
    }

    .cert-badges {
        justify-content: center;
    }
}

/* Styles pour les nouvelles modales */
.changelog-content {
    max-height: 400px;
    overflow-y: auto;
}

.changelog-item {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-primary);
}

.changelog-item:last-child {
    border-bottom: none;
}

.changelog-date {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.changelog-item h4 {
    color: var(--text-primary);
    margin-bottom: 0.75rem;
}

.changelog-item ul {
    list-style: none;
    padding: 0;
}

.changelog-item li {
    padding: 0.25rem 0;
    color: var(--text-secondary);
}

.docs-content, .blog-content, .community-content, .about-content, .careers-content, .contact-content, .press-content, .legal-content, .security-content {
    max-height: 500px;
    overflow-y: auto;
}

.docs-section, .blog-post, .about-section, .press-section, .legal-section, .security-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(99, 102, 241, 0.1);
}

.docs-section:last-child, .blog-post:last-child, .about-section:last-child, .press-section:last-child, .legal-section:last-child, .security-section:last-child {
    border-bottom: none;
}

.docs-link, .blog-link, .press-link {
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.docs-link:hover, .blog-link:hover, .press-link:hover {
    color: var(--primary-dark);
}

.blog-date, .press-date {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.community-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    justify-content: center;
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.community-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.community-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(99, 102, 241, 0.05);
    border-radius: 10px;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.community-link:hover {
    background: rgba(99, 102, 241, 0.1);
    transform: translateY(-2px);
}

.link-icon {
    font-size: 1.5rem;
}

.community-link h5 {
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
}

.community-link p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.job-listings {
    margin: 2rem 0;
}

.job-item {
    padding: 1.5rem;
    background: rgba(99, 102, 241, 0.05);
    border-radius: 10px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.job-item:hover {
    background: rgba(99, 102, 241, 0.1);
    transform: translateY(-2px);
}

.job-item h5 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
}

.job-details {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.job-location, .job-type {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.careers-cta {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(99, 102, 241, 0.1);
}

.contact-info {
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.contact-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.contact-item h5 {
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
}

.contact-item p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-form input, .contact-form select, .contact-form textarea {
    padding: 0.75rem;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-family: inherit;
}

.contact-form input:focus, .contact-form select:focus, .contact-form textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.legal-update {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(99, 102, 241, 0.1);
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-align: center;
}

.security-certifications {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(99, 102, 241, 0.1);
}

.cert-badges {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.cert-badge {
    padding: 0.5rem 1rem;
    background: var(--primary);
    color: white;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;

    .promo-subtitle {
        font-size: 1.4rem;
    }

    .promo-description {
        font-size: 1rem;
    }

    .promo-logo {
        font-size: 3rem;
    }

    .promo-controls {
        bottom: var(--space-sm);
        left: var(--space-sm);
        right: var(--space-sm);
        padding: var(--space-xs);
    }

    .promo-control-btn {
        font-size: 1rem;
    }

    .promo-timer {
        font-size: 0.7rem;
        min-width: 50px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }

    .price-card.featured {
        transform: none;
    }

    .price-card.featured:hover {
        transform: translateY(-4px);
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }

    .footer-bottom {
        flex-direction: column;
        gap: var(--space-md);
        text-align: center;
    }

    .modal-content {
        margin: var(--space-md);
        padding: var(--space-lg);
    }

    .modal-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--space-md);
    }

    .hero h1 {
        font-size: 2rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .feature-card,
    .price-card {
        padding: var(--space-lg);
    }
}
