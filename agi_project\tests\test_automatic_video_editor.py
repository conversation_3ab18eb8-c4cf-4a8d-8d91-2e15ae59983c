import unittest
from agi_project.modules.generation.automatic_video_editor import VideoEditor

class TestAutomaticVideoEditor(unittest.TestCase):
    def setUp(self):
        self.editor = VideoEditor()

    def test_edit_video_basic(self):
        input_path = "test_video.mp4"
        output_path = "edited_video.mp4"
        result = self.editor.edit(input_path, output_path)
        self.assertTrue(result["success"])
        self.assertEqual(result["output_path"], output_path)

    def test_invalid_input_path(self):
        with self.assertRaises(FileNotFoundError):
            self.editor.edit("nonexistent.mp4", "output.mp4")

    def test_trim_functionality(self):
        input_path = "test_video.mp4"
        output_path = "trimmed_video.mp4"
        result = self.editor.trim(input_path, output_path, start=10, end=30)
        self.assertTrue(result["success"])
        self.assertAlmostEqual(result["duration"], 20, delta=1)

    def test_effects_application(self):
        input_path = "test_video.mp4"
        output_path = "effect_video.mp4"
        effects = ["contrast", "saturation"]
        result = self.editor.apply_effects(input_path, output_path, effects=effects)
        self.assertTrue(result["success"])
        self.assertEqual(result["effects_applied"], effects)

    def test_transition_options(self):
        input_path = "test_video.mp4"
        output_path = "transition_video.mp4"
        transitions = ["fade", "slide"]
        result = self.editor.add_transitions(input_path, output_path, transitions=transitions)
        self.assertTrue(result["success"])
        self.assertEqual(result["transitions"], transitions)

if __name__ == '__main__':
    unittest.main()
