{"name": "nephiris-ai-chat", "version": "2.0.0", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "framer-motion": "^10.16.0", "react-icons": "^4.11.0", "date-fns": "^2.30.0", "react-markdown": "^9.0.0", "react-syntax-highlighter": "^15.5.0", "uuid": "^9.0.0", "axios": "^1.5.0", "react-hot-toast": "^2.4.1", "react-use-gesture": "^9.1.3", "react-spring": "^9.7.0", "web-vitals": "^3.4.0", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/forms": "^0.5.6"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "dev": "react-scripts start"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5"}, "proxy": "http://localhost:8000"}