"""
Module : article_generator.py

Ce module gère la génération d'articles longs, structurés et paramétrables.
Il s'appuie sur une logique de segmentation en sections (intro, corps, conclusion)
et peut inclure des sous-titres, des listes à puces, des citations, etc.

Fonctionnalités :
- Génération d'articles selon un sujet donné et une structure prédéfinie (ou dynamique)
- Personnalisation du nombre de sections, du style (formel, informel, etc.), de la longueur
- Possibilité d'insérer des mots-clés, citations et références
- Export dans différents formats (Markdown, HTML, texte brut)

Exemple d'utilisation :
- article = ArticleGenerator.generate_article(
    topic="Les avantages de l'IA",
    length="long",
    style="formel",
    sections=["Introduction", "Développements", "Cas d'usage", "Conclusion"])

Ensuite, on peut exporter l'article dans le format désiré.

"""

import random
import textwrap
import json
from typing import List, Dict, Any

class ArticleGenerator:
    @staticmethod
    def generate_article(
        topic: str,
        length: str = "moyen",
        style: str = "neutre",
        sections: List[str] = None,
        keywords: List[str] = None
    ) -> Dict[str, Any]:
        """
        Génère un article structuré basé sur les paramètres fournis.
        - topic : sujet principal
        - length : "court", "moyen", "long"
        - style : "formel", "informel", "neutre", "persuasif", etc.
        - sections : liste de sections souhaitées (si None, sections par défaut)
        - keywords : liste de mots-clés à inclure

        Retourne un dictionnaire contenant le titre, les sections et éventuellement
        les mots-clés insérés.
        """
        if not sections:
            sections = ["Introduction", "Développement", "Conclusion"]

        if not keywords:
            keywords = []

        # Mesure simpliste pour contrôler la longueur
        if length == "court":
            sentence_count = 3
        elif length == "long":
            sentence_count = 12
        else:
            sentence_count = 6

        # Génération de l'article
        title = f"Article sur {topic}"
        article_sections = []
        for section_name in sections:
            content = ArticleGenerator._generate_paragraph(section_name, sentence_count, style, keywords)
            article_sections.append({
                "section_title": section_name,
                "content": content
            })

        return {
            "title": title,
            "style": style,
            "topic": topic,
            "sections": article_sections,
            "keywords": keywords
        }

    @staticmethod
    def _generate_paragraph(
        section_name: str,
        sentence_count: int,
        style: str,
        keywords: List[str]
    ) -> str:
        """
        Génère un paragraphe simplifié pour la section,
        en insérant aléatoirement les mots-clés.
        """
        base_text = f"Dans la section '{section_name}', nous abordons le sujet avec un style {style}. "
        paragraphs = []
        for _ in range(sentence_count):
            sentence = f"Ceci est une phrase développant l'idée de la section {section_name}."
            # Insérer un mot clé au hasard
            if keywords and random.random() < 0.3:
                kw = random.choice(keywords)
                sentence += f" Le mot-clé '{kw}' est particulièrement significatif."
            paragraphs.append(sentence)
        content = " ".join(paragraphs)
        return base_text + content

    @staticmethod
    def export_article(article: Dict[str, Any], format_type: str = "json") -> str:
        """
        Exporte l'article dans le format spécifié.
        - "json" : retourne le JSON
        - "markdown" : retourne un format Markdown
        - "text" : retourne texte brut
        """
        if format_type == "json":
            return json.dumps(article, ensure_ascii=False, indent=2)
        elif format_type == "markdown":
            return ArticleGenerator._to_markdown(article)
        elif format_type == "text":
            return ArticleGenerator._to_text(article)
        else:
            raise ValueError("Format non supporté.")

    @staticmethod
    def _to_markdown(article: Dict[str, Any]) -> str:
        lines = [f"# {article['title']}"]
        lines.append(f"**Style**: {article['style']}")
        lines.append(f"**Sujet**: {article['topic']}\n")
        for sec in article["sections"]:
            lines.append(f"## {sec['section_title']}")
            lines.append(sec["content"] + "\n")
        if article["keywords"]:
            lines.append(f"Mots-clés : {', '.join(article['keywords'])}")
        return "\n".join(lines)

    @staticmethod
    def _to_text(article: Dict[str, Any]) -> str:
        lines = [f"Titre : {article['title']}"]
        lines.append(f"Style : {article['style']}")
        lines.append(f"Sujet : {article['topic']}\n")
        for sec in article["sections"]:
            lines.append(f"=== {sec['section_title']} ===")
            lines.append(sec["content"] + "\n")
        if article["keywords"]:
            lines.append(f"Mots-clés : {', '.join(article['keywords'])}")
        return "\n".join(lines)
