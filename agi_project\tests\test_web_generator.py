import unittest
import os
import shutil
import sys
from pathlib import Path

# Ajout du chemin du projet pour les imports
sys.path.insert(0, str(Path(__file__).parent.parent))
from agi_project.modules.code_generation.web_generator import WebGenerator


class TestWebGenerator(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.test_project_dir = os.path.join('test_projects')
        os.makedirs(cls.test_project_dir, exist_ok=True)
        
    def setUp(self):
        self.generator = WebGenerator()
        self.test_project = os.path.join(self.test_project_dir, 'test_react_app')
        
    def tearDown(self):
        if os.path.exists(self.test_project):
            shutil.rmtree(self.test_project)

    def test_create_react_project(self):
        config = {
            'project_name': 'test_react_app',
            'framework': 'react',
            'components': ['Header', 'Footer']
        }
        
        result = self.generator.create_project(config)
        
        # Vérifie que le projet a été créé
        self.assertTrue(os.path.exists(self.test_project))
        
        # Vérifie les fichiers de base
        self.assertTrue(os.path.exists(os.path.join(self.test_project, 'src', 'App.js')))
        self.assertTrue(os.path.exists(os.path.join(self.test_project, 'src', 'index.js')))
        
        # Vérifie les composants générés
        self.assertTrue(os.path.exists(os.path.join(self.test_project, 'src', 'components', 'Header.jsx')))
        self.assertTrue(os.path.exists(os.path.join(self.test_project, 'src', 'components', 'Footer.jsx')))
        
        # Vérifie la structure de retour
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['project_path'], self.test_project)
        
    def test_component_generation(self):
        config = {
            'project_name': 'test_react_app',
            'framework': 'react',
            'components': ['TestComponent']
        }
        
        self.generator.create_project(config)
        
        # Vérifie le contenu du composant généré
        with open(os.path.join(self.test_project, 'src', 'components', 'TestComponent.jsx')) as f:
            content = f.read()
            self.assertIn('function TestComponent()', content)
            self.assertIn('export default TestComponent', content)

if __name__ == '__main__':
    unittest.main()
