🔮 NEPHIRIS AI REACT CHAT - GUIDE DE DÉMARRAGE
================================================

📋 ÉTAPES SIMPLES :

1️⃣ OUVRIR UN TERMINAL
   - Appuyez sur Win + R
   - Ta<PERSON>z "cmd" et appuyez sur Entrée
   - OU clic droit dans le dossier react-chat → "Ouvrir dans le terminal"

2️⃣ NAVIGUER VERS LE DOSSIER
   cd "C:\Users\<USER>\Desktop\Nephiris AI\frontend\react-chat"

3️⃣ INSTALLER LES DÉPENDANCES (première fois seulement)
   npm install

4️⃣ DÉMARRER L'APPLICATION
   npm start

5️⃣ OUVRIR DANS LE NAVIGATEUR
   L'application s'ouvrira automatiquement à :
   http://127.0.0.1:5000

🎯 SI LE PORT 5000 NE FONCTIONNE PAS :
   L'application utilisera automatiquement le port suivant disponible
   Regardez dans le terminal pour voir l'URL exacte

⚡ FONCTIONNALITÉS AJOUTÉES :
   ✅ Reconnaissance vocale (bouton micro 🎤)
   ✅ Lecture audio des réponses (bouton haut-parleur 🔊)
   ✅ Mode focus plein écran (bouton agrandir 🖥️)
   ✅ Recherche dans l'historique (barre de recherche 🔍)
   ✅ Export des conversations (bouton télécharger 📊)
   ✅ Statistiques en temps réel (panneau stats 📈)
   ✅ Thème sombre/clair (bouton soleil/lune 🌙)
   ✅ Actions contextuelles sur les messages
   ✅ Support Markdown avec coloration syntaxique
   ✅ PWA installable comme application native

🛠️ EN CAS DE PROBLÈME :
   - Vérifiez que Node.js est installé : node --version
   - Supprimez node_modules et réinstallez : 
     rmdir /s node_modules
     npm install
   - Redémarrez le terminal

📱 INSTALLATION PWA :
   Une fois l'app ouverte dans Chrome/Edge :
   1. Cliquez sur l'icône d'installation dans la barre d'adresse
   2. Suivez les instructions
   3. L'app sera installée comme application native

🎨 PERSONNALISATION :
   - Modifiez les couleurs dans src/index.css
   - Changez le port dans le fichier .env
   - Ajoutez vos propres fonctionnalités

🚀 VERSION CRÉÉE : 2.1.0
   Interface React moderne avec toutes les fonctionnalités avancées !

📞 SUPPORT :
   Si ça ne fonctionne toujours pas, vérifiez :
   1. Node.js installé (https://nodejs.org/)
   2. Terminal ouvert dans le bon dossier
   3. Commandes tapées correctement
   4. Aucun antivirus bloquant npm

🔮 NEPHIRIS AI - L'INTERFACE LA PLUS AVANCÉE POUR VOTRE IA !
