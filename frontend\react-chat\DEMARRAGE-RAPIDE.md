# 🚀 Démarrage Rapide - Nephiris AI React Chat

## 📋 Étapes Simples

### 1️⃣ Ouvrir un Terminal
- **Windows** : Clic droit dans le dossier → "Ouvrir dans le terminal"
- **Mac/Linux** : Terminal → `cd chemin/vers/frontend/react-chat`

### 2️⃣ Naviguer vers le Dossier
```bash
cd frontend/react-chat
```

### 3️⃣ Installer les Dépendances (première fois)
```bash
npm install
```

### 4️⃣ Démarrer l'Application
```bash
npm start
```

### 5️⃣ Ouvrir dans le Navigateur
L'application s'ouvrira automatiquement à :
**http://127.0.0.1:5000**

---

## 🎯 Fonctionnalités à Tester

### ✅ Interface Moderne
- **Sidebar** avec historique des conversations
- **Thème sombre/clair** (bouton soleil/lune)
- **Animations fluides** Framer Motion

### ✅ Chat Avancé
- **Cartes de suggestion** pour démarrer
- **Support Markdown** dans les réponses
- **Actions contextuelles** (copier, modifier, supprimer)
- **Indicateurs de frappe** réalistes

### ✅ Fonctionnalités Uniques
- **🎤 Reconnaissance vocale** (bouton micro)
- **🔍 Recherche** dans l'historique
- **📊 Statistiques** en temps réel
- **🖥️ Mode focus** (plein écran)
- **💾 Export** des conversations
- **🔊 Lecture audio** des réponses IA

### ✅ Gestion des Conversations
- **Création** automatique de nouvelles conversations
- **Titres** générés automatiquement
- **Persistance** LocalStorage
- **Suppression** avec confirmation

---

## 🛠️ Dépannage Rapide

### Problème : "npm not found"
**Solution** : Installer Node.js depuis https://nodejs.org/

### Problème : Port occupé
**Solution** : L'app utilisera automatiquement le port suivant disponible

### Problème : Erreurs d'installation
**Solution** :
```bash
rm -rf node_modules package-lock.json
npm install
```

### Problème : Page blanche
**Solution** : Vérifier la console du navigateur (F12)

---

## 🎨 Personnalisation

### Changer le Port
Modifier le fichier `.env` :
```
PORT=5000
HOST=127.0.0.1
```

### Modifier les Couleurs
Éditer les variables CSS dans `src/index.css` :
```css
:root {
  --primary: #6366f1;
  --bg-primary: #0f0f23;
  /* ... */
}
```

---

## 📱 Installation PWA

1. **Ouvrir** l'app dans Chrome/Edge
2. **Cliquer** sur l'icône d'installation (barre d'adresse)
3. **Suivre** les instructions

---

## 🔮 Fonctionnalités Avancées Ajoutées

### 🎤 Reconnaissance Vocale
- Cliquer sur le bouton micro
- Parler en français
- Le texte apparaît automatiquement

### 🔊 Lecture Audio
- Bouton "Écouter" sur les messages IA
- Synthèse vocale française
- Contrôle du volume

### 🖥️ Mode Focus
- Bouton plein écran dans l'input
- Interface épurée pour la concentration
- Échap pour quitter

### 📊 Statistiques
- Nombre total de messages
- Moyenne par conversation
- Messages restants (plan gratuit)
- Réponses IA générées

### 🔍 Recherche Avancée
- Recherche dans les titres
- Recherche dans le contenu des messages
- Filtrage en temps réel

### 💾 Export/Import
- Export JSON des conversations
- Sauvegarde automatique
- Restauration possible

---

## 🚀 Prochaines Étapes

Une fois l'application démarrée :

1. **Tester** les cartes de suggestion
2. **Créer** plusieurs conversations
3. **Essayer** la reconnaissance vocale
4. **Changer** le thème
5. **Explorer** le mode focus
6. **Exporter** vos conversations

---

## 📞 Support

En cas de problème :
1. Vérifier que Node.js est installé
2. Redémarrer le terminal
3. Supprimer node_modules et réinstaller
4. Consulter les logs dans la console

---

**Nephiris AI React Chat v2.1.0** 🔮
*L'interface de chat la plus avancée pour votre IA*
