#!/bin/bash

echo ""
echo "========================================"
echo "   🔮 Nephiris AI - Interface React"
echo "========================================"
echo ""

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo "❌ Node.js n'est pas installé !"
    echo ""
    echo "Veuillez installer Node.js depuis : https://nodejs.org/"
    echo ""
    exit 1
fi

echo "✅ Node.js détecté"
echo ""

# Vérifier si les dépendances sont installées
if [ ! -d "node_modules" ]; then
    echo "📦 Installation des dépendances..."
    echo ""
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Erreur lors de l'installation des dépendances"
        exit 1
    fi
    echo ""
    echo "✅ Dépendances installées avec succès !"
    echo ""
fi

echo "🚀 Démarrage de l'interface Nephiris AI..."
echo ""
echo "📱 L'application s'ouvrira automatiquement dans votre navigateur"
echo "🌐 URL : http://localhost:3000"
echo ""
echo "⚡ Fonctionnalités disponibles :"
echo "  - Interface ChatGPT moderne"
echo "  - Thème sombre/clair"
echo "  - Historique des conversations"
echo "  - Suggestions intelligentes"
echo "  - Support Markdown"
echo "  - PWA (installable)"
echo ""
echo "🛑 Pour arrêter : Ctrl+C"
echo ""

# Démarrer l'application React
npm start
