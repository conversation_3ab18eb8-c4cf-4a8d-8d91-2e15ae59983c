# 🚀 Guide d'Installation - Nephiris AI React Chat

## 📋 P<PERSON>requis

Avant de commencer, assurez-vous d'avoir installé :

### 1. Node.js (version 16 ou supérieure)
- **Télécharger** : https://nodejs.org/
- **Vérifier l'installation** : `node --version`
- **Version recommandée** : Node.js 18 LTS

### 2. npm (inclus avec Node.js)
- **Vérifier l'installation** : `npm --version`

## 🛠️ Installation Étape par Étape

### Étape 1 : Navigation vers le dossier
```bash
cd frontend/react-chat
```

### Étape 2 : Installation des dépendances
```bash
npm install
```

**Si vous rencontrez des erreurs**, essayez :
```bash
# Nettoyer le cache npm
npm cache clean --force

# Supprimer node_modules et package-lock.json
rm -rf node_modules package-lock.json

# Réinstaller
npm install
```

### Étape 3 : Démarrage de l'application
```bash
npm start
```

L'application s'ouvrira automatiquement dans votre navigateur à l'adresse :
**http://localhost:3000**

## 🎯 Scripts Disponibles

### Développement
```bash
npm start          # Démarre en mode développement
npm run dev        # Alias pour npm start
```

### Production
```bash
npm run build      # Construit l'application pour la production
npm test           # Lance les tests
```

### Utilitaires
```bash
npm run eject      # Éjecte la configuration (ATTENTION : irréversible)
```

## 🔧 Dépannage

### Problème : "npm command not found"
**Solution** : Installer Node.js depuis https://nodejs.org/

### Problème : "Permission denied"
**Solution** (Linux/Mac) :
```bash
sudo npm install -g npm@latest
```

### Problème : "Port 3000 already in use"
**Solution** :
```bash
# Utiliser un autre port
PORT=3001 npm start

# Ou tuer le processus sur le port 3000
npx kill-port 3000
```

### Problème : Erreurs de dépendances
**Solution** :
```bash
# Supprimer node_modules et réinstaller
rm -rf node_modules package-lock.json
npm install
```

## 🌐 Accès à l'Application

Une fois démarrée, l'application sera accessible à :
- **Local** : http://localhost:3000
- **Réseau** : http://[votre-ip]:3000

## 📱 Installation PWA

L'application peut être installée comme une PWA :
1. **Ouvrir** l'application dans Chrome/Edge
2. **Cliquer** sur l'icône d'installation dans la barre d'adresse
3. **Suivre** les instructions d'installation

## 🎨 Fonctionnalités Disponibles

### ✅ Interface Moderne
- Design ChatGPT-style
- Thème sombre/clair
- Animations fluides
- Responsive design

### ✅ Chat Avancé
- Historique des conversations
- Support Markdown
- Indicateurs de frappe
- Actions sur les messages

### ✅ Fonctionnalités Techniques
- PWA (installable)
- Cache offline
- Persistance LocalStorage
- Notifications toast

## 🔄 Mise à Jour

Pour mettre à jour l'application :
```bash
# Mettre à jour les dépendances
npm update

# Ou réinstaller complètement
rm -rf node_modules package-lock.json
npm install
```

## 📞 Support

### En cas de problème :
1. **Vérifier** que Node.js est installé
2. **Supprimer** node_modules et réinstaller
3. **Redémarrer** le terminal
4. **Vérifier** les ports disponibles

### Logs utiles :
```bash
# Voir les logs détaillés
npm start --verbose

# Vérifier la configuration
npm config list
```

## 🚀 Démarrage Rapide

### Windows
```cmd
# Double-cliquer sur start.bat
# OU
cd frontend\react-chat
npm install
npm start
```

### Linux/Mac
```bash
# Exécuter start.sh
chmod +x start.sh
./start.sh

# OU
cd frontend/react-chat
npm install
npm start
```

## 🎯 Prochaines Étapes

Une fois l'application démarrée :
1. **Tester** les cartes de suggestion
2. **Créer** une nouvelle conversation
3. **Changer** le thème (bouton soleil/lune)
4. **Explorer** les fonctionnalités avancées

---

**Nephiris AI** - L'interface moderne pour votre IA 🔮

Pour toute question, consultez le README.md principal.
