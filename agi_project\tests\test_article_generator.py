import unittest
from agi_project.modules.generation.article_generator import ArticleGenerator

class TestArticleGenerator(unittest.TestCase):
    def setUp(self):
        self.generator = ArticleGenerator()

    def test_generate_article_basic(self):
        topic = "L'impact de l'IA sur l'éducation"
        article = self.generator.generate(topic)
        self.assertIn("title", article)
        self.assertIn("content", article)
        self.assertIn("references", article)
        self.assertGreater(len(article["content"]), 0)

    def test_empty_topic(self):
        with self.assertRaises(ValueError):
            self.generator.generate("")

    def test_length_options(self):
        topic = "Les énergies renouvelables en Europe"
        lengths = ["short", "medium", "long"]
        for length in lengths:
            article = self.generator.generate(topic, length=length)
            self.assertEqual(article["length"], length)

    def test_style_options(self):
        topic = "L'histoire de l'informatique"
        styles = ["academic", "journalistic", "blog"]
        for style in styles:
            article = self.generator.generate(topic, style=style)
            self.assertEqual(article["style"], style)

    def test_reference_count(self):
        topic = "Les neurosciences cognitives"
        for ref_count in [3, 5, 7]:
            article = self.generator.generate(topic, reference_count=ref_count)
            self.assertEqual(len(article["references"]), ref_count)

if __name__ == '__main__':
    unittest.main()
