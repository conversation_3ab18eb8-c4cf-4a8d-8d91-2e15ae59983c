import unittest
from agi_project.modules.generation.music_generator import MusicGenerator

class TestMusicGenerator(unittest.TestCase):
    def setUp(self):
        self.generator = MusicGenerator()

    def test_generate_music_basic(self):
        description = "Une mélodie calme de piano"
        result = self.generator.generate(description)
        self.assertIn("audio_url", result)
        self.assertIn("duration", result)
        self.assertGreater(result["duration"], 0)

    def test_empty_description(self):
        with self.assertRaises(ValueError):
            self.generator.generate("")

    def test_genre_options(self):
        description = "Musique de fond"
        genres = ["classical", "electronic", "jazz"]
        for genre in genres:
            result = self.generator.generate(description, genre=genre)
            self.assertEqual(result["genre"], genre)

    def test_duration_control(self):
        description = "Musique d'ambiance"
        durations = [30, 60, 120]  # en secondes
        for duration in durations:
            result = self.generator.generate(description, duration=duration)
            self.assertAlmostEqual(result["duration"], duration, delta=5)

    def test_instruments_options(self):
        description = "Composition musicale"
        instruments = ["piano", "guitar", "strings"]
        for instrument in instruments:
            result = self.generator.generate(description, instruments=[instrument])
            self.assertIn(instrument, result["instruments"])

if __name__ == '__main__':
    unittest.main()
