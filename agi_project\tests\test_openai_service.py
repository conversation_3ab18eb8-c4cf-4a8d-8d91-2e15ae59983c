"""
Tests for tools/openai_service.py
Checks normal, invalid, and edge cases for the OpenAI service.
Mocks httpx calls to avoid real API usage.
"""

import pytest
import httpx
import asyncio
from unittest.mock import patch, MagicMock

from agi_project.tools.openai_service import OpenAIService

@pytest.mark.asyncio
async def test_query_openai_valid():
    service = OpenAIService(model="test-model")
    with patch("httpx.AsyncClient.post") as mock_post:
        # Mock a successful response
        mock_response = MagicMock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            "choices": [{
                "message": {
                    "content": "Mocked response"
                }
            }]
        }
        mock_post.return_value = mock_response

        result = await service.query_openai(prompt="Hello")
        assert result == "Mocked response"

@pytest.mark.asyncio
async def test_query_openai_invalid_key():
    service = OpenAIService(model="test-model")
    # Force api_key to None
    service.api_key = None

    result = await service.query_openai(prompt="Hello")
    assert "n'est pas configurée" in result

@pytest.mark.asyncio
async def test_query_openai_http_error():
    service = OpenAIService()

    with patch("httpx.AsyncClient.post") as mock_post:
        # Mock a failed HTTP request
        mock_response = MagicMock()
        # Raise HTTPError
        mock_response.raise_for_status.side_effect = httpx.HTTPError("Mock HTTP error")
        mock_post.return_value = mock_response

        result = await service.query_openai(prompt="Test error")
        assert "Erreur lors de la requête" in result

@pytest.mark.asyncio
async def test_query_openai_unexpected_response():
    service = OpenAIService()

    with patch("httpx.AsyncClient.post") as mock_post:
        # Mock an unexpected JSON structure
        mock_response = MagicMock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {"unexpected": "format"}
        mock_post.return_value = mock_response

        result = await service.query_openai(prompt="Test unexpected")
        assert "format de réponse inattendu" in result
