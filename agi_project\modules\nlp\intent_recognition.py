"""
Module : intent_recognition.py

Ce module gère la reconnaissance d’intentions dans des phrases ou requêtes utilisateur,
permettant à l’AGI de comprendre le but ou la demande sous-jacente.

Fonctionnalités :
- Définition d’un ensemble d’intentions possibles (ex. salutation, demande d’information, commande).
- Analyse simple basée sur des mots-clés ou expressions régulières.
- Retourne l’intention la plus probable avec un score de confiance.
- Extensible pour intégrer des modèles ML ou NLP avancés.

Exemple d’utilisation :
intent = IntentRecognition.recognize_intent("Peux-tu me donner la météo ?")

Approche simplifiée :
- Utilisation d’un dictionnaire de mots-clés associés à chaque intention.
- Calcul d’un score basé sur la présence de mots-clés.
"""

import re
from typing import Dict, Tuple

class IntentRecognition:
    INTENT_KEYWORDS = {
        "greeting": ["bonjour", "salut", "hello", "coucou"],
        "weather_query": ["météo", "temps", "pluie", "soleil"],
        "farewell": ["au revoir", "bye", "à bientôt"],
        "help_request": ["aide", "aider", "support", "problème"],
        "information_request": ["informations", "détails", "explique", "qu'est-ce que"],
        "command": ["exécute", "lance", "démarre", "arrête"]
    }

    @staticmethod
    def recognize_intent(text: str) -> Tuple[str, float]:
        """
        Analyse le texte et retourne l’intention la plus probable et un score de confiance.
        """
        text_lower = text.lower()
        scores = {intent: 0 for intent in IntentRecognition.INTENT_KEYWORDS}

        for intent, keywords in IntentRecognition.INTENT_KEYWORDS.items():
            for kw in keywords:
                if re.search(r'\b' + re.escape(kw) + r'\b', text_lower):
                    scores[intent] += 1

        # Trouver l’intention avec le score le plus élevé
        best_intent = max(scores, key=scores.get)
        best_score = scores[best_intent] / max(len(IntentRecognition.INTENT_KEYWORDS[best_intent]), 1)

        return best_intent, best_score
