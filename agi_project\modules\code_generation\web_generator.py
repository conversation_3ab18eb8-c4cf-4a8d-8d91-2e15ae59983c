import os
from typing import Dict, List
import subprocess

class WebGenerator:
    """Générateur de projets web comportant plusieurs frameworks (React, Vue, Angular, HTML)."""
    
    FRAMEWORKS = ['react', 'vue', 'angular', 'html']
    
    def __init__(self):
        self.template_paths = {
            'react': 'templates/react_base',
            'vue': 'templates/vue_base',
            'html': 'templates/html_base'
        }

    def create_project(self, config: Dict) -> Dict:
        """
        Crée un nouveau projet web en fonction d'un dictionnaire de configuration.

        :param config:
            {
              'framework': 'react' | 'vue' | 'angular' | 'html',
              'project_name': 'nomDuProjet',
              'components': ['NomComposant1', 'NomComposant2', ...]
            }
        :return: Un dictionnaire décrivant le statut final du projet.
        """
        framework = config.get('framework', 'react')
        if framework not in self.FRAMEWORKS:
            raise ValueError(f"Framework '{framework}' non supporté.")

        project_name = config['project_name']
        project_dir = os.path.join('projects', project_name)
        os.makedirs(project_dir, exist_ok=True)

        # Copier le template
        self._copy_template(framework, project_dir)

        # Générer les composants
        self._generate_components(config.get('components', []), project_dir, framework)

        return {
            'status': 'success',
            'project_path': project_dir,
            'next_steps': ['npm install', 'npm start']
        }

    def _copy_template(self, framework: str, target_dir: str):
        """
        Copie les fichiers de base selon le framework.
        """
        import shutil
        template_dir = self.template_paths.get(framework)
        if not template_dir:
            return

        source_path = os.path.join(os.path.dirname(__file__), '..', '..', template_dir)
        if not os.path.exists(source_path):
            raise FileNotFoundError(f"Le répertoire de template '{source_path}' est introuvable.")

        shutil.copytree(source_path, target_dir, dirs_exist_ok=True)

    def _generate_components(self, components: List[str], project_dir: str, framework: str):
        """
        Génère des composants selon le framework. 
        Pour l’instant, un placeholder React est créé, 
        et pour Vue/Angular/HTML on pourra adapter si besoin.
        """
        if not components:
            return

        if framework == 'react':
            self._generate_react_components(components, project_dir)
        else:
            # Placeholder pour d'autres frameworks
            pass

    def _generate_react_components(self, components: List[str], project_dir: str):
        """
        Crée des fichiers .jsx basiques pour chaque composant React.
        """
        components_dir = os.path.join(project_dir, 'src', 'components')
        os.makedirs(components_dir, exist_ok=True)

        for component in components:
            lines = [
                "import React from 'react';",
                "",
                f"function {component}() {{",
                "  return (",
                "    <div>",
                f"      // {component}: Implémentez ici votre composant",
                "    </div>",
                "  );",
                "}",
                "",
                f"export default {component};",
            ]
            content = "\n".join(lines)

            filepath = os.path.join(components_dir, f"{component}.jsx")
            with open(filepath, "w", encoding="utf-8") as f:
                f.write(content)

    def deploy(self, project_path: str, platform: str = 'vercel') -> Dict:
        """
        Déploie le projet sur la plateforme spécifiée. Défaut: vercel
        """
        if platform == 'vercel':
            result = subprocess.run(['vercel', '--prod'], cwd=project_path, capture_output=True)
            success = (result.returncode == 0)
            return {
                'success': success,
                'url': 'https://your-project.vercel.app' if success else None
            }
        else:
            raise ValueError(f"Plateforme non supportée: {platform}")
