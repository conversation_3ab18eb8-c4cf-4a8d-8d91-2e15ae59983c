import unittest
from agi_project.modules.nlp.entity_recognition import EntityRecognizer

class TestEntityRecognition(unittest.TestCase):
    def setUp(self):
        self.recognizer = EntityRecognizer()

    def test_recognize_entities(self):
        text = "Apple est situé à Cupertino en Californie."
        result = self.recognizer.extract_entities(text)
        self.assertIn("Apple", result["organizations"])
        self.assertIn("Cupertino", result["locations"])
        self.assertIn("Californie", result["locations"])

    def test_empty_text(self):
        with self.assertRaises(ValueError):
            self.recognizer.extract_entities("")

    def test_no_entities_found(self):
        text = "Ceci est un test simple."
        result = self.recognizer.extract_entities(text)
        self.assertEqual(result, {
            "persons": [],
            "organizations": [],
            "locations": []
        })

    def test_multilingual_support(self):
        text = "Elon Musk trabaja en Tesla y SpaceX en California."
        result = self.recognizer.extract_entities(text)
        self.assertIn("Elon Musk", result["persons"])
        self.assertIn("Tesla", result["organizations"])
        self.assertIn("SpaceX", result["organizations"])
        self.assertIn("California", result["locations"])

if __name__ == '__main__':
    unittest.main()
