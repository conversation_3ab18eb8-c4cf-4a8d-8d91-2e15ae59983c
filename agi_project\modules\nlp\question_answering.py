"""
Module : question_answering.py

Ce module gère la réponse à des questions posées en langage naturel,
en s’appuyant sur un corpus de documents ou une base de connaissances.

Fonctionnalités :
- Recherche simple de réponses dans un texte donné.
- Extraction de la phrase la plus pertinente contenant la réponse.
- Support de questions factuelles ou ouvertes (placeholder).
- Extensible pour intégrer des modèles NLP avancés (ex. BERT, GPT).

Exemple d’utilisation :
answer = QuestionAnswering.answer_question(
    question="Qui est le président de la France ?",
    context="<PERSON> est le président de la République française depuis 2017."
)

Approche simplifiée :
- Recherche de mots-clés dans le contexte.
- Extraction de la phrase contenant le plus de mots-clés.
"""

import re
from typing import Optional

class QuestionAnswering:
    @staticmethod
    def answer_question(question: str, context: str) -> Optional[str]:
        """
        Cherche la réponse à la question dans le contexte donné.
        Retourne la phrase la plus pertinente ou None si aucune réponse trouvée.
        """
        if not question.strip() or not context.strip():
            return None

        question_words = set(re.findall(r'\w+', question.lower()))
        sentences = re.split(r'(?<=[.!?])\s+', context)

        best_sentence = None
        max_overlap = 0

        for sentence in sentences:
            sentence_words = set(re.findall(r'\w+', sentence.lower()))
            overlap = len(question_words.intersection(sentence_words))
            if overlap > max_overlap:
                max_overlap = overlap
                best_sentence = sentence

        return best_sentence
