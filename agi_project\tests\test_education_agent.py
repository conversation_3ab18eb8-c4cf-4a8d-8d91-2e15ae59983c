import pytest
import asyncio
from unittest.mock import AsyncMock, patch

from agi_project.agent.education_agent import EducationAgent
from agi_project.agent.multi_agent_system import Task

@pytest.mark.asyncio
class TestEducationAgent:

    @pytest.fixture
    def education_agent(self):
        return EducationAgent(agent_id="edu1")

    @pytest.fixture
    def quiz_task(self):
        # Format: "quiz:subject:5" => 5 questions on subject
        return Task(id="task_quiz_1", description="quiz:IA Générative:5")

    @pytest.fixture
    def learning_path_task(self):
        # Format: "learning_path:Python Débutant:Débutant"
        return Task(id="task_lp_1", description="learning_path:Python Débutant:Débutant")

    @pytest.fixture
    def assessment_task(self):
        # Format: "assessment:Système Multi-Agents:Voici ma réponse"
        return Task(id="task_assess_1", description="assessment:Système Multi-Agents:Voici ma réponse")

    @pytest.fixture
    def invalid_format_task(self):
        # Missing the third part (details)
        return Task(id="task_invalid_fmt", description="quiz:IA Générative")

    @pytest.mark.parametrize("description", [
        "quiz:IA Générative:",
        "learning_path:Python",
        "assessment:Système Multi-Agents"
    ])
    async def test_incomplete_input(self, education_agent, description):
        task = Task(id="bad_format", description=description)
        result = await education_agent.execute_task(task)

        assert result["status"] == "failed"
        assert "Format attendu" in result["error"]

    @patch("agi_project.modules.generation.quiz_generator.QuizGenerator.generate_quiz", new_callable=AsyncMock)
    async def test_quiz_generation(self, mock_quiz_gen, education_agent, quiz_task):
        mock_quiz_gen.return_value = [
            {"question": "Qu'est-ce que l'IA Générative ?", "answer": "Une IA capable de générer du contenu."},
            # ...
        ]
        result = await education_agent.execute_task(quiz_task)

        assert result["status"] == "completed"
        assert result["content_type"] == "quiz"
        assert len(result["result"]) >= 1
        assert "question" in result["result"][0]

    @patch("agi_project.modules.knowledge.knowledge_graph_manager.KnowledgeGraphManager.get_related_concepts", new_callable=AsyncMock)
    @patch("agi_project.modules.nlp.question_answering.QuestionAnswering.generate_lesson", new_callable=AsyncMock)
    @patch("agi_project.modules.generation.quiz_generator.QuizGenerator.generate_quiz", new_callable=AsyncMock)
    async def test_learning_path_generation(
        self, mock_quiz_gen, mock_lesson, mock_get_concepts, education_agent, learning_path_task
    ):
        mock_get_concepts.return_value = ["Variables", "Boucles", "Conditions", "Fonctions", "Classes"]
        mock_lesson.return_value = "Contenu de cours"
        mock_quiz_gen.return_value = [{"question": "Sur les variables ?", "answer": "Réponse..."}]

        result = await education_agent.execute_task(learning_path_task)

        assert result["status"] == "completed"
        assert result["content_type"] == "learning_path"
        assert "modules" in result["result"]
        assert len(result["result"]["modules"]) == 5

    @patch("agi_project.modules.nlp.question_answering.QuestionAnswering.evaluate_answer", new_callable=AsyncMock)
    async def test_assessment(self, mock_evaluate, education_agent, assessment_task):
        mock_evaluate.return_value = {
            "score": 0.8,
            "feedback": "Bonne réponse dans l'ensemble",
            "improvement_suggestions": ["Donner plus de précisions sur la partie architecture."],
        }
        result = await education_agent.execute_task(assessment_task)

        assert result["status"] == "completed"
        assert result["content_type"] == "assessment"
        assert "score" in result["result"]
        assert "feedback" in result["result"]
        assert result["result"]["score"] == 0.8

    @pytest.mark.asyncio
    async def test_unknown_type(self, education_agent):
        # Unknown type => should fail
        task = Task(id="task_unknown", description="random:Something:More")
        result = await education_agent.execute_task(task)
        assert result["status"] == "failed"
        assert "non supporté" in result["error"]
