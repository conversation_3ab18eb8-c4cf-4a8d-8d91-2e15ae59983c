"""
Module : quiz_generator.py

Ce module gère la génération de quiz selon différents formats :
- QCM (Questions à Choix Multiples)
- Vrai/Faux
- Questions ouvertes personnalisées

Objectif :
Fournir un ensemble de fonctions pour créer et configurer des quiz
basés sur un contenu donné, générer des distracteurs (mauvaises réponses)
et formater la présentation du quiz.

Utilisation :
- quiz = QuizGenerator.generate_mcq(content, nb_questions=5) pour générer un quiz QCM
- quiz = QuizGenerator.generate_true_false(statements) pour générer des questions Vrai/Faux

Fonctionnalités :
- Génération automatique de questions et réponses factices
- Paramétrage du nombre de questions, de la difficulté, etc.
- Possibilité d’exporter le quiz sous format JSON, CSV ou Markdown
- Support multilingue (extension possible)

"""

import random
import json
from typing import List, Dict, Any

class QuizGenerator:
    @staticmethod
    def generate_mcq(content: List[str], nb_questions: int = 5) -> Dict[str, Any]:
        """
        Génère un quiz de type QCM à partir d'un contenu,
        Suppose que 'content' est une liste de faits/phrases.
        """
        questions = []
        # Logique simplifiée : on prend nb_questions phrases au hasard 
        # et on produit une question + 3 distracteurs.
        random.shuffle(content)
        selected = content[:nb_questions]

        for idx, fact in enumerate(selected):
            # Générer la question
            question_text = f"Question {idx+1}: {fact}\nQuelle est la suite logique ?"
            # Générer des réponses
            correct_answer = "Réponse correcte"
            distractors = [
                "Réponse erronée 1",
                "Réponse erronée 2",
                "Réponse erronée 3"
            ]
            random.shuffle(distractors)
            questions.append({
                "question": question_text,
                "options": [correct_answer] + distractors,
                "correct_index": 0
            })
        return {
            "type": "QCM",
            "questions": questions
        }

    @staticmethod
    def generate_true_false(statements: List[str]) -> Dict[str, Any]:
        """
        Génère un quiz Vrai/Faux à partir d'un ensemble d'énoncés.
        """
        quiz_content = []
        for idx, st in enumerate(statements):
            # On choisit au hasard si c'est vrai ou faux si on ne dispose pas d'une info
            # sur la véracité.
            is_true = bool(random.getrandbits(1))
            quiz_content.append({
                "statement": st,
                "answer": "Vrai" if is_true else "Faux"
            })
        return {
            "type": "Vrai/Faux",
            "items": quiz_content
        }

    @staticmethod
    def generate_open_questions(topic: str, nb_questions: int = 3) -> Dict[str, Any]:
        """
        Génère des questions ouvertes basées sur un sujet donné.
        Les réponses attendues sont libres et doivent être évaluées manuellement
        ou par un module NLP plus avancé si nécessaire.
        """
        questions = []
        for i in range(nb_questions):
            question_text = f"Décrivez l'aspect {i+1} du sujet {topic}"
            questions.append({
                "question": question_text,
                "guidance": "Réponse libre"
            })
        return {
            "type": "Questions ouvertes",
            "topic": topic,
            "questions": questions
        }

    @staticmethod
    def export_quiz(quiz: Dict[str, Any], filepath: str):
        """
        Exporte le quiz sous format JSON.
        """
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(quiz, f, ensure_ascii=False, indent=2)
