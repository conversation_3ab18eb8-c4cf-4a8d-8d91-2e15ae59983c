"""
Module : text_classifier.py

Ce module gère la classification de textes selon différentes catégories/thèmes,
par exemple pour filtrer du spam, étiqueter des avis sentimentaux (positif, négatif),
ou classifier des articles par sujet (politique, sport, etc.).

Fonctionnalités :
- Possibilité de définir un jeu de labels (catégories) et d’entraîner un modèle simple.
- Possibilité d’effectuer une classification rapide sur un texte donné.
- Gestion de données d’entraînement et d’évaluation (placeholder dans ce module).
- Extension possible pour intégrer un modèle ML plus avancé (scikit-learn, PyTorch, etc.).

Exemple d’utilisation :
1. Créer un TextClassifier et définir la liste de labels (['positif', 'négatif']).
2. Appeler train_model() en fournissant un petit dataset.
3. Utiliser predict() pour étiqueter un nouveau texte.

Approche simplifiée :
- Entraînement rudimentaire basé sur la fréquence de mots,
- Pas de persistance du modèle dans un fichier, ni cross-validation avancée.
"""

import re
from typing import List, Dict, Any
from collections import defaultdict, Counter

class TextClassifier:
    def __init__(self, labels: List[str]):
        """
        labels : liste de catégories possibles.
        """
        self.labels = labels
        # Pour chaque label, on stocke la fréquence globale de chaque mot.
        self.label_word_freqs: Dict[str, Counter] = {label: Counter() for label in labels}
        self.label_counts: Dict[str, int] = {label: 0 for label in labels}
        self.vocab: Counter = Counter()

    def train_model(self, data: List[Dict[str, Any]]):
        """
        data : liste de paires { 'text': ..., 'label': ... }
        Met à jour les fréquences de mots pour chaque label.
        """
        for entry in data:
            text = entry['text']
            label = entry['label']
            if label not in self.labels:
                continue  # Label inconnu, on l’ignore ou on l’ajoute dynamiquement
            self.label_counts[label] += 1
            tokens = re.findall(r'\w+', text.lower())
            self.label_word_freqs[label].update(tokens)
            self.vocab.update(tokens)

    def predict(self, text: str) -> str:
        """
        Prédit la catégorie du texte parmi self.labels, en se basant sur un scoring naïf.
        """
        tokens = re.findall(r'\w+', text.lower())
        # On calcule un score pour chaque label.
        scores = {label: 0.0 for label in self.labels}
        for label in self.labels:
            label_total_words = sum(self.label_word_freqs[label].values())
            if label_total_words == 0:
                continue
            for word in tokens:
                # Laplace smoothing
                word_freq = self.label_word_freqs[label][word] + 1
                word_score = word_freq / (label_total_words + len(self.vocab))
                scores[label] += word_score

        # On retourne le label avec le plus grand score
        best_label = max(scores, key=scores.get)
        return best_label
