"""
Performance and Stress Testing Script

1. Measures latency for /chat endpoint with valid prompts.
2. Simulates concurrent requests to identify potential bottlenecks.
3. Logs results for analysis (latency distribution, error rates, etc.).

Note: This script uses the 'locust' library for load testing.

Usage:
1) Install locust if not installed: `pip install locust`
2) Run: `locust -f agi_project/tests/test_performance.py`

"""
import time
from locust import HttpUser, task, between

# Adjust these as needed for more or fewer requests, concurrency, intervals, etc.

class AGIPerformanceTest(HttpUser):
    wait_time = between(1, 3)  # Wait time between tasks
    
    @task
    def chat_performance_test(self):
        """
        Simple test sending a request to /chat with a basic prompt.
        """
        start_time = time.time()
        payload = {"message": "Hello, how are you?"}
        with self.client.post("/chat", json=payload, catch_response=True) as response:
            end_time = time.time()
            latency = end_time - start_time

            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"HTTP {response.status_code} | {response.text}")

        # We could log latency or do advanced analytics
        # print(f"Request took: {latency * 1000:.2f} ms")
