@echo off
echo.
echo ========================================
echo   🔮 NEPHIRIS AI - SERVEUR PORTABLE
echo ========================================
echo.

REM Vérifier si Python est installé
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python n'est pas installé !
    echo.
    echo 💡 Solutions :
    echo   1. Installer Python depuis : https://python.org/
    echo   2. OU utiliser la version HTML simple
    echo.
    pause
    exit /b 1
)

echo ✅ Python détecté
echo.

echo 🚀 Démarrage du serveur Nephiris AI...
echo.
echo 📱 Interface de test AGI
echo 🌐 URL : http://127.0.0.1:5000
echo.
echo ⚡ Fonctionnalités :
echo   - Interface ChatGPT moderne
echo   - Test de votre AGI
echo   - API /api/chat pour connexion
echo   - Historique des conversations
echo.
echo 🛑 Pour arrêter : Ctrl+C
echo.

REM Démarrer le serveur Python
python server.py

pause
