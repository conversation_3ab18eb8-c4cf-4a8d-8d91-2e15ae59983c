from typing import Dict, List
import asyncio

from agi_project.agent.multi_agent_system import Agent, AgentType, Task
from agi_project.modules.generation.image_generator import ImageGenerator
from agi_project.modules.generation.video_script_generator import VideoScriptGenerator
from agi_project.modules.generation.voice_generator import VoiceGenerator
from agi_project.modules.generation.music_generator import MusicGenerator

class CreativeAgent(Agent):
    """
    Agent spécialisé dans la génération de contenu créatif :
    images, vidéos, voix, musique, etc.
    """
    
    def __init__(self, agent_id: str):
        super().__init__(
            agent_id, 
            AgentType.CREATIVE,
            specialties=["image", "video", "voice", "music"]
        )
        self.image_gen = ImageGenerator()
        self.video_gen = VideoScriptGenerator()
        self.voice_gen = VoiceGenerator()
        self.music_gen = MusicGenerator()
        self.generated_content: Dict[str, str] = {}

    async def execute_task(self, task: Task) -> Dict:
        """
        Exécute une tâche créative selon le type demandé
        dans task.description (format: "type:prompt")
        """
        self.current_task = task
        
        try:
            content_type, prompt = task.description.split(":", 1)
            content_type = content_type.strip().lower()
            prompt = prompt.strip()

            result = None
            if content_type == "image":
                result = await self.image_gen.generate(prompt)
            elif content_type == "video":
                result = await self.video_gen.generate(prompt)
            elif content_type == "voice":
                result = await self.voice_gen.generate(prompt)
            elif content_type == "music":
                result = await self.music_gen.generate(prompt)
            else:
                raise ValueError(f"Type de contenu non supporté: {content_type}")

            # Stocker le contenu généré avec son ID de tâche
            self.generated_content[task.id] = result
            
            task.status = "completed"
            return {
                "agent_id": self.id,
                "status": "completed",
                "content_type": content_type,
                "result": result
            }

        except Exception as e:
            task.status = "failed"
            return {
                "agent_id": self.id,
                "status": "failed",
                "error": str(e)
            }

    def learn_from_experience(self, task_result: Dict):
        """
        Analyse les résultats pour améliorer les futures générations
        """
        self.learning_history.append({
            "task": self.current_task,
            "result": task_result,
            "insights": self._extract_insights(task_result)
        })
        self.current_task = None

    def _extract_insights(self, task_result: Dict) -> List[str]:
        """
        Extrait des informations utiles sur la génération
        """
        insights = []
        if task_result["status"] == "completed":
            insights.append(f"Generated {task_result['content_type']} content successfully")
        else:
            insights.append(f"Generation failed: {task_result.get('error', 'Unknown error')}")
        return insights

    def get_generated_content(self, task_id: str) -> str:
        """
        Récupère le contenu généré pour une tâche donnée
        """
        return self.generated_content.get(task_id, "")
