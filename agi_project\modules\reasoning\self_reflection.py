"""
Module de réflexion et d'auto-évaluation pour l'AGI
Permet à l'agent d'analyser ses propres décisions et d'améliorer son raisonnement
"""

from typing import Dict, List, Any
import json
from datetime import datetime

class SelfReflection:
    def __init__(self):
        self.thought_stream = []
        self.decisions_history = []
        self.improvement_suggestions = []
        
    def add_thought(self, thought: str, context: Dict[str, Any] = None):
        """Ajoute une pensée au flux de conscience"""
        timestamp = datetime.now().isoformat()
        thought_entry = {
            "timestamp": timestamp,
            "thought": thought,
            "context": context or {}
        }
        self.thought_stream.append(thought_entry)
        return thought_entry

    def evaluate_decision(self, decision: Dict[str, Any], outcome: Dict[str, Any]):
        """Évalue une décision et son résultat"""
        evaluation = {
            "timestamp": datetime.now().isoformat(),
            "decision": decision,
            "outcome": outcome,
            "analysis": self._analyze_outcome(decision, outcome),
            "improvements": self._generate_improvements(decision, outcome)
        }
        self.decisions_history.append(evaluation)
        return evaluation

    def _analyze_outcome(self, decision: Dict[str, Any], outcome: Dict[str, Any]) -> Dict[str, Any]:
        """Analyse le résultat d'une décision"""
        success = outcome.get("success", False)
        expected_result = decision.get("expected_result", {})
        actual_result = outcome.get("actual_result", {})
        
        analysis = {
            "success": success,
            "match_expected": expected_result == actual_result,
            "divergence": self._calculate_divergence(expected_result, actual_result),
            "learning_points": self._extract_learning_points(decision, outcome)
        }
        return analysis

    def _generate_improvements(self, decision: Dict[str, Any], outcome: Dict[str, Any]) -> List[str]:
        """Génère des suggestions d'amélioration basées sur l'analyse"""
        improvements = []
        analysis = self._analyze_outcome(decision, outcome)
        
        if not analysis["success"]:
            improvements.append("Revoir la logique de décision")
        if not analysis["match_expected"]:
            improvements.append("Ajuster les prédictions de résultats")
        if analysis["divergence"] > 0.5:  # seuil arbitraire
            improvements.append("Améliorer la précision des estimations")
            
        self.improvement_suggestions.extend(improvements)
        return improvements

    def _calculate_divergence(self, expected: Dict[str, Any], actual: Dict[str, Any]) -> float:
        """Calcule l'écart entre résultats attendus et réels"""
        if not expected or not actual:
            return 1.0
        matches = sum(1 for k in expected if k in actual and expected[k] == actual[k])
        total = len(set(expected.keys()) | set(actual.keys()))
        return 1 - (matches / total if total > 0 else 0)

    def _extract_learning_points(self, decision: Dict[str, Any], outcome: Dict[str, Any]) -> List[str]:
        """Extrait les points d'apprentissage d'une décision"""
        learning_points = []
        
        if not outcome.get("success", False):
            learning_points.append(f"Échec: {outcome.get('error', 'Raison inconnue')}")
        
        if decision.get("confidence", 0) > 0.8 and not outcome.get("success", False):
            learning_points.append("Surconfiance potentielle dans la décision")
            
        if decision.get("alternatives", []):
            learning_points.append("Explorer davantage les alternatives")
            
        return learning_points

    def get_improvement_summary(self) -> Dict[str, Any]:
        """Génère un résumé des améliorations suggérées"""
        return {
            "total_decisions": len(self.decisions_history),
            "success_rate": self._calculate_success_rate(),
            "top_improvements": self._get_top_improvements(),
            "recent_learnings": self._get_recent_learnings(5)
        }

    def _calculate_success_rate(self) -> float:
        """Calcule le taux de succès des décisions"""
        if not self.decisions_history:
            return 0.0
        successes = sum(1 for d in self.decisions_history if d["analysis"]["success"])
        return successes / len(self.decisions_history)

    def _get_top_improvements(self, limit: int = 3) -> List[str]:
        """Retourne les suggestions d'amélioration les plus fréquentes"""
        if not self.improvement_suggestions:
            return []
        from collections import Counter
        counts = Counter(self.improvement_suggestions)
        return [item for item, count in counts.most_common(limit)]

    def _get_recent_learnings(self, limit: int = 5) -> List[str]:
        """Retourne les apprentissages récents"""
        all_learnings = []
        for decision in reversed(self.decisions_history):
            all_learnings.extend(decision["analysis"]["learning_points"])
            if len(all_learnings) >= limit:
                break
        return all_learnings[:limit]

    def export_insights(self, filepath: str):
        """Exporte les insights pour analyse externe"""
        insights = {
            "thought_stream": self.thought_stream,
            "decisions_history": self.decisions_history,
            "improvement_summary": self.get_improvement_summary()
        }
        with open(filepath, 'w') as f:
            json.dump(insights, f, indent=2)
