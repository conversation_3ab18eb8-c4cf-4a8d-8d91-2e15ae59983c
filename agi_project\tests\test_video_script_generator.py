import unittest
from agi_project.modules.generation.video_script_generator import VideoScriptGenerator

class TestVideoScriptGenerator(unittest.TestCase):
    def setUp(self):
        self.generator = VideoScriptGenerator()

    def test_generate_script_basic(self):
        topic = "L'histoire de l'IA"
        script = self.generator.generate(topic)
        self.assertIn("title", script)
        self.assertIn("scenes", script)
        self.assertGreater(len(script["scenes"]), 0)
        self.assertIn("IA", script["title"])

    def test_empty_topic(self):
        with self.assertRaises(ValueError):
            self.generator.generate("")

    def test_script_structure(self):
        topic = "Les énergies renouvelables"
        script = self.generator.generate(topic)
        for scene in script["scenes"]:
            self.assertIn("description", scene)
            self.assertIn("duration", scene)
            self.assertIn("visuals", scene)
            self.assertIn("narration", scene)

    def test_duration_limits(self):
        topic = "Le changement climatique"
        for duration in [1, 5, 10]:
            script = self.generator.generate(topic, total_duration=duration)
            total = sum(s["duration"] for s in script["scenes"])
            self.assertAlmostEqual(total, duration, delta=0.5)

    def test_style_adaptation(self):
        topic = "L'évolution des smartphones"
        styles = ["documentary", "educational", "entertaining"]
        for style in styles:
            script = self.generator.generate(topic, style=style)
            self.assertEqual(script["style"], style)

if __name__ == '__main__':
    unittest.main()
