import React, { useState, useEffect, useRef, createContext, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiSend, FiPlus, FiLogIn, FiImage, FiZap, FiList } from 'react-icons/fi';
import { format } from 'date-fns';
import './App.css';

// Chat context to manage conversations and messages
const ChatContext = createContext();

const useChat = () => useContext(ChatContext);

// Typing effect hook for progressive text display
function useTypingEffect(text, speed = 30) {
  const [displayedText, setDisplayedText] = useState('');
  useEffect(() => {
    let index = 0;
    setDisplayedText('');
    const interval = setInterval(() => {
      setDisplayedText((prev) => prev + text.charAt(index));
      index++;
      if (index >= text.length) clearInterval(interval);
    }, speed);
    return () => clearInterval(interval);
  }, [text, speed]);
  return displayedText;
}

// Button component
const Button = ({ children, onClick, className }) => (
  <button
    onClick={onClick}
    className={`px-4 py-2 rounded-md bg-violet-600 hover:bg-violet-700 text-white transition ${className}`}
  >
    {children}
  </button>
);

// Sidebar component with conversation history and buttons
const Sidebar = () => {
  const { conversations, selectConversation, addConversation } = useChat();
  return (
    <div className="w-72 bg-gray-900 text-gray-100 flex flex-col h-full border-r border-gray-700">
      <div className="p-4 flex items-center justify-between border-b border-gray-700">
        <h2 className="text-xl font-semibold">Nephiris AI</h2>
        <Button onClick={addConversation} className="flex items-center space-x-1">
          <FiPlus />
          <span>New Chat</span>
        </Button>
      </div>
      <div className="flex-1 overflow-y-auto">
        {conversations.map((conv, idx) => (
          <div
            key={conv.id}
            onClick={() => selectConversation(conv.id)}
            className="cursor-pointer p-3 hover:bg-violet-800 border-b border-gray-700"
          >
            <div className="font-medium truncate">{conv.title || `Conversation ${idx + 1}`}</div>
            <div className="text-xs text-gray-400 truncate">{format(new Date(conv.updatedAt), 'Pp')}</div>
          </div>
        ))}
      </div>
      <div className="p-4 border-t border-gray-700 space-y-2">
        <Button className="w-full flex items-center justify-center space-x-2">
          <FiZap />
          <span>Try the AI</span>
        </Button>
        <Button className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700">
          <FiLogIn />
          <span>Google Login</span>
        </Button>
      </div>
    </div>
  );
};

// Chat message bubble component
const ChatMessage = ({ message }) => {
  const isUser = message.sender === 'user';
  const bubbleClass = isUser
    ? 'bg-violet-600 text-white self-end rounded-br-none'
    : 'bg-gray-800 text-gray-200 self-start rounded-bl-none';
  const displayedText = useTypingEffect(message.text, 20);

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0 }}
      className={`max-w-[70%] p-3 my-1 rounded-lg break-words whitespace-pre-wrap ${bubbleClass}`}
    >
      <div>{displayedText}</div>
      <div className="text-xs text-gray-400 mt-1 text-right">{format(new Date(message.timestamp), 'p')}</div>
    </motion.div>
  );
};

// Typing indicator component
const TypingIndicator = () => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    className="flex space-x-1 items-center"
  >
    <div className="w-2 h-2 bg-violet-500 rounded-full animate-bounce delay-0"></div>
    <div className="w-2 h-2 bg-violet-500 rounded-full animate-bounce delay-200"></div>
    <div className="w-2 h-2 bg-violet-500 rounded-full animate-bounce delay-400"></div>
  </motion.div>
);

// Chat input component
const ChatInput = () => {
  const { sendMessage, isTyping } = useChat();
  const [input, setInput] = useState('');
  const inputRef = useRef(null);

  const handleSend = () => {
    if (input.trim() === '') return;
    sendMessage(input.trim());
    setInput('');
    inputRef.current.focus();
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="p-4 border-t border-gray-700 flex items-center space-x-2 bg-gray-900">
      <textarea
        ref={inputRef}
        rows={1}
        value={input}
        onChange={(e) => setInput(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder="Type your message..."
        className="flex-1 resize-none rounded-md bg-gray-800 text-gray-100 p-2 focus:outline-none focus:ring-2 focus:ring-violet-500"
      />
      <button
        onClick={handleSend}
        disabled={input.trim() === ''}
        className="p-2 rounded-md bg-violet-600 hover:bg-violet-700 disabled:opacity-50 disabled:cursor-not-allowed transition"
        aria-label="Send message"
      >
        <FiSend size={20} />
      </button>
    </div>
  );
};

// Main layout component
const MainLayout = ({ children }) => (
  <div className="flex h-screen overflow-hidden bg-gray-900 text-gray-100">
    <Sidebar />
    <main className="flex-1 flex flex-col">{children}</main>
  </div>
);

// Chat window component
const ChatWindow = () => {
  const { currentConversation, messages, isTyping } = useChat();
  const messagesEndRef = useRef(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isTyping]);

  return (
    <div className="flex flex-col flex-1 p-4 overflow-y-auto space-y-2">
      {messages.map((msg) => (
        <ChatMessage key={msg.id} message={msg} />
      ))}
      {isTyping && <TypingIndicator />}
      <div ref={messagesEndRef} />
    </div>
  );
};

// Chat provider to manage state and API integration
const ChatProvider = ({ children }) => {
  const [conversations, setConversations] = useState(() => {
    const saved = localStorage.getItem('conversations');
    return saved ? JSON.parse(saved) : [];
  });
  const [currentConversationId, setCurrentConversationId] = useState(
    conversations.length > 0 ? conversations[0].id : null
  );
  const [messages, setMessages] = useState([]);
  const [isTyping, setIsTyping] = useState(false);

  // Load messages for current conversation
  useEffect(() => {
    if (!currentConversationId) {
      setMessages([]);
      return;
    }
    const conv = conversations.find((c) => c.id === currentConversationId);
    setMessages(conv?.messages || []);
  }, [currentConversationId, conversations]);

  // Save conversations to localStorage
  useEffect(() => {
    localStorage.setItem('conversations', JSON.stringify(conversations));
  }, [conversations]);

  const addConversation = () => {
    const newConv = {
      id: Date.now().toString(),
      title: '',
      messages: [],
      updatedAt: new Date().toISOString(),
    };
    setConversations([newConv, ...conversations]);
    setCurrentConversationId(newConv.id);
  };

  const selectConversation = (id) => {
    setCurrentConversationId(id);
  };

  // Simulate sending message and receiving AI response progressively
  const sendMessage = (text) => {
    if (!currentConversationId) {
      addConversation();
    }
    const userMessage = {
      id: Date.now().toString() + '-user',
      sender: 'user',
      text,
      timestamp: new Date().toISOString(),
    };
    setMessages((prev) => [...prev, userMessage]);
    updateConversationMessages(currentConversationId, [...messages, userMessage]);
    setIsTyping(true);

    // Simulate AI response with progressive typing
    let aiResponse = "This is a simulated AI response for your message: " + text;
    let index = 0;
    const interval = setInterval(() => {
      index++;
      const partial = aiResponse.slice(0, index);
      const aiMessage = {
        id: currentConversationId + '-ai',
        sender: 'ai',
        text: partial,
        timestamp: new Date().toISOString(),
      };
      setMessages((prev) => {
        const filtered = prev.filter((m) => m.sender !== 'ai');
        return [...filtered, aiMessage];
      });
      updateConversationMessages(currentConversationId, [...messages.filter(m => m.sender !== 'ai'), aiMessage]);
      if (index >= aiResponse.length) {
        clearInterval(interval);
        setIsTyping(false);
        // Update conversation title if empty
        setConversations((prev) =>
          prev.map((conv) =>
            conv.id === currentConversationId && !conv.title
              ? { ...conv, title: text.slice(0, 20) + '...', updatedAt: new Date().toISOString() }
              : conv
          )
        );
      }
    }, 30);
  };

  const updateConversationMessages = (convId, newMessages) => {
    setConversations((prev) =>
      prev.map((conv) =>
        conv.id === convId ? { ...conv, messages: newMessages, updatedAt: new Date().toISOString() } : conv
      )
    );
  };

  return (
    <ChatContext.Provider
      value={{
        conversations,
        currentConversation: conversations.find((c) => c.id === currentConversationId),
        messages,
        isTyping,
        addConversation,
        selectConversation,
        sendMessage,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

function App() {
  return (
    <ChatProvider>
      <MainLayout>
        <ChatWindow />
        <ChatInput />
      </MainLayout>
    </ChatProvider>
  );
}

export default App;
