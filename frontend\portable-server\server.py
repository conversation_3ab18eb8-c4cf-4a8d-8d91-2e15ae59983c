#!/usr/bin/env python3
"""
Serveur portable pour Nephiris AI Chat
Fonctionne sans installation Node.js
"""

import http.server
import socketserver
import webbrowser
import os
import sys
import threading
import time
from urllib.parse import urlparse, parse_qs
import json

class NephirisHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.path.dirname(os.path.abspath(__file__)), **kwargs)
    
    def end_headers(self):
        # Ajouter les headers CORS pour éviter les erreurs
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.send_header('Cache-Control', 'no-cache')
        super().end_headers()
    
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.path = '/nephiris-fixed.html'

        # Servir les fichiers statiques
        return super().do_GET()
    
    def do_POST(self):
        # Gérer les requêtes POST pour l'API chat
        if self.path == '/api/chat':
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                data = json.loads(post_data.decode('utf-8'))
                message = data.get('message', '')
                
                # Simuler une réponse IA
                response = self.generate_ai_response(message)
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                
                response_data = {
                    'message': response,
                    'timestamp': time.time()
                }
                
                self.wfile.write(json.dumps(response_data).encode('utf-8'))
                
            except Exception as e:
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                error_response = {'error': str(e)}
                self.wfile.write(json.dumps(error_response).encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()
    
    def generate_ai_response(self, message):
        """Générer une réponse IA simulée"""
        responses = [
            f"Je comprends votre question sur '{message[:50]}...'. Voici une réponse détaillée qui prend en compte votre demande.",
            f"Excellente question ! Concernant '{message[:30]}...', laissez-moi vous expliquer cela clairement.",
            f"Voici ce que je peux vous dire à propos de '{message[:40]}...'. Il y a plusieurs aspects intéressants à considérer.",
            f"Je vais vous aider avec '{message[:35]}...'. Voici une approche structurée pour répondre à votre question.",
            f"C'est un sujet fascinant ! Pour '{message[:45]}...', permettez-moi de vous donner une explication complète.",
            "Je suis Nephiris AI, votre assistant intelligent. Je peux vous aider avec diverses tâches comme l'écriture, l'analyse, la programmation, et bien plus encore.",
            "Merci pour votre message ! Je suis là pour vous assister de manière créative et efficace. Que puis-je faire pour vous aujourd'hui ?",
            "Votre question est très pertinente. Laissez-moi vous proposer plusieurs perspectives et solutions pratiques.",
        ]
        
        # Choisir une réponse basée sur le contenu du message
        if len(message) < 10:
            return responses[5]  # Réponse générale pour messages courts
        elif any(word in message.lower() for word in ['bonjour', 'salut', 'hello', 'hi']):
            return responses[6]  # Réponse de salutation
        elif '?' in message:
            return responses[7]  # Réponse pour questions
        else:
            # Réponse contextuelle
            import random
            return random.choice(responses[:5])
    
    def log_message(self, format, *args):
        # Réduire les logs pour une sortie plus propre
        if "GET" in format % args and any(ext in format % args for ext in ['.css', '.js', '.ico']):
            return
        print(f"[{time.strftime('%H:%M:%S')}] {format % args}")

def open_browser_delayed():
    """Ouvrir le navigateur après un délai"""
    time.sleep(2)
    webbrowser.open('http://127.0.0.1:5000')

def main():
    PORT = 5000
    HOST = '127.0.0.1'
    
    print("=" * 50)
    print("🔮 NEPHIRIS AI - SERVEUR PORTABLE")
    print("=" * 50)
    print()
    
    try:
        # Créer le serveur
        with socketserver.TCPServer((HOST, PORT), NephirisHandler) as httpd:
            print(f"✅ Serveur démarré avec succès !")
            print(f"🌐 URL: http://{HOST}:{PORT}")
            print(f"📁 Dossier: {os.path.dirname(os.path.abspath(__file__))}")
            print()
            print("🚀 Fonctionnalités disponibles :")
            print("   - Interface ChatGPT moderne")
            print("   - Chat avec IA simulée")
            print("   - Historique des conversations")
            print("   - Thème sombre/clair")
            print("   - Responsive design")
            print()
            print("🛑 Pour arrêter : Ctrl+C")
            print()
            
            # Ouvrir le navigateur automatiquement
            browser_thread = threading.Thread(target=open_browser_delayed)
            browser_thread.daemon = True
            browser_thread.start()
            
            # Démarrer le serveur
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 10048:  # Port déjà utilisé
            print(f"❌ Erreur: Le port {PORT} est déjà utilisé")
            print("💡 Solutions :")
            print("   1. Fermez l'autre application qui utilise ce port")
            print("   2. Ou modifiez le PORT dans ce script")
        else:
            print(f"❌ Erreur: {e}")
    except KeyboardInterrupt:
        print("\n🛑 Serveur arrêté par l'utilisateur")
        print("👋 Merci d'avoir utilisé Nephiris AI !")

if __name__ == "__main__":
    main()
