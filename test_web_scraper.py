import asyncio
from agi_project.modules.knowledge.web_scraper import MassiveDataCollector

async def test_scraper():
    collector = MassiveDataCollector(num_workers=2)
    try:
        results = await collector.collect_web([
            'https://en.wikipedia.org/wiki/Python_(programming_language)',
            'https://invalid-url.example.com'
        ])
        print(f"Results:")
        for url, content in results.items():
            print(f"\nURL: {url}")
            if isinstance(content, str):
                print(f"Content (first 200 chars): {content[:200]}...")
            else:
                print(f"Error: {content}")
    finally:
        collector.cleanup()

if __name__ == "__main__":
    asyncio.run(test_scraper())
