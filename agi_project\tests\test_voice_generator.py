import unittest
from agi_project.modules.generation.voice_generator import VoiceGenerator

class TestVoiceGenerator(unittest.TestCase):
    def setUp(self):
        self.generator = VoiceGenerator()

    def test_generate_voice_basic(self):
        text = "Bonjour, ceci est un test de synthèse vocale."
        result = self.generator.generate(text)
        self.assertIn("audio_url", result)
        self.assertIn("text", result)
        self.assertEqual(result["text"], text)

    def test_empty_text(self):
        with self.assertRaises(ValueError):
            self.generator.generate("")

    def test_voice_options(self):
        text = "Test des différentes voix disponibles"
        voices = ["male", "female", "neutral"]
        for voice in voices:
            result = self.generator.generate(text, voice=voice)
            self.assertEqual(result["voice"], voice)

    def test_speed_options(self):
        text = "Test de différentes vitesses de lecture"
        speeds = [0.8, 1.0, 1.2]
        for speed in speeds:
            result = self.generator.generate(text, speed=speed)
            self.assertEqual(result["speed"], speed)

    def test_language_support(self):
        text = "This is an English test"
        result = self.generator.generate(text, language="en-US")
        self.assertEqual(result["language"], "en-US")

if __name__ == '__main__':
    unittest.main()
