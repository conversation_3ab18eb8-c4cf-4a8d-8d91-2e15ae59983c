@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom global styles */
body {
  @apply bg-gray-900 text-gray-100 font-sans antialiased;
  overscroll-behavior: none;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  @apply w-2;
}

::-webkit-scrollbar-track {
  @apply bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-violet-600 rounded-full;
}

/* Selection styling */
::selection {
  @apply bg-violet-600 text-white;
}

/* Smooth transitions for interactive elements */
button, a, input, textarea {
  @apply transition-colors duration-200;
}

/* Focus states */
button:focus, a:focus, input:focus, textarea:focus {
  @apply outline-none ring-2 ring-violet-500 ring-offset-2 ring-offset-gray-900;
}

/* Disabled state styling */
button:disabled {
  @apply opacity-50 cursor-not-allowed;
}
