# Nephiris AI - Frontend Moderne

## 🎨 Design moderne

Ce frontend a été entièrement refait avec un design moderne.

## ✨ Nouvelles fonctionnalités

### 🎯 Interface utilisateur
- **Design moderne** : Palette de couleurs sombre avec accents colorés
- **Typography améliorée** : Utilisation d'Inter et Space Grotesk
- **Layout responsive** : Optimisé pour tous les écrans
- **Animations fluides** : Transitions et effets visuels
- **Thème sombre/clair** : Toggle pour changer de thème
- **Curseur personnalisé** : Curseur interactif avec effets

### 🔧 Fonctionnalités interactives
- **Navigation fluide** : Scroll smooth entre les sections
- **Boutons fonctionnels** : Tous les boutons ont des actions
- **Modals informatifs** : Détails sur chaque fonctionnalité
- **Notifications avancées** : Système de notifications avec types
- **Effets de scroll** : Animations au défilement
- **Particules interactives** : Effets visuels en arrière-plan
- **Statistiques animées** : Compteurs avec animations

### 🤖 Assistant IA intégré
- **Chatbot en temps réel** : Assistant IA accessible en bas à droite
- **Conversations intelligentes** : Réponses contextuelles
- **Interface moderne** : Design épuré avec avatars
- **Notifications chat** : Indicateur de nouveaux messages

### 🎵 Expérience immersive
- **Effets sonores** : Sons subtils pour les interactions
- **Toggle audio** : Contrôle des effets sonores
- **Feedback haptique** : Vibrations sur mobile (PWA)

### 📱 Progressive Web App (PWA)
- **Installation native** : Installable comme une app
- **Mode hors-ligne** : Fonctionne sans connexion
- **Notifications push** : Notifications système
- **Mise à jour automatique** : Service worker intégré
- **Icônes adaptatives** : Icons pour tous les appareils

### 📱 Responsive Design
- **Mobile-first** : Optimisé pour mobile et tablette
- **Navigation adaptative** : Menu qui s'adapte à la taille d'écran
- **Grilles flexibles** : Layout qui s'ajuste automatiquement

## 🎬 Vidéo promotionnelle

### Génération de la vidéo
1. Ouvrez `video-generator.html` dans votre navigateur
2. La vidéo se lance automatiquement après 2 secondes
3. Utilisez les contrôles pour démarrer/pause/reset

### Spécifications de la vidéo
- **Durée** : 30 secondes exactement
- **Résolution** : 1920x1080 (Full HD)
- **Style** : Futuriste avec effets de particules
- **Couleurs** : Dégradés bleu/violet sur fond sombre
- **Animations** : Transitions fluides entre les scènes

### Script de la vidéo
```
🎬 Scène 1 (0-5s): "Bienvenue dans l'avenir."
🎬 Scène 2 (5-10s): Logo + "Voici Nephiris AI — votre nouvelle intelligence artificielle personnelle."
🎬 Scène 3 (10-15s): "Puissante. Intelligente. Visionnaire."
🎬 Scène 4 (15-20s): "Pensée pour vous aider, apprendre avec vous… et évoluer avec vous."
🎬 Scène 5 (20-25s): "Rejoignez l'expérience Nephiris."
🎬 Scène 6 (25-30s): Logo final + "L'intelligence qui vous comprend."
```

### Export de la vidéo
Pour créer le fichier MP4 :

1. **Méthode recommandée - OBS Studio** :
   - Téléchargez OBS Studio (gratuit)
   - Créez une source "Capture de fenêtre" 
   - Sélectionnez la zone du canvas (1920x1080)
   - Enregistrez pendant 30 secondes
   - Exportez en MP4

2. **Méthode alternative - Extension navigateur** :
   - Utilisez une extension comme "Screen Recorder"
   - Capturez l'onglet pendant 30 secondes
   - Téléchargez le fichier MP4

3. **Méthode avancée - Code personnalisé** :
   - Utilisez l'API MediaRecorder du navigateur
   - Capturez automatiquement le canvas
   - Générez le fichier MP4 programmatiquement

## 🚀 Utilisation

### Lancement local
```bash
# Serveur simple Python
python -m http.server 8000

# Ou avec Node.js
npx serve .

# Puis ouvrez http://localhost:8000
```

### Structure des fichiers
```
frontend/
├── index.html           # Page principale
├── style.css            # Styles modernes avec thèmes
├── script.js            # JavaScript interactif avancé
├── video-generator.html # Générateur de vidéo
├── manifest.json        # Manifest PWA
├── sw.js               # Service Worker
├── browserconfig.xml   # Configuration navigateurs
├── assets/             # Ressources
└── README.md           # Cette documentation
```

## 🎯 Fonctionnalités détaillées

### Navigation
- **Header fixe** : Reste visible au scroll avec transparence
- **Toggle thème** : Bouton pour basculer sombre/clair
- **Liens smooth** : Navigation fluide entre sections
- **Responsive menu** : S'adapte aux petits écrans

### Section Hero
- **Layout en grille** : Texte à gauche, vidéo à droite
- **Particules animées** : Effets visuels en arrière-plan
- **Call-to-action** : Boutons d'action principaux
- **Vidéo intégrée** : Lecteur avec fallback élégant

### Section Statistiques
- **4 métriques clés** : Disponibilité, performance, utilisateurs, précision
- **Compteurs animés** : Animation au scroll
- **Design moderne** : Cartes avec effets hover
- **Icônes expressives** : Émojis pour chaque métrique

### Section Fonctionnalités
- **6 cartes interactives** : Chaque fonctionnalité a sa modal
- **Effets hover** : Animations au survol avec sons
- **Boutons d'action** : "Voir l'exemple" pour chaque carte
- **Modals détaillées** : Descriptions complètes

### Section Pricing
- **3 plans** : Gratuit, Pro, Entreprise
- **Plan mis en avant** : Le plan Pro est highlighted
- **Boutons fonctionnels** : Chaque plan a son action
- **Design premium** : Badges et animations

### Chatbot intégré
- **Assistant IA** : Chat en temps réel en bas à droite
- **Interface moderne** : Design avec avatars
- **Réponses intelligentes** : Simulation de conversations
- **Notifications** : Indicateur de nouveaux messages

### Footer
- **4 colonnes** : Produit, Ressources, Entreprise, Légal
- **Liens sociaux** : Twitter, LinkedIn, GitHub avec icônes SVG
- **Statut système** : Indicateur de disponibilité animé
- **Branding cohérent** : Logo et tagline

## 🎨 Personnalisation

### Couleurs
Modifiez les variables CSS dans `style.css` :
```css
:root {
    --primary: #6366f1;      /* Couleur principale */
    --secondary: #8b5cf6;    /* Couleur secondaire */
    --accent: #06b6d4;       /* Couleur d'accent */
    /* ... */
}
```

### Typography
Changez les polices dans les variables :
```css
:root {
    --font-family-primary: 'Inter', sans-serif;
    --font-family-display: 'Space Grotesk', sans-serif;
}
```

### Animations
Ajustez les durées et effets dans les classes CSS correspondantes.

## 🔧 Développement

### Ajout de nouvelles fonctionnalités
1. Modifiez `index.html` pour la structure
2. Ajoutez les styles dans `style.css`
3. Implémentez la logique dans `script.js`

### Tests
- Testez sur différentes tailles d'écran
- Vérifiez les animations et transitions
- Validez l'accessibilité

## 📞 Support

Pour toute question ou amélioration :
- Modifiez directement les fichiers
- Testez les changements localement
- Documentez les nouvelles fonctionnalités

---

**Nephiris AI** - L'intelligence qui vous comprend 🔮
