/* Variables CSS pour les thèmes */
:root {
    /* Thème sombre (par défaut) */
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-card: rgba(255, 255, 255, 0.03);
    --bg-card-hover: rgba(255, 255, 255, 0.06);
    --bg-input: #2d2d3a;
    --bg-message-user: #6366f1;
    --bg-message-ai: transparent;
    
    --text-primary: #ffffff;
    --text-secondary: #a1a1aa;
    --text-tertiary: #71717a;
    --text-muted: #52525b;
    
    --border-primary: rgba(255, 255, 255, 0.1);
    --border-secondary: rgba(255, 255, 255, 0.05);
    --border-input: rgba(255, 255, 255, 0.2);
    
    --primary: #6366f1;
    --primary-hover: #5855eb;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    
    --sidebar-width: 260px;
    --header-height: 60px;
    --input-area-height: auto;
}

/* Thème clair */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-card: rgba(0, 0, 0, 0.02);
    --bg-card-hover: rgba(0, 0, 0, 0.04);
    --bg-input: #ffffff;
    --bg-message-user: #6366f1;
    --bg-message-ai: transparent;
    
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #64748b;
    --text-muted: #94a3b8;
    
    --border-primary: rgba(0, 0, 0, 0.1);
    --border-secondary: rgba(0, 0, 0, 0.05);
    --border-input: rgba(0, 0, 0, 0.15);
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Layout principal */
.chat-container {
    display: flex;
    height: 100vh;
    width: 100vw;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-primary);
}

.new-chat-btn {
    width: 100%;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.new-chat-btn:hover {
    background: var(--bg-card-hover);
    border-color: var(--primary);
}

.conversations-list {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem;
}

.conversation-item {
    padding: 0.75rem;
    margin-bottom: 0.25rem;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    group: hover;
}

.conversation-item:hover {
    background: var(--bg-card);
}

.conversation-item.active {
    background: var(--bg-card-hover);
    border-left: 3px solid var(--primary);
}

.conversation-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.conversation-preview {
    font-size: 0.75rem;
    color: var(--text-secondary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.conversation-time {
    font-size: 0.7rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid var(--border-primary);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-info {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.user-details {
    flex: 1;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.user-plan {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.settings-btn,
.theme-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
}

.settings-btn:hover,
.theme-toggle:hover {
    background: var(--bg-card);
    color: var(--text-primary);
}

/* Zone principale */
.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.chat-header {
    height: var(--header-height);
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    padding: 0 1.5rem;
    gap: 1rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
    display: none;
}

.sidebar-toggle:hover {
    background: var(--bg-card);
    color: var(--text-primary);
}

.chat-title {
    flex: 1;
}

.chat-title h1 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.model-info {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: var(--bg-card);
    color: var(--text-primary);
}

/* Zone des messages */
.messages-container {
    flex: 1;
    overflow-y: auto;
    position: relative;
}

.welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 2rem;
}

.welcome-content {
    text-align: center;
    max-width: 600px;
}

.nephiris-logo {
    margin-bottom: 2rem;
    display: flex;
    justify-content: center;
}

.logo-animation {
    filter: drop-shadow(0 10px 20px rgba(99, 102, 241, 0.3));
}

.welcome-content h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.welcome-content p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.suggestion-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.suggestion-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.suggestion-card:hover {
    background: var(--bg-card-hover);
    border-color: var(--primary);
    transform: translateY(-2px);
}

.suggestion-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.suggestion-text h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.suggestion-text p {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
}

.messages {
    padding: 1rem 0;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
}

/* Messages */
.message {
    margin-bottom: 1.5rem;
    padding: 0 1.5rem;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.message-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: var(--primary);
    color: white;
}

.message.ai .message-avatar {
    background: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
}

.message-author {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-left: auto;
}

.message-content {
    margin-left: 2.25rem;
    line-height: 1.6;
    color: var(--text-primary);
}

.message-actions {
    margin-left: 2.25rem;
    margin-top: 0.75rem;
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.message:hover .message-actions {
    opacity: 1;
}

.message-action-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
    font-size: 0.75rem;
}

.message-action-btn:hover {
    background: var(--bg-card);
    color: var(--text-primary);
}

/* Zone de saisie */
.input-area {
    padding: 1.5rem;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-primary);
}

.input-container {
    max-width: 800px;
    margin: 0 auto;
}

.input-wrapper {
    background: var(--bg-input);
    border: 1px solid var(--border-input);
    border-radius: var(--radius-xl);
    padding: 0.75rem 1rem;
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
    transition: border-color 0.2s ease;
}

.input-wrapper:focus-within {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

#messageInput {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1rem;
    line-height: 1.5;
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 200px;
    font-family: inherit;
}

#messageInput::placeholder {
    color: var(--text-muted);
}

.send-btn {
    background: var(--primary);
    border: none;
    border-radius: var(--radius-lg);
    padding: 0.5rem;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: scale(1.05);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.75rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.character-count {
    color: var(--text-muted);
}

.input-actions {
    display: flex;
    gap: 0.5rem;
}

.usage-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    font-size: 0.75rem;
}

.usage-text {
    color: var(--text-secondary);
}

.upgrade-btn {
    background: none;
    border: none;
    color: var(--primary);
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 500;
    text-decoration: underline;
}

.upgrade-btn:hover {
    color: var(--primary-hover);
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: 0;
        top: 0;
        height: 100vh;
        z-index: 1000;
        transform: translateX(-100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .chat-main {
        width: 100%;
    }
    
    .suggestion-cards {
        grid-template-columns: 1fr;
    }
    
    .input-area {
        padding: 1rem;
    }
    
    .message {
        padding: 0 1rem;
    }
}

/* Indicateur de frappe */
.typing-indicator {
    margin-bottom: 1.5rem;
    padding: 0 1.5rem;
}

.typing-content {
    margin-left: 2.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 0.25rem;
}

.typing-dot {
    width: 6px;
    height: 6px;
    background: var(--text-secondary);
    border-radius: 50%;
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dot:nth-child(3) { animation-delay: 0s; }

@keyframes typingDot {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Formatage du contenu des messages */
.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
    margin: 1rem 0 0.5rem 0;
    color: var(--text-primary);
    font-weight: 600;
}

.message-content h1 { font-size: 1.5rem; }
.message-content h2 { font-size: 1.25rem; }
.message-content h3 { font-size: 1.125rem; }

.message-content p {
    margin: 0.75rem 0;
    line-height: 1.6;
}

.message-content ul,
.message-content ol {
    margin: 0.75rem 0;
    padding-left: 1.5rem;
}

.message-content li {
    margin: 0.25rem 0;
}

.message-content blockquote {
    border-left: 3px solid var(--primary);
    padding-left: 1rem;
    margin: 1rem 0;
    color: var(--text-secondary);
    font-style: italic;
}

.message-content code {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    padding: 0.125rem 0.375rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    color: var(--text-primary);
}

.message-content pre {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: 1rem;
    margin: 1rem 0;
    overflow-x: auto;
    position: relative;
}

.message-content pre code {
    background: none;
    border: none;
    padding: 0;
    font-size: 0.875rem;
    line-height: 1.5;
}

.code-block {
    position: relative;
    margin: 1rem 0;
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-bottom: none;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    padding: 0.75rem 1rem;
}

.code-language {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.copy-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    transition: all 0.2s ease;
}

.copy-btn:hover {
    background: var(--bg-card);
    color: var(--text-primary);
}

.copy-btn.copied {
    color: var(--success);
}

/* Modales */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 2000;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.modal-overlay.show {
    display: flex;
}

.modal {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-primary);
}

.modal-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--bg-card);
    color: var(--text-primary);
}

.modal-content {
    padding: 1.5rem;
    overflow-y: auto;
    max-height: calc(80vh - 120px);
}

/* Notifications */
.notifications-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 400px;
}

.notification {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: 1rem;
    box-shadow: var(--shadow-xl);
    animation: notificationSlideIn 0.3s ease;
    position: relative;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary);
}

.notification.success::before { background: var(--success); }
.notification.warning::before { background: var(--warning); }
.notification.error::before { background: var(--error); }

@keyframes notificationSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.notification-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: var(--bg-card);
    color: var(--text-primary);
}

.notification-content {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* Scrollbars personnalisées */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--border-primary);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Animations globales */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* États de chargement */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.skeleton {
    background: linear-gradient(90deg, var(--bg-card) 25%, var(--bg-card-hover) 50%, var(--bg-card) 75%);
    background-size: 200% 100%;
    animation: skeleton 1.5s infinite;
}

@keyframes skeleton {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Accessibilité */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus visible pour l'accessibilité */
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Amélioration du contraste pour l'accessibilité */
@media (prefers-contrast: high) {
    :root {
        --border-primary: rgba(255, 255, 255, 0.3);
        --border-secondary: rgba(255, 255, 255, 0.2);
        --text-secondary: #d1d5db;
    }
}
