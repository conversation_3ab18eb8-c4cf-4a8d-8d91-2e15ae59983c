import os
import logging
import json
from datetime import datetime
from flask import Flask, request, jsonify, send_from_directory
from dotenv import load_dotenv

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'nephiris_api_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Charger les variables d'environnement
load_dotenv()

app = Flask(__name__)

# Gestionnaire d'historique des conversations
class ConversationHistoryManager:
    def __init__(self):
        self.history_file = 'conversation_history.json'
        self.conversations = self.load_history()
    
    def load_history(self):
        """Charge l'historique des conversations depuis le fichier JSON"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Erreur lors du chargement de l'historique: {e}")
        return []
    
    def save_history(self):
        """Sauvegarde l'historique des conversations dans le fichier JSON"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.conversations, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde de l'historique: {e}")
    
    def add_conversation(self, user_message, ai_response, session_id=None):
        """Ajoute une nouvelle conversation à l'historique"""
        conversation = {
            'id': len(self.conversations) + 1,
            'timestamp': datetime.now().isoformat(),
            'session_id': session_id or 'anonymous',
            'user_message': user_message,
            'ai_response': ai_response
        }
        self.conversations.append(conversation)
        self.save_history()
        logger.info(f"Conversation ajoutée à l'historique: ID {conversation['id']}")
    
    def get_recent_conversations(self, limit=50):
        """Récupère les conversations récentes"""
        return self.conversations[-limit:] if self.conversations else []

# Instance globale du gestionnaire d'historique
history_manager = ConversationHistoryManager()

@app.route('/')
def serve_index():
    """
    Sert le fichier index.html à la racine du site.
    Accessible via http://127.0.0.1:5000/
    """
    return send_from_directory(os.path.join(os.getcwd(), 'frontend'), 'index.html')

@app.route('/<path:filename>')
def serve_static_file(filename):
    """
    Sert tous les fichiers statiques (CSS, JS, images, etc.)
    depuis le dossier 'frontend'.
    """
    return send_from_directory(os.path.join(os.getcwd(), 'frontend'), filename)

@app.route('/chat', methods=['POST'])
def chat_endpoint():
    """
    Point d'entrée pour la discussion.
    Attend un prompt et une liste 'memory' dans le body JSON.
    Renvoie une réponse formatée.
    """
    try:
        data = request.get_json(force=True)
        user_prompt = data.get("prompt", "")
        chat_memory = data.get("memory", [])

        logger.info(f"Nouvelle requête reçue - Prompt: {user_prompt}")
        logger.debug(f"Mémoire de conversation: {chat_memory}")

        if not user_prompt:
            logger.warning("Requête reçue sans prompt")
            return jsonify({"response": "Vous n'avez rien dit."})

        # Récupération de la clé API depuis les variables d'environnement
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key or api_key == 'votre-clé-api-openai-ici':
            logger.error("Clé API OpenAI manquante ou invalide")
            return jsonify({"response": "Configuration OpenAI manquante. Veuillez configurer votre clé API dans le fichier .env"})
        
        # Import OpenAI après vérification de la clé
        from openai import OpenAI
        client = OpenAI(api_key=api_key)

        # Formatage du contexte avec l'historique des messages
        messages = [
            {"role": "system", "content": "Tu es Nephiris AI, un assistant intelligent, utile et bienveillant."}
        ]
        
        for memory in chat_memory:
            if memory.get("role") and memory.get("content"):
                role = "assistant" if memory["role"] == "ai" else "user"
                messages.append({"role": role, "content": memory["content"]})
        
        # Ajout du nouveau message utilisateur
        messages.append({"role": "user", "content": user_prompt})

        logger.info("Envoi de la requête à OpenAI")
        logger.debug(f"Messages envoyés: {messages}")

        # Appel à l'API GPT pour générer une réponse contextuelle
        completion = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=messages,
            temperature=0.7,
            max_tokens=200
        )
        
        # Extraction de la réponse
        ai_response = completion.choices[0].message.content
        logger.info("Réponse reçue de OpenAI")
        logger.debug(f"Réponse: {ai_response}")
        
        # Sauvegarde dans l'historique
        history_manager.add_conversation(user_prompt, ai_response)
        
        return jsonify({
            "response": ai_response,
            "history": history_manager.get_recent_conversations()
        })

    except ImportError as e:
        error_msg = f"Erreur d'importation OpenAI: {str(e)}"
        logger.error(error_msg)
        return jsonify({"response": f"Erreur: le module OpenAI n'est pas installé. Exécutez: pip install openai\nDétails: {str(e)}"})
    except Exception as e:
        error_msg = f"Erreur détaillée: {str(e)}"
        logger.error(error_msg)
        error_msg_lower = str(e).lower()
        if "api_key" in error_msg_lower or "authentication" in error_msg_lower:
            return jsonify({
                "response": f"Erreur: clé API OpenAI invalide. Vérifiez votre configuration dans .env\nDétails: {str(e)}",
                "error_details": str(e)
            })
        elif "rate limit" in error_msg_lower:
            return jsonify({
                "response": f"Trop de requêtes. Veuillez patienter quelques secondes.\nDétails: {str(e)}",
                "error_details": str(e)
            })
        elif "quota" in error_msg_lower:
            return jsonify({
                "response": f"Quota OpenAI dépassé. Vérifiez votre compte OpenAI.\nDétails: {str(e)}",
                "error_details": str(e)
            })
        else:
            return jsonify({
                "response": f"Une erreur est survenue: {str(e)}",
                "error_details": str(e)
            })

if __name__ == "__main__":
    logger.info("Démarrage du serveur Nephiris AI")
    app.run(host="0.0.0.0", port=5000, debug=True)
