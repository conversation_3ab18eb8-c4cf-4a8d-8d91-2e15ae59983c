<!DOCTYPE html>
<html lang="fr" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nephiris AI</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔮</text></svg>">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --bg-primary: #0f0f23;
            --bg-secondary: #1a1a2e;
            --bg-input: #2d2d3a;
            --text-primary: #ffffff;
            --text-secondary: #a1a1aa;
            --text-muted: #52525b;
            --border: rgba(255, 255, 255, 0.1);
            --primary: #6366f1;
            --primary-hover: #5855eb;
            --radius: 12px;
        }

        [data-theme="light"] {
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-input: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #475569;
            --text-muted: #94a3b8;
            --border: rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
        }

        .chat-container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 260px;
            background: var(--bg-secondary);
            border-right: 1px solid var(--border);
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 16px;
            border-bottom: 1px solid var(--border);
        }

        .new-chat-btn {
            width: 100%;
            background: transparent;
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 12px 16px;
            color: var(--text-primary);
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s;
        }

        .new-chat-btn:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .conversations {
            flex: 1;
            overflow-y: auto;
            padding: 8px;
        }

        .conversation-item {
            padding: 12px;
            margin: 2px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .conversation-item:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .conversation-item.active {
            background: rgba(99, 102, 241, 0.1);
            color: var(--text-primary);
        }

        .sidebar-footer {
            padding: 16px;
            border-top: 1px solid var(--border);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-size: 14px;
            font-weight: 500;
        }

        .user-plan {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .theme-toggle {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: all 0.2s;
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            height: 60px;
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            padding: 0 24px;
            justify-content: space-between;
        }

        .chat-title {
            font-size: 18px;
            font-weight: 600;
        }

        .model-badge {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .menu-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            display: none;
        }

        .messages-area {
            flex: 1;
            overflow-y: auto;
            padding: 24px 0;
        }

        .welcome-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            padding: 0 24px;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 24px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .welcome-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .welcome-subtitle {
            font-size: 18px;
            color: var(--text-secondary);
            margin-bottom: 32px;
        }

        .suggestions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            max-width: 800px;
            width: 100%;
        }

        .suggestion-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 20px;
            cursor: pointer;
            transition: all 0.2s;
            text-align: left;
        }

        .suggestion-card:hover {
            border-color: var(--primary);
            transform: translateY(-2px);
        }

        .suggestion-icon {
            font-size: 24px;
            margin-bottom: 12px;
        }

        .suggestion-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .suggestion-desc {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .messages {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 24px;
        }

        .message {
            margin-bottom: 32px;
            display: flex;
            gap: 16px;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: var(--primary);
            color: white;
        }

        .message.ai .message-avatar {
            background: var(--bg-secondary);
            border: 1px solid var(--border);
        }

        .message-content {
            flex: 1;
            line-height: 1.6;
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .message-author {
            font-weight: 600;
            font-size: 14px;
        }

        .message-time {
            font-size: 12px;
            color: var(--text-muted);
        }

        .message-text {
            color: var(--text-primary);
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-secondary);
            font-style: italic;
            margin-top: 8px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: var(--text-secondary);
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        .typing-dot:nth-child(3) { animation-delay: 0s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .input-area {
            padding: 24px;
            border-top: 1px solid var(--border);
        }

        .input-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .input-wrapper {
            background: var(--bg-input);
            border: 1px solid var(--border);
            border-radius: 24px;
            padding: 12px 20px;
            display: flex;
            align-items: flex-end;
            gap: 12px;
            transition: border-color 0.2s;
        }

        .input-wrapper:focus-within {
            border-color: var(--primary);
        }

        .message-input {
            flex: 1;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 16px;
            line-height: 1.5;
            resize: none;
            outline: none;
            min-height: 24px;
            max-height: 200px;
            font-family: inherit;
        }

        .message-input::placeholder {
            color: var(--text-muted);
        }

        .send-btn {
            background: var(--primary);
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            color: white;
        }

        .send-btn:hover:not(:disabled) {
            background: var(--primary-hover);
            transform: scale(1.05);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .input-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 12px;
            font-size: 12px;
            color: var(--text-muted);
        }

        .usage-info {
            color: var(--text-secondary);
        }

        .upgrade-link {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }

        .upgrade-link:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transform: translateX(-100%);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .chat-header {
                padding: 0 16px;
            }

            .menu-btn {
                display: block;
            }

            .suggestions {
                grid-template-columns: 1fr;
            }

            .messages {
                padding: 0 16px;
            }

            .input-area {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <button class="new-chat-btn" id="newChatBtn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 5v14M5 12h14"/>
                    </svg>
                    Nouvelle conversation
                </button>
            </div>

            <div class="conversations" id="conversations">
            </div>

            <div class="sidebar-footer">
                <div class="user-avatar">U</div>
                <div class="user-info">
                    <div class="user-name">Utilisateur</div>
                    <div class="user-plan">Plan Gratuit</div>
                </div>
                <button class="theme-toggle" id="themeToggle">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                    </svg>
                </button>
            </div>
        </div>

        <div class="main-content">
            <div class="chat-header">
                <button class="menu-btn" id="menuBtn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="3" y1="6" x2="21" y2="6"/>
                        <line x1="3" y1="12" x2="21" y2="12"/>
                        <line x1="3" y1="18" x2="21" y2="18"/>
                    </svg>
                </button>
                <div class="chat-title">Nephiris AI</div>
                <div class="model-badge">GPT-4 Turbo</div>
            </div>

            <div class="messages-area" id="messagesArea">
                <div class="welcome-screen" id="welcomeScreen">
                    <div class="logo">🔮</div>
                    <h1 class="welcome-title">Bonjour ! Je suis Nephiris AI</h1>
                    <p class="welcome-subtitle">Comment puis-je vous aider aujourd'hui ?</p>

                    <div class="suggestions">
                        <div class="suggestion-card" data-prompt="Explique-moi le concept d'intelligence artificielle">
                            <div class="suggestion-icon">🧠</div>
                            <div class="suggestion-title">Expliquer l'IA</div>
                            <div class="suggestion-desc">Comprendre l'intelligence artificielle</div>
                        </div>

                        <div class="suggestion-card" data-prompt="Aide-moi à rédiger un email professionnel">
                            <div class="suggestion-icon">✉️</div>
                            <div class="suggestion-title">Rédiger un email</div>
                            <div class="suggestion-desc">Communication professionnelle</div>
                        </div>

                        <div class="suggestion-card" data-prompt="Crée-moi un plan d'étude pour apprendre Python">
                            <div class="suggestion-icon">📚</div>
                            <div class="suggestion-title">Plan d'apprentissage</div>
                            <div class="suggestion-desc">Organiser mes études</div>
                        </div>

                        <div class="suggestion-card" data-prompt="Génère des idées créatives pour un projet">
                            <div class="suggestion-icon">💡</div>
                            <div class="suggestion-title">Brainstorming</div>
                            <div class="suggestion-desc">Idées créatives et innovation</div>
                        </div>
                    </div>
                </div>

                <div class="messages" id="messages" style="display: none;">
                </div>
            </div>

            <div class="input-area">
                <div class="input-container">
                    <div class="input-wrapper">
                        <textarea
                            class="message-input"
                            id="messageInput"
                            placeholder="Envoyez un message à Nephiris AI..."
                            rows="1"
                        ></textarea>
                        <button class="send-btn" id="sendBtn" disabled>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 2L11 13"/>
                                <path d="M22 2L15 22L11 13L2 9L22 2Z"/>
                            </svg>
                        </button>
                    </div>
                    <div class="input-footer">
                        <div class="usage-info">Plan Gratuit: <span id="messageCount">10</span>/15 messages aujourd'hui</div>
                        <a href="#" class="upgrade-link">Passer au Premium</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class NephirisAI {
            constructor() {
                this.conversations = JSON.parse(localStorage.getItem('nephiris_conversations') || '[]');
                this.currentConversationId = null;
                this.messageCount = parseInt(localStorage.getItem('nephiris_message_count') || '0');
                this.maxMessages = 15;
                this.isTyping = false;

                this.init();
            }

            init() {
                this.setupEventListeners();
                this.setupTheme();
                this.loadConversations();
                this.updateMessageCount();
                this.setupTextareaResize();

                if (this.conversations.length > 0) {
                    this.loadConversation(this.conversations[0].id);
                }
            }

            setupEventListeners() {
                document.getElementById('newChatBtn').addEventListener('click', () => this.newConversation());
                document.getElementById('sendBtn').addEventListener('click', () => this.sendMessage());
                document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());
                document.getElementById('menuBtn').addEventListener('click', () => this.toggleSidebar());

                const messageInput = document.getElementById('messageInput');
                messageInput.addEventListener('input', () => this.handleInputChange());
                messageInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                document.querySelectorAll('.suggestion-card').forEach(card => {
                    card.addEventListener('click', () => {
                        const prompt = card.dataset.prompt;
                        messageInput.value = prompt;
                        this.handleInputChange();
                        this.sendMessage();
                    });
                });
            }

            setupTextareaResize() {
                const textarea = document.getElementById('messageInput');
                textarea.addEventListener('input', () => {
                    textarea.style.height = 'auto';
                    textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
                });
            }

            setupTheme() {
                const savedTheme = localStorage.getItem('nephiris_theme') || 'dark';
                document.documentElement.setAttribute('data-theme', savedTheme);
                this.updateThemeIcon(savedTheme);
            }

            toggleTheme() {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('nephiris_theme', newTheme);
                this.updateThemeIcon(newTheme);
            }

            updateThemeIcon(theme) {
                const themeToggle = document.getElementById('themeToggle');
                const icon = theme === 'dark'
                    ? '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="5"/><path d="M12 1v2m0 18v2M4.22 4.22l1.42 1.42m12.72 12.72l1.42 1.42M1 12h2m18 0h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/></svg>'
                    : '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/></svg>';
                themeToggle.innerHTML = icon;
            }

            toggleSidebar() {
                const sidebar = document.getElementById('sidebar');
                sidebar.classList.toggle('open');
            }

            handleInputChange() {
                const messageInput = document.getElementById('messageInput');
                const sendBtn = document.getElementById('sendBtn');

                sendBtn.disabled = !messageInput.value.trim() || this.isTyping;
            }

            newConversation() {
                const conversationId = this.generateId();
                const conversation = {
                    id: conversationId,
                    title: 'Nouvelle conversation',
                    messages: [],
                    createdAt: new Date().toISOString()
                };

                this.conversations.unshift(conversation);
                this.saveConversations();
                this.loadConversations();
                this.loadConversation(conversationId);

                document.getElementById('messageInput').focus();
            }

            async sendMessage() {
                const messageInput = document.getElementById('messageInput');
                const content = messageInput.value.trim();

                if (!content || this.isTyping) return;

                if (this.messageCount >= this.maxMessages) {
                    alert('Limite de messages atteinte ! Passez au Premium pour continuer.');
                    return;
                }

                if (!this.currentConversationId) {
                    this.newConversation();
                }

                this.addMessage('user', content);
                messageInput.value = '';
                this.handleInputChange();
                this.hideWelcomeScreen();

                this.messageCount++;
                localStorage.setItem('nephiris_message_count', this.messageCount.toString());
                this.updateMessageCount();

                this.showTypingIndicator();

                try {
                    const response = await this.callAPI(content);
                    this.hideTypingIndicator();
                    this.addMessage('ai', response);
                } catch (error) {
                    this.hideTypingIndicator();
                    this.addMessage('ai', 'Désolé, une erreur s\'est produite. Veuillez réessayer.');
                }

                this.updateConversationTitle();
            }

            async callAPI(message) {
                return new Promise((resolve) => {
                    setTimeout(() => {
                        const responses = [
                            "Je comprends votre question. Voici une réponse détaillée qui prend en compte tous les aspects de votre demande. L'intelligence artificielle est un domaine fascinant qui continue d'évoluer rapidement.",
                            "Excellente question ! Laissez-moi vous expliquer cela de manière claire et structurée. Il y a plusieurs points importants à considérer dans cette situation.",
                            "Voici ce que je peux vous dire à ce sujet. Il y a plusieurs approches possibles, et je vais vous présenter les plus efficaces selon votre contexte.",
                            "Je vais vous aider avec cela. Voici une approche étape par étape pour résoudre votre problème de manière efficace et durable.",
                            "C'est un sujet très intéressant ! Permettez-moi de vous donner une explication complète avec des exemples concrets pour mieux illustrer le concept."
                        ];

                        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                        resolve(randomResponse);
                    }, 1500 + Math.random() * 2000);
                });
            }

            addMessage(type, content) {
                const conversation = this.getCurrentConversation();
                if (!conversation) return;

                const message = {
                    id: this.generateId(),
                    type,
                    content,
                    timestamp: new Date().toISOString()
                };

                conversation.messages.push(message);
                this.saveConversations();
                this.renderMessage(message);
                this.scrollToBottom();
            }

            renderMessage(message) {
                const messagesContainer = document.getElementById('messages');
                const messageElement = document.createElement('div');
                messageElement.className = `message ${message.type}`;

                const avatar = message.type === 'user' ? 'U' : '🔮';
                const author = message.type === 'user' ? 'Vous' : 'Nephiris AI';
                const time = this.formatTime(message.timestamp);

                messageElement.innerHTML = `
                    <div class="message-avatar">${avatar}</div>
                    <div class="message-content">
                        <div class="message-header">
                            <div class="message-author">${author}</div>
                            <div class="message-time">${time}</div>
                        </div>
                        <div class="message-text">${this.formatContent(message.content)}</div>
                    </div>
                `;

                messagesContainer.appendChild(messageElement);
            }

            formatContent(content) {
                return content
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/`(.*?)`/g, '<code style="background: var(--bg-secondary); padding: 2px 6px; border-radius: 4px; font-family: monospace;">$1</code>')
                    .replace(/\n/g, '<br>');
            }

            showTypingIndicator() {
                this.isTyping = true;
                const messagesContainer = document.getElementById('messages');

                const typingElement = document.createElement('div');
                typingElement.className = 'message ai';
                typingElement.id = 'typingIndicator';

                typingElement.innerHTML = `
                    <div class="message-avatar">🔮</div>
                    <div class="message-content">
                        <div class="message-header">
                            <div class="message-author">Nephiris AI</div>
                        </div>
                        <div class="typing-indicator">
                            <span>est en train d'écrire</span>
                            <div class="typing-dots">
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                            </div>
                        </div>
                    </div>
                `;

                messagesContainer.appendChild(typingElement);
                this.scrollToBottom();
            }

            hideTypingIndicator() {
                this.isTyping = false;
                const typingIndicator = document.getElementById('typingIndicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }

            hideWelcomeScreen() {
                document.getElementById('welcomeScreen').style.display = 'none';
                document.getElementById('messages').style.display = 'block';
            }

            showWelcomeScreen() {
                document.getElementById('welcomeScreen').style.display = 'flex';
                document.getElementById('messages').style.display = 'none';
            }

            loadConversations() {
                const conversationsContainer = document.getElementById('conversations');
                conversationsContainer.innerHTML = '';

                this.conversations.forEach(conversation => {
                    const conversationElement = document.createElement('div');
                    conversationElement.className = 'conversation-item';
                    conversationElement.textContent = conversation.title;

                    if (conversation.id === this.currentConversationId) {
                        conversationElement.classList.add('active');
                    }

                    conversationElement.addEventListener('click', () => {
                        this.loadConversation(conversation.id);
                    });

                    conversationsContainer.appendChild(conversationElement);
                });
            }

            loadConversation(conversationId) {
                this.currentConversationId = conversationId;
                const conversation = this.getCurrentConversation();

                if (!conversation) return;

                this.updateActiveConversation();
                this.renderMessages(conversation.messages);

                if (conversation.messages.length === 0) {
                    this.showWelcomeScreen();
                } else {
                    this.hideWelcomeScreen();
                }
            }

            updateActiveConversation() {
                document.querySelectorAll('.conversation-item').forEach(item => {
                    item.classList.remove('active');
                });

                const activeItems = document.querySelectorAll('.conversation-item');
                const activeIndex = this.conversations.findIndex(conv => conv.id === this.currentConversationId);
                if (activeItems[activeIndex]) {
                    activeItems[activeIndex].classList.add('active');
                }
            }

            renderMessages(messages) {
                const messagesContainer = document.getElementById('messages');
                messagesContainer.innerHTML = '';

                messages.forEach(message => {
                    this.renderMessage(message);
                });

                this.scrollToBottom();
            }

            updateConversationTitle() {
                const conversation = this.getCurrentConversation();
                if (!conversation || conversation.messages.length === 0) return;

                const firstUserMessage = conversation.messages.find(msg => msg.type === 'user');
                if (firstUserMessage && conversation.title === 'Nouvelle conversation') {
                    conversation.title = firstUserMessage.content.length > 30 ?
                        firstUserMessage.content.substring(0, 30) + '...' :
                        firstUserMessage.content;

                    this.saveConversations();
                    this.loadConversations();
                }
            }

            updateMessageCount() {
                const messageCountElement = document.getElementById('messageCount');
                const remaining = Math.max(0, this.maxMessages - this.messageCount);
                messageCountElement.textContent = remaining;

                if (remaining <= 3) {
                    messageCountElement.style.color = '#f59e0b';
                }
                if (remaining === 0) {
                    messageCountElement.style.color = '#ef4444';
                }
            }

            scrollToBottom() {
                const messagesArea = document.getElementById('messagesArea');
                messagesArea.scrollTop = messagesArea.scrollHeight;
            }

            getCurrentConversation() {
                return this.conversations.find(conv => conv.id === this.currentConversationId);
            }

            saveConversations() {
                localStorage.setItem('nephiris_conversations', JSON.stringify(this.conversations));
            }

            formatTime(timestamp) {
                const date = new Date(timestamp);
                return date.toLocaleTimeString('fr-FR', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }

            generateId() {
                return Date.now().toString(36) + Math.random().toString(36).substr(2);
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            new NephirisAI();
        });
    </script>
</body>
</html>