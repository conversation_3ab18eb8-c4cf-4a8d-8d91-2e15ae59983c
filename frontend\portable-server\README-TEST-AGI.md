# 🔮 NEPHIRIS AI - INTERFACE DE TEST AGI

## 🎯 **OBJECTIF**
Interface portable pour tester votre AGI sur **127.0.0.1:5000** sans installation Node.js

---

## 🚀 **DÉMARRAGE ULTRA-SIMPLE**

### **Méthode 1 : Script Automatique**
1. **Double-cliquez** sur `start-server.bat`
2. **Attendez** que le serveur démarre
3. **L'interface s'ouvre** automatiquement dans votre navigateur
4. **URL** : http://127.0.0.1:5000

### **Méthode 2 : Manuel**
1. **Ouvrez un terminal** dans ce dossier
2. **Tapez** : `python server.py`
3. **Ouvrez** : http://127.0.0.1:5000

---

## 🔌 **CONNEXION DE VOTRE AGI**

### **API Endpoint**
Votre AGI doit répondre aux requêtes POST sur :
```
http://127.0.0.1:5000/api/chat
```

### **Format de Requête**
```json
{
  "message": "Bon<PERSON>r, comment allez-vous ?"
}
```

### **Format de Réponse**
```json
{
  "message": "Je vais bien, merci ! Comment puis-je vous aider ?",
  "timestamp": 1703123456.789
}
```

---

## 🧪 **TESTS SUGGÉRÉS**

### **1. Test de Base**
- "Bonjour, peux-tu te présenter ?"
- "Quelles sont tes capacités ?"

### **2. Test Logique**
- "Résous : 2x + 5 = 15"
- "Explique le paradoxe du grand-père"

### **3. Test Créatif**
- "Écris un poème sur l'IA"
- "Invente une histoire courte"

### **4. Test Technique**
- "Explique le machine learning"
- "Code une fonction Python pour trier une liste"

---

## ⚙️ **CONFIGURATION**

### **Changer le Port**
Modifiez dans `server.py` :
```python
PORT = 5000  # Changez ici
```

### **Personnaliser l'Interface**
Modifiez `nephiris-chat.html` pour :
- Changer les couleurs
- Ajouter des fonctionnalités
- Modifier les suggestions

---

## 🔧 **INTÉGRATION AVEC VOTRE AGI**

### **Option 1 : AGI comme Serveur**
Votre AGI écoute sur un port et répond aux requêtes HTTP

### **Option 2 : AGI comme Client**
Votre AGI se connecte à cette interface via WebSocket

### **Option 3 : AGI Intégré**
Modifiez `server.py` pour appeler directement votre AGI

---

## 📊 **FONCTIONNALITÉS DE L'INTERFACE**

### ✅ **Interface Moderne**
- Design ChatGPT-style
- Responsive design
- Animations fluides

### ✅ **Test Complet**
- Historique des conversations
- Suggestions de test
- Indicateurs de frappe
- Timestamps

### ✅ **Monitoring**
- Statut de connexion
- Temps de réponse
- Gestion d'erreurs

---

## 🛠️ **DÉPANNAGE**

### **Problème : "Python n'est pas reconnu"**
**Solution** : Installer Python depuis https://python.org/

### **Problème : "Port 5000 occupé"**
**Solution** : Changer le port dans `server.py`

### **Problème : "Connexion refusée"**
**Solution** : Vérifier que votre AGI écoute sur le bon port

---

## 📱 **UTILISATION**

1. **Démarrez** le serveur avec `start-server.bat`
2. **Connectez** votre AGI à l'API
3. **Testez** avec les suggestions ou vos propres questions
4. **Analysez** les réponses de votre AGI
5. **Itérez** pour améliorer les performances

---

## 🔮 **AVANTAGES**

- ✅ **Aucune installation** Node.js requise
- ✅ **Fonctionne** sur 127.0.0.1:5000
- ✅ **Interface professionnelle** pour démonstrations
- ✅ **API simple** pour intégration
- ✅ **Portable** - fonctionne partout
- ✅ **Personnalisable** selon vos besoins

---

## 📞 **SUPPORT**

Si vous avez des questions sur l'intégration de votre AGI :
1. Vérifiez que Python est installé
2. Testez d'abord avec l'AGI simulée
3. Adaptez l'API selon vos besoins
4. Personnalisez l'interface si nécessaire

**Prêt à tester votre AGI !** 🚀
