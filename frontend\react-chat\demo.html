<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nephiris AI - Demo React</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .demo-container {
            text-align: center;
            max-width: 800px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .logo {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #a1a1aa;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 2rem 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        
        .feature:hover {
            transform: translateY(-5px);
            border-color: #6366f1;
            box-shadow: 0 10px 25px rgba(99, 102, 241, 0.2);
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .feature-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .feature-desc {
            font-size: 0.9rem;
            color: #a1a1aa;
        }
        
        .instructions {
            background: rgba(99, 102, 241, 0.1);
            border: 1px solid rgba(99, 102, 241, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin: 2rem 0;
        }
        
        .instructions h3 {
            color: #6366f1;
            margin-bottom: 1rem;
        }
        
        .step {
            margin: 0.5rem 0;
            padding-left: 1rem;
            position: relative;
        }
        
        .step::before {
            content: "→";
            position: absolute;
            left: 0;
            color: #6366f1;
        }
        
        .command {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Cascadia Code', monospace;
            font-size: 0.9rem;
            margin: 10px 0;
            border-left: 3px solid #6366f1;
        }
        
        .note {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 1rem 0;
            color: #fbbf24;
        }
        
        .version {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(99, 102, 241, 0.2);
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            border: 1px solid rgba(99, 102, 241, 0.3);
        }
    </style>
</head>
<body>
    <div class="version">v2.1.0</div>
    
    <div class="demo-container">
        <div class="logo">🔮</div>
        <h1 class="title">Nephiris AI React Chat</h1>
        <p class="subtitle">
            Interface de chat moderne et avancée avec reconnaissance vocale, 
            thèmes dynamiques, et fonctionnalités intelligentes
        </p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🎨</div>
                <div class="feature-title">Interface Moderne</div>
                <div class="feature-desc">Design ChatGPT-style avec animations fluides</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🎤</div>
                <div class="feature-title">Reconnaissance Vocale</div>
                <div class="feature-desc">Parlez directement à l'IA</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">💾</div>
                <div class="feature-title">Historique Persistant</div>
                <div class="feature-desc">Conversations sauvegardées automatiquement</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🌙</div>
                <div class="feature-title">Thèmes Dynamiques</div>
                <div class="feature-desc">Mode sombre/clair avec transition</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📱</div>
                <div class="feature-title">PWA Ready</div>
                <div class="feature-desc">Installable comme application native</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">Performance</div>
                <div class="feature-desc">Optimisé avec React 18 et Framer Motion</div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>🚀 Instructions de Démarrage</h3>
            
            <div class="step">Ouvrir un terminal dans le dossier frontend/react-chat</div>
            <div class="command">cd frontend/react-chat</div>
            
            <div class="step">Installer les dépendances (première fois seulement)</div>
            <div class="command">npm install</div>
            
            <div class="step">Démarrer l'application</div>
            <div class="command">npm start</div>
            
            <div class="step">Ouvrir dans le navigateur</div>
            <div class="command">http://127.0.0.1:5000</div>
        </div>
        
        <div class="note">
            <strong>📌 Note :</strong> L'application est configurée pour fonctionner sur 127.0.0.1:5000 
            comme vous l'avez demandé. Si le port est occupé, elle utilisera automatiquement le suivant disponible.
        </div>
        
        <div style="margin-top: 2rem; font-size: 0.9rem; color: #6b7280;">
            <p>✨ Fonctionnalités avancées incluses :</p>
            <p>Recherche dans l'historique • Export des conversations • Mode focus • Statistiques • Actions contextuelles</p>
        </div>
    </div>
</body>
</html>
