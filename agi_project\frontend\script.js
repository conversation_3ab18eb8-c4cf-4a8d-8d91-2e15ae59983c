document.getElementById('testApiBtn').addEventListener('click', async () => {
    const responseElement = document.getElementById('apiResponse');
    responseElement.textContent = 'Chargement...';

    try {
        const response = await fetch('http://127.0.0.1:8000/');
        if (!response.ok) {
            throw new Error('Erreur réseau');
        }
        const data = await response.json();
        responseElement.textContent = data.message;
    } catch (error) {
        responseElement.textContent = 'Erreur lors de la requête API';
        console.error(error);
    }
});

const chatMessages = document.getElementById('chatMessages');
const chatInput = document.getElementById('chatInput');
const sendBtn = document.getElementById('sendBtn');
const sidebar = document.querySelector('.sidebar');
const menuBtn = document.querySelector('.menu-btn');
const sidebarLinks = document.querySelectorAll('.sidebar nav ul li a');
const topButtons = document.querySelectorAll('.top-btn');

function appendMessage(sender, message, className) {
    const messageElem = document.createElement('div');
    messageElem.textContent = message;
    messageElem.classList.add('message');
    if (className) {
        messageElem.classList.add(className);
    }
    chatMessages.appendChild(messageElem);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

sendBtn.addEventListener('click', async () => {
    const userMessage = chatInput.value.trim();
    if (!userMessage) return;
    appendMessage('Vous', userMessage, 'user');
    chatInput.value = '';
    appendMessage('AGI', '...', 'agi'); // Placeholder for response

    try {
        const response = await fetch('http://127.0.0.1:8000/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ message: userMessage })
        });
        if (!response.ok) {
            throw new Error('Erreur réseau');
        }
        const data = await response.json();
        chatMessages.lastChild.textContent = data.response;
    } catch (error) {
        chatMessages.lastChild.textContent = 'Erreur lors de la requête API';
        console.error(error);
    }
});

// Send message on Enter key
chatInput.addEventListener('keydown', (e) => {
    if (e.key === 'Enter') {
        sendBtn.click();
    }
});

// Toggle sidebar visibility
menuBtn.addEventListener('click', () => {
    sidebar.classList.toggle('collapsed');
});

// Sidebar navigation active link
sidebarLinks.forEach(link => {
    link.addEventListener('click', (e) => {
        e.preventDefault();
        sidebarLinks.forEach(l => l.classList.remove('active'));
        link.classList.add('active');
        // Placeholder for navigation action
        alert(`Navigation vers ${link.textContent} (fonctionnalité à implémenter)`);
    });
});

// Top bar buttons functionality
topButtons.forEach(button => {
    button.addEventListener('click', () => {
        alert(`Bouton "${button.textContent.trim()}" cliqué (fonctionnalité à implémenter)`);
    });
});
