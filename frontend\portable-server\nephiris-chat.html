<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nephiris AI - Test AGI</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #0f0f23;
            color: white;
            height: 100vh;
            display: flex;
        }
        
        .sidebar {
            width: 280px;
            background: #1a1a2e;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
        }
        
        .logo {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 700;
            font-size: 18px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .logo-icon {
            font-size: 24px;
            filter: drop-shadow(0 0 10px rgba(99, 102, 241, 0.5));
        }
        
        .new-chat-btn {
            margin: 16px;
            padding: 12px 16px;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: transparent;
            color: white;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
        }
        
        .new-chat-btn:hover {
            background: #6366f1;
            transform: translateY(-1px);
        }
        
        .conversations {
            flex: 1;
            overflow-y: auto;
            padding: 8px 16px;
        }
        
        .conversation-item {
            padding: 12px;
            margin: 4px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            color: #a1a1aa;
        }
        
        .conversation-item:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .conversation-item.active {
            background: rgba(99, 102, 241, 0.2);
            color: white;
        }
        
        .sidebar-footer {
            padding: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            font-size: 14px;
            font-weight: 600;
        }
        
        .user-plan {
            font-size: 12px;
            color: #a1a1aa;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .chat-header {
            height: 60px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            padding: 0 24px;
            justify-content: space-between;
        }
        
        .chat-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .status-badge {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            border: 1px solid #10b981;
        }
        
        .messages-area {
            flex: 1;
            overflow-y: auto;
            padding: 24px 0;
        }
        
        .welcome-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            padding: 24px;
        }
        
        .welcome-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 24px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .welcome-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .welcome-subtitle {
            font-size: 16px;
            color: #a1a1aa;
            margin-bottom: 32px;
        }
        
        .suggestions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            max-width: 600px;
            width: 100%;
        }
        
        .suggestion-card {
            background: #1a1a2e;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: left;
        }
        
        .suggestion-card:hover {
            border-color: #6366f1;
            transform: translateY(-2px);
        }
        
        .suggestion-icon {
            font-size: 20px;
            margin-bottom: 8px;
        }
        
        .suggestion-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .suggestion-desc {
            font-size: 12px;
            color: #a1a1aa;
        }
        
        .messages {
            max-width: 700px;
            margin: 0 auto;
            padding: 0 24px;
            display: none;
        }
        
        .message {
            margin-bottom: 24px;
            display: flex;
            gap: 12px;
            opacity: 0;
            animation: fadeInUp 0.3s ease-out forwards;
        }
        
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .message-avatar {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .message.user .message-avatar {
            background: #6366f1;
            color: white;
        }
        
        .message.ai .message-avatar {
            background: #1a1a2e;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .message-content {
            flex: 1;
        }
        
        .message-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }
        
        .message-author {
            font-weight: 600;
            font-size: 13px;
        }
        
        .message-time {
            font-size: 11px;
            color: #666;
        }
        
        .message-text {
            line-height: 1.5;
            font-size: 14px;
        }
        
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #a1a1aa;
            font-style: italic;
        }
        
        .typing-dots {
            display: flex;
            gap: 3px;
        }
        
        .typing-dot {
            width: 4px;
            height: 4px;
            background: #a1a1aa;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        .typing-dot:nth-child(3) { animation-delay: 0s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
        
        .input-area {
            padding: 24px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .input-container {
            max-width: 700px;
            margin: 0 auto;
        }
        
        .input-wrapper {
            background: #2d2d3a;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.2s ease;
        }
        
        .input-wrapper:focus-within {
            border-color: #6366f1;
        }
        
        .message-input {
            flex: 1;
            background: none;
            border: none;
            color: white;
            font-size: 14px;
            outline: none;
            resize: none;
            min-height: 20px;
            max-height: 120px;
            font-family: inherit;
        }
        
        .message-input::placeholder {
            color: #666;
        }
        
        .send-btn {
            background: #6366f1;
            border: none;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: white;
            transition: all 0.2s ease;
        }
        
        .send-btn:hover {
            background: #5855eb;
            transform: scale(1.05);
        }
        
        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .input-footer {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 8px;
            font-size: 11px;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .sidebar { display: none; }
            .suggestions { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="logo">
            <span class="logo-icon">🔮</span>
            <span>Nephiris AI</span>
        </div>
        
        <button class="new-chat-btn" onclick="newConversation()">
            + Nouvelle conversation
        </button>
        
        <div class="conversations" id="conversations"></div>
        
        <div class="sidebar-footer">
            <div class="user-avatar">T</div>
            <div class="user-info">
                <div class="user-name">Testeur AGI</div>
                <div class="user-plan">Mode Test</div>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="chat-header">
            <div class="chat-title">Test AGI - Nephiris AI</div>
            <div class="status-badge">🟢 Connecté</div>
        </div>

        <div class="messages-area">
            <div class="welcome-screen" id="welcomeScreen">
                <div class="welcome-logo">🔮</div>
                <h1 class="welcome-title">Test AGI - Nephiris AI</h1>
                <p class="welcome-subtitle">Interface de test pour votre AGI sur 127.0.0.1:5000</p>
                
                <div class="suggestions">
                    <div class="suggestion-card" onclick="sendSuggestion('Bonjour, peux-tu te présenter ?')">
                        <div class="suggestion-icon">👋</div>
                        <div class="suggestion-title">Présentation</div>
                        <div class="suggestion-desc">Demander à l'AGI de se présenter</div>
                    </div>
                    
                    <div class="suggestion-card" onclick="sendSuggestion('Quelles sont tes capacités ?')">
                        <div class="suggestion-icon">🧠</div>
                        <div class="suggestion-title">Capacités</div>
                        <div class="suggestion-desc">Explorer les fonctionnalités de l'AGI</div>
                    </div>
                    
                    <div class="suggestion-card" onclick="sendSuggestion('Résous ce problème : 2x + 5 = 15')">
                        <div class="suggestion-icon">🔢</div>
                        <div class="suggestion-title">Test Mathématiques</div>
                        <div class="suggestion-desc">Tester les capacités de calcul</div>
                    </div>
                    
                    <div class="suggestion-card" onclick="sendSuggestion('Écris un court poème sur l\\'intelligence artificielle')">
                        <div class="suggestion-icon">✍️</div>
                        <div class="suggestion-title">Test Créatif</div>
                        <div class="suggestion-desc">Évaluer la créativité</div>
                    </div>
                </div>
            </div>

            <div class="messages" id="messages"></div>
        </div>

        <div class="input-area">
            <div class="input-container">
                <div class="input-wrapper">
                    <textarea 
                        class="message-input" 
                        id="messageInput" 
                        placeholder="Testez votre AGI ici..."
                        rows="1"
                        onkeydown="handleKeyDown(event)"
                        oninput="handleInput()"
                    ></textarea>
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()" disabled>
                        ➤
                    </button>
                </div>
                <div class="input-footer">
                    <div>Interface de test AGI - 127.0.0.1:5000</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let conversations = [];
        let currentConversationId = null;
        let isTyping = false;

        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        function handleInput() {
            const input = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');

            // Auto-resize
            input.style.height = 'auto';
            input.style.height = Math.min(input.scrollHeight, 120) + 'px';

            // Enable/disable send button
            const hasText = input.value.trim().length > 0;
            sendBtn.disabled = !hasText || isTyping;

            // Change button style when enabled
            if (hasText && !isTyping) {
                sendBtn.style.background = '#6366f1';
                sendBtn.style.opacity = '1';
                sendBtn.style.cursor = 'pointer';
            } else {
                sendBtn.style.background = '#666';
                sendBtn.style.opacity = '0.5';
                sendBtn.style.cursor = 'not-allowed';
            }
        }

        function newConversation() {
            const id = Date.now().toString();
            const conversation = {
                id: id,
                title: 'Test AGI',
                messages: []
            };
            
            conversations.unshift(conversation);
            loadConversation(id);
            updateConversationsList();
        }

        function sendSuggestion(text) {
            document.getElementById('messageInput').value = text;
            handleInput();
            sendMessage();
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const content = input.value.trim();
            
            if (!content || isTyping) return;

            if (!currentConversationId) {
                newConversation();
            }

            addMessage('user', content);
            input.value = '';
            handleInput();
            hideWelcomeScreen();

            showTypingIndicator();

            try {
                // Appel à votre AGI via l'API
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: content })
                });

                const data = await response.json();
                hideTypingIndicator();
                addMessage('ai', data.message || 'Réponse de l\'AGI');
            } catch (error) {
                hideTypingIndicator();
                addMessage('ai', 'Erreur de connexion avec l\'AGI. Vérifiez que votre AGI est bien connectée sur ce port.');
            }

            updateConversationTitle();
        }

        function addMessage(type, content) {
            const conversation = getCurrentConversation();
            if (!conversation) return;

            const message = {
                id: Date.now().toString(),
                type: type,
                content: content,
                timestamp: new Date().toISOString()
            };

            conversation.messages.push(message);
            renderMessage(message);
            scrollToBottom();
        }

        function renderMessage(message) {
            const messagesContainer = document.getElementById('messages');
            const messageElement = document.createElement('div');
            messageElement.className = `message ${message.type}`;

            const avatar = message.type === 'user' ? 'T' : '🔮';
            const author = message.type === 'user' ? 'Testeur' : 'AGI';
            const time = new Date(message.timestamp).toLocaleTimeString('fr-FR', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });

            messageElement.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">
                    <div class="message-header">
                        <div class="message-author">${author}</div>
                        <div class="message-time">${time}</div>
                    </div>
                    <div class="message-text">${content}</div>
                </div>
            `;

            messagesContainer.appendChild(messageElement);
        }

        function showTypingIndicator() {
            isTyping = true;
            const messagesContainer = document.getElementById('messages');
            
            const typingElement = document.createElement('div');
            typingElement.className = 'message ai';
            typingElement.id = 'typingIndicator';
            
            typingElement.innerHTML = `
                <div class="message-avatar">🔮</div>
                <div class="message-content">
                    <div class="message-header">
                        <div class="message-author">AGI</div>
                    </div>
                    <div class="typing-indicator">
                        <span>traite votre demande</span>
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>
            `;
            
            messagesContainer.appendChild(typingElement);
            scrollToBottom();
        }

        function hideTypingIndicator() {
            isTyping = false;
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        function hideWelcomeScreen() {
            document.getElementById('welcomeScreen').style.display = 'none';
            document.getElementById('messages').style.display = 'block';
        }

        function loadConversation(conversationId) {
            currentConversationId = conversationId;
            const conversation = getCurrentConversation();
            
            if (!conversation) return;

            const messagesContainer = document.getElementById('messages');
            messagesContainer.innerHTML = '';
            
            conversation.messages.forEach(message => {
                renderMessage(message);
            });
            
            if (conversation.messages.length === 0) {
                document.getElementById('welcomeScreen').style.display = 'flex';
                document.getElementById('messages').style.display = 'none';
            } else {
                hideWelcomeScreen();
            }
            
            updateConversationsList();
            scrollToBottom();
        }

        function updateConversationsList() {
            const container = document.getElementById('conversations');
            container.innerHTML = '';

            conversations.forEach(conversation => {
                const element = document.createElement('div');
                element.className = 'conversation-item';
                element.textContent = conversation.title;
                
                if (conversation.id === currentConversationId) {
                    element.classList.add('active');
                }

                element.onclick = () => loadConversation(conversation.id);
                container.appendChild(element);
            });
        }

        function updateConversationTitle() {
            const conversation = getCurrentConversation();
            if (!conversation || conversation.messages.length === 0) return;

            const firstUserMessage = conversation.messages.find(msg => msg.type === 'user');
            if (firstUserMessage && conversation.title === 'Test AGI') {
                conversation.title = firstUserMessage.content.length > 30 ? 
                    firstUserMessage.content.substring(0, 30) + '...' : 
                    firstUserMessage.content;
                
                updateConversationsList();
            }
        }

        function scrollToBottom() {
            const messagesArea = document.querySelector('.messages-area');
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function getCurrentConversation() {
            return conversations.find(conv => conv.id === currentConversationId);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            handleInput();

            // Focus on input
            document.getElementById('messageInput').focus();

            // Add event listener for input changes
            document.getElementById('messageInput').addEventListener('input', handleInput);
            document.getElementById('messageInput').addEventListener('keyup', handleInput);
        });
    </script>
</body>
</html>
