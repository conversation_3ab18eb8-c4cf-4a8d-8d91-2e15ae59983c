// Nephiris AI Frontend JavaScript
class NephirisApp {
    constructor() {
        this.init();
    }

    init() {
        this.showLoadingScreen();

        this.setupNavigation();
        this.setupFeatureCards();
        this.setupPricingCards();
        this.setupModals();
        this.setupScrollEffects();
        this.setupButtonHandlers();
        this.setupThemeToggle();
        this.setupParticles();
        this.setupStatsCounter();
        this.setupAIInterface();
        this.setupSupportChat();

        this.setupSoundEffects();
        this.setupNotifications();
        this.setupFloatingMenu();
        this.setupSearch();

        this.setupCookieConsent();
        this.setupPerformanceMonitor();
        this.setupEasterEgg();
        this.setupAdvancedFeatures();

        // Initialiser les données utilisateur
        this.initUserData();
    }



    // Navigation fluide
    setupNavigation() {
        // Liens de navigation
        document.querySelectorAll('a[href^="#"]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header transparent au scroll
        const header = document.querySelector('.main-header');
        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;

            if (currentScrollY > 100) {
                header.style.background = 'rgba(15, 15, 35, 0.95)';
            } else {
                header.style.background = 'rgba(15, 15, 35, 0.8)';
            }

            lastScrollY = currentScrollY;
        });
    }

    // Cartes de fonctionnalités interactives
    setupFeatureCards() {
        const featureCards = document.querySelectorAll('.feature-card');

        featureCards.forEach(card => {
            const featureBtn = card.querySelector('.feature-btn');

            if (featureBtn) {
                featureBtn.addEventListener('click', () => {
                    const featureType = card.dataset.feature;
                    this.showFeatureModal(featureType);
                });
            }
        });
    }

    // Cartes de prix interactives
    setupPricingCards() {
        const priceButtons = document.querySelectorAll('.price-button');

        priceButtons.forEach(button => {
            button.addEventListener('click', () => {
                const plan = button.dataset.plan;
                this.handlePlanSelection(plan);
            });
        });
    }

    // Système de modals
    setupModals() {
        const modal = document.getElementById('feature-modal');
        const closeBtn = modal?.querySelector('.modal-close');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.closeModal();
            });
        }

        // Fermer modal en cliquant à l'extérieur
        modal?.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal();
            }
        });

        // Fermer modal avec Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }

    showFeatureModal(featureType) {
        const modal = document.getElementById('feature-modal');
        const title = document.getElementById('modal-title');
        const description = document.getElementById('modal-description');

        const features = {
            research: {
                title: 'Recherche & Analyse Intelligente',
                description: 'Nephiris peut rechercher et analyser des produits, comparer les prix, lire les avis clients sur différentes plateformes, et vous fournir un rapport structuré avec toutes les informations pertinentes.'
            },
            strategy: {
                title: 'Stratégie Marketing Avancée',
                description: 'Développement de stratégies go-to-market complètes incluant l\'analyse de marché, le dimensionnement, les canaux d\'acquisition client, la stratégie de prix et le calendrier de lancement.'
            },
            planning: {
                title: 'Planification de Voyage Personnalisée',
                description: 'Organisation complète de voyages avec recherche d\'hébergements, création d\'itinéraires personnalisés, réservations et recommandations d\'activités selon vos préférences.'
            },
            automation: {
                title: 'Automatisation Intelligente',
                description: 'Automatisation de vos tâches répétitives, optimisation de vos processus de travail, et création de workflows personnalisés pour améliorer votre productivité.'
            },
            analysis: {
                title: 'Analyse de Données Avancée',
                description: 'Traitement et analyse de vos données pour générer des insights actionnables, création de rapports détaillés et visualisations pour faciliter la prise de décision.'
            },
            communication: {
                title: 'Communication Professionnelle',
                description: 'Rédaction d\'emails personnalisés, gestion de vos communications professionnelles, et création de contenu adapté à votre audience et vos objectifs.'
            }
        };

        const feature = features[featureType];
        if (feature && modal && title && description) {
            title.textContent = feature.title;
            description.textContent = feature.description;
            modal.classList.add('show');
        }
    }

    closeModal() {
        const modal = document.getElementById('feature-modal');
        if (modal) {
            modal.classList.remove('show');
        }
    }

    handlePlanSelection(plan) {
        const plans = {
            free: 'Inscription gratuite',
            pro: 'Abonnement Pro',
            enterprise: 'Contact commercial'
        };

        const planName = plans[plan] || 'Plan sélectionné';

        this.playSound('click');
        this.showNotification(`${planName} - Fonctionnalité bientôt disponible !`, 'info');
    }

    showNotification(message) {
        // Créer une notification temporaire
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--gradient-primary);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 0.75rem;
            box-shadow: var(--shadow-lg);
            z-index: 3000;
            animation: slideInRight 0.3s ease;
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Effets de scroll
    setupScrollEffects() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observer les éléments à animer
        document.querySelectorAll('.feature-card, .price-card').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
    }

    // Gestionnaires de boutons
    setupButtonHandlers() {


        // Effets sonores sur hover
        document.querySelectorAll('button, a, .feature-card, .price-card').forEach(el => {
            el.addEventListener('mouseenter', () => this.playSound('hover'));
        });

        // Boutons du footer
        const footerLinks = [
            'changelog-link', 'docs-link', 'blog-link', 'support-link',
            'community-link', 'about-link', 'careers-link', 'contact-link',
            'press-link', 'privacy-link', 'terms-link', 'cookies-link', 'security-link'
        ];

        footerLinks.forEach(linkId => {
            document.getElementById(linkId)?.addEventListener('click', (e) => {
                e.preventDefault();
                this.showNotification('Page bientôt disponible !');
            });
        });

        // Boutons des modals
        document.getElementById('try-feature')?.addEventListener('click', () => {
            this.closeModal();
            this.showNotification('Fonctionnalité bientôt disponible !');
        });

        document.getElementById('learn-more')?.addEventListener('click', () => {
            this.closeModal();
            this.showNotification('Documentation bientôt disponible !');
        });
    }

    // Toggle thème sombre/clair
    setupThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = themeToggle?.querySelector('.theme-icon');

        // Charger le thème sauvegardé
        const savedTheme = localStorage.getItem('theme') || 'dark';
        document.documentElement.setAttribute('data-theme', savedTheme);

        if (themeIcon) {
            themeIcon.textContent = savedTheme === 'dark' ? '🌙' : '☀️';
        }

        themeToggle?.addEventListener('click', () => {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            if (themeIcon) {
                themeIcon.textContent = newTheme === 'dark' ? '🌙' : '☀️';
            }

            this.playSound('click');
            this.showNotification(`Thème ${newTheme === 'dark' ? 'sombre' : 'clair'} activé`, 'success');
        });
    }

    // Particules interactives
    setupParticles() {
        const particlesContainer = document.getElementById('particles');
        if (!particlesContainer) return;

        const createParticle = () => {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 8 + 's';
            particle.style.animationDuration = (6 + Math.random() * 4) + 's';

            // Couleurs aléatoires
            const colors = ['var(--primary)', 'var(--secondary)', 'var(--accent)'];
            particle.style.background = colors[Math.floor(Math.random() * colors.length)];

            particlesContainer.appendChild(particle);

            // Supprimer après animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 10000);
        };

        // Créer des particules régulièrement
        setInterval(createParticle, 2000);

        // Créer quelques particules initiales
        for (let i = 0; i < 5; i++) {
            setTimeout(createParticle, i * 500);
        }
    }

    // Compteurs animés pour les statistiques
    setupStatsCounter() {
        const statNumbers = document.querySelectorAll('.stat-number');

        const animateCounter = (element) => {
            const target = parseFloat(element.dataset.target);
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;

            const timer = setInterval(() => {
                current += step;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                // Formater le nombre
                if (target >= 1000) {
                    element.textContent = Math.floor(current).toLocaleString();
                } else {
                    element.textContent = current.toFixed(1);
                }
            }, 16);
        };

        // Observer pour déclencher l'animation au scroll
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        statNumbers.forEach(stat => observer.observe(stat));
    }

    // Interface IA principale avec vraie intelligence
    setupAIInterface() {
        this.currentUser = null;
        this.conversations = [];
        this.currentConversation = null;
        this.messageCount = 0;
        this.dailyLimit = 15;

        // Initialiser la vraie IA
        this.ai = new NephirisAI();
        this.markdownRenderer = new MarkdownRenderer();

        // Boutons pour ouvrir l'IA
        document.getElementById('google-login')?.addEventListener('click', () => {
            this.openAIInterface();
        });

        document.getElementById('try-ai-btn')?.addEventListener('click', () => {
            this.openAIInterface();
        });

        document.getElementById('start-ai-btn')?.addEventListener('click', () => {
            this.openAIInterface();
        });

        // Contrôles de l'interface IA
        document.getElementById('ai-close-btn')?.addEventListener('click', () => {
            this.closeAIInterface();
        });

        document.getElementById('ai-google-login')?.addEventListener('click', () => {
            this.authenticateWithGoogle();
        });

        // Gestion des messages IA
        this.setupAIMessaging();
        this.setupAIHistory();
    }

    openAIInterface() {
        const aiInterface = document.getElementById('ai-interface');
        aiInterface?.classList.add('show');
        this.playSound('open');

        if (!this.currentUser) {
            this.showAILogin();
        } else {
            this.showAIMain();
        }
    }

    closeAIInterface() {
        const aiInterface = document.getElementById('ai-interface');
        aiInterface?.classList.remove('show');
        this.playSound('close');
    }

    showAILogin() {
        document.getElementById('ai-login-section').style.display = 'flex';
        document.getElementById('ai-main-interface').style.display = 'none';
        document.getElementById('ai-status').textContent = 'Connectez-vous pour commencer';
    }

    showAIMain() {
        document.getElementById('ai-login-section').style.display = 'none';
        document.getElementById('ai-main-interface').style.display = 'flex';
        document.getElementById('ai-status').textContent = 'En ligne';
        this.updateUserInfo();
        this.loadConversations();
    }

    authenticateWithGoogle() {
        // Simulation de l'authentification Google
        this.playSound('click');
        this.showNotification('Connexion en cours...', 'info');

        setTimeout(() => {
            this.currentUser = {
                name: 'Utilisateur Demo',
                email: '<EMAIL>',
                avatar: 'U',
                plan: 'gratuit',
                joinDate: new Date()
            };

            this.showAIMain();
            this.showNotification('Connexion réussie !', 'success');
            this.playSound('notification');
        }, 2000);
    }

    updateUserInfo() {
        if (!this.currentUser) return;

        document.getElementById('user-name').textContent = this.currentUser.name;
        document.getElementById('user-avatar').textContent = this.currentUser.avatar;
        document.getElementById('user-plan').textContent =
            this.currentUser.plan === 'gratuit' ? 'Plan Gratuit' : 'Plan Premium';

        // Mettre à jour l'usage
        const remaining = this.dailyLimit - this.messageCount;
        document.getElementById('usage-count').textContent = this.messageCount;
        document.getElementById('ai-usage-info').style.display =
            this.currentUser.plan === 'gratuit' ? 'flex' : 'none';
    }

    setupAIMessaging() {
        const aiInput = document.getElementById('ai-input');
        const sendBtn = document.getElementById('ai-send-btn');
        const attachBtn = document.getElementById('ai-attach-btn');

        // Auto-resize textarea
        aiInput?.addEventListener('input', () => {
            aiInput.style.height = 'auto';
            aiInput.style.height = Math.min(aiInput.scrollHeight, 120) + 'px';
        });

        // Envoyer message
        const sendMessage = () => {
            const message = aiInput?.value.trim();
            if (!message || !this.currentUser) return;

            if (this.currentUser.plan === 'gratuit' && this.messageCount >= this.dailyLimit) {
                this.showNotification('Limite quotidienne atteinte. Passez au Premium !', 'warning');
                return;
            }

            this.addAIMessage(message, 'user');
            aiInput.value = '';
            aiInput.style.height = 'auto';
            this.messageCount++;
            this.updateUserInfo();

            // Simuler réponse IA
            this.simulateAIResponse(message);
        };

        sendBtn?.addEventListener('click', sendMessage);

        aiInput?.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Actions rapides
        document.querySelectorAll('.quick-action-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const prompt = btn.dataset.prompt;
                if (prompt && aiInput) {
                    aiInput.value = prompt;
                    aiInput.focus();
                }
            });
        });

        // Bouton d'attachement
        attachBtn?.addEventListener('click', () => {
            this.showNotification('Fonctionnalité bientôt disponible !', 'info');
        });
    }

    addAIMessage(content, type, useMarkdown = true) {
        const messagesContainer = document.getElementById('ai-messages');
        if (!messagesContainer) return;

        // Masquer le message de bienvenue
        const welcomeMessage = messagesContainer.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.style.display = 'none';
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `ai-message ${type}`;

        const avatar = document.createElement('div');
        avatar.className = 'ai-message-avatar';
        avatar.textContent = type === 'user' ? this.currentUser?.avatar || 'U' : '🔮';

        const messageContent = document.createElement('div');
        messageContent.className = 'ai-message-content';

        // Utiliser le rendu markdown pour les messages IA
        if (useMarkdown && type === 'assistant') {
            messageContent.innerHTML = this.markdownRenderer.render(content);
        } else {
            messageContent.textContent = content;
        }

        const messageTime = document.createElement('div');
        messageTime.className = 'ai-message-time';
        messageTime.textContent = new Date().toLocaleTimeString();

        messageDiv.appendChild(avatar);
        const contentWrapper = document.createElement('div');
        contentWrapper.appendChild(messageContent);
        contentWrapper.appendChild(messageTime);
        messageDiv.appendChild(contentWrapper);

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // Sauvegarder dans l'historique
        this.saveMessageToHistory(content, type);
    }

    async simulateAIResponse(userMessage) {
        // Afficher l'indicateur de typing
        this.showTypingIndicator();

        try {
            // Obtenir l'historique de la conversation
            const history = this.currentConversation?.messages || [];

            // Générer une vraie réponse IA
            const aiResponse = await this.ai.generateResponse(userMessage, history);

            // Masquer l'indicateur de typing
            this.hideTypingIndicator();

            // Afficher la réponse avec effet de streaming
            this.streamAIMessage(aiResponse.content, 'assistant');

            this.playSound('message');

        } catch (error) {
            console.error('Erreur IA:', error);
            this.hideTypingIndicator();
            this.addAIMessage("Désolé, je rencontre une difficulté technique. Pouvez-vous reformuler votre question ?", 'assistant');
        }
    }

    showTypingIndicator() {
        const messagesContainer = document.getElementById('ai-messages');
        if (!messagesContainer) return;

        const typingDiv = document.createElement('div');
        typingDiv.className = 'ai-message assistant typing-indicator';
        typingDiv.id = 'typing-indicator';

        typingDiv.innerHTML = `
            <div class="ai-message-avatar">🔮</div>
            <div class="ai-message-content">
                <div class="typing-indicator">
                    Nephiris réfléchit
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        `;

        messagesContainer.appendChild(typingDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    streamAIMessage(content, type) {
        const messagesContainer = document.getElementById('ai-messages');
        if (!messagesContainer) return;

        // Masquer le message de bienvenue
        const welcomeMessage = messagesContainer.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.style.display = 'none';
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `ai-message ${type}`;

        const avatar = document.createElement('div');
        avatar.className = 'ai-message-avatar';
        avatar.textContent = type === 'user' ? this.currentUser?.avatar || 'U' : '🔮';

        const messageContent = document.createElement('div');
        messageContent.className = 'ai-message-content';

        const messageTime = document.createElement('div');
        messageTime.className = 'ai-message-time';
        messageTime.textContent = new Date().toLocaleTimeString();

        messageDiv.appendChild(avatar);
        const contentWrapper = document.createElement('div');
        contentWrapper.appendChild(messageContent);
        contentWrapper.appendChild(messageTime);
        messageDiv.appendChild(contentWrapper);

        messagesContainer.appendChild(messageDiv);

        // Effet de streaming (typing)
        this.typeMessage(messageContent, content, () => {
            // Sauvegarder dans l'historique après le streaming
            this.saveMessageToHistory(content, type);
        });

        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    typeMessage(element, text, callback) {
        // Rendre le markdown
        const renderedText = this.markdownRenderer.render(text);

        element.innerHTML = '';
        element.classList.add('message-streaming');

        let i = 0;
        const speed = 8; // Vitesse de typing ultra-rapide (réduit de 20 à 8)

        const type = () => {
            if (i < renderedText.length) {
                // Ajouter plusieurs caractères à la fois pour plus de fluidité
                const chunkSize = Math.min(3, renderedText.length - i);
                element.innerHTML = renderedText.substring(0, i + chunkSize);
                i += chunkSize;

                // Scroll automatique pendant le typing
                const messagesContainer = document.getElementById('ai-messages');
                if (messagesContainer) {
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }

                setTimeout(type, speed);
            } else {
                // Fin du typing
                element.classList.remove('message-streaming');
                element.innerHTML = renderedText;
                if (callback) callback();
            }
        };

        type();
    }

    setupAIHistory() {
        document.getElementById('new-conversation-btn')?.addEventListener('click', () => {
            this.startNewConversation();
        });

        document.getElementById('upgrade-btn')?.addEventListener('click', () => {
            this.showUpgradeModal();
        });

        document.getElementById('upgrade-link')?.addEventListener('click', () => {
            this.showUpgradeModal();
        });

        // Nouveaux boutons d'action
        document.getElementById('ai-export-btn')?.addEventListener('click', () => {
            this.exportConversation();
        });

        document.getElementById('ai-clear-btn')?.addEventListener('click', () => {
            this.clearCurrentConversation();
        });

        document.getElementById('ai-settings-btn')?.addEventListener('click', () => {
            this.showAISettings();
        });
    }

    saveMessageToHistory(content, type) {
        if (!this.currentConversation) {
            this.startNewConversation();
        }

        this.currentConversation.messages.push({
            content,
            type,
            timestamp: new Date()
        });

        this.currentConversation.lastMessage = content.substring(0, 50) + '...';
        this.currentConversation.lastActivity = new Date();

        // Sauvegarder dans localStorage
        localStorage.setItem('nephiris_conversations', JSON.stringify(this.conversations));
    }

    startNewConversation() {
        const conversation = {
            id: Date.now(),
            title: 'Nouvelle conversation',
            messages: [],
            lastMessage: '',
            lastActivity: new Date(),
            createdAt: new Date()
        };

        this.conversations.unshift(conversation);
        this.currentConversation = conversation;

        // Nettoyer l'interface
        const messagesContainer = document.getElementById('ai-messages');
        if (messagesContainer) {
            messagesContainer.innerHTML = `
                <div class="welcome-message">
                    <div class="welcome-icon">🔮</div>
                    <h3>Nouvelle conversation</h3>
                    <p>Que puis-je faire pour vous aujourd'hui ?</p>
                </div>
            `;
        }

        this.loadConversations();
        this.playSound('click');
    }

    loadConversations() {
        // Charger depuis localStorage
        const saved = localStorage.getItem('nephiris_conversations');
        if (saved) {
            this.conversations = JSON.parse(saved);
        }

        const conversationsList = document.getElementById('conversations-list');
        if (!conversationsList) return;

        conversationsList.innerHTML = '';

        this.conversations.forEach(conv => {
            const convElement = document.createElement('div');
            convElement.className = 'conversation-item';
            if (conv.id === this.currentConversation?.id) {
                convElement.classList.add('active');
            }

            convElement.innerHTML = `
                <div class="conversation-title">${conv.title}</div>
                <div class="conversation-preview">${conv.lastMessage || 'Nouvelle conversation'}</div>
                <div class="conversation-time">${this.formatTime(conv.lastActivity)}</div>
            `;

            convElement.addEventListener('click', () => {
                this.loadConversation(conv);
            });

            conversationsList.appendChild(convElement);
        });
    }

    loadConversation(conversation) {
        this.currentConversation = conversation;

        const messagesContainer = document.getElementById('ai-messages');
        if (!messagesContainer) return;

        messagesContainer.innerHTML = '';

        if (conversation.messages.length === 0) {
            messagesContainer.innerHTML = `
                <div class="welcome-message">
                    <div class="welcome-icon">🔮</div>
                    <h3>Conversation vide</h3>
                    <p>Commencez à taper pour démarrer la conversation.</p>
                </div>
            `;
        } else {
            conversation.messages.forEach(msg => {
                this.addAIMessage(msg.content, msg.type);
            });
        }

        this.loadConversations(); // Refresh pour mettre à jour l'active
        this.playSound('click');
    }

    formatTime(date) {
        const now = new Date();
        const diff = now - new Date(date);
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return 'À l\'instant';
        if (minutes < 60) return `${minutes}m`;
        if (hours < 24) return `${hours}h`;
        return `${days}j`;
    }

    showUpgradeModal() {
        this.showCustomModal('Passer au Premium', `
            <div class="upgrade-modal-content">
                <div class="upgrade-features">
                    <h4>Fonctionnalités Premium</h4>
                    <div class="premium-feature">✓ Messages illimités</div>
                    <div class="premium-feature">✓ Modèles IA avancés</div>
                    <div class="premium-feature">✓ Historique illimité</div>
                    <div class="premium-feature">✓ Support prioritaire</div>
                    <div class="premium-feature">✓ Intégrations avancées</div>
                    <div class="premium-feature">✓ API access</div>
                </div>
                <div class="upgrade-pricing">
                    <div class="price">25 CAD<span>/mois</span></div>
                    <button class="btn-primary upgrade-now-btn">Passer au Premium</button>
                </div>
            </div>
        `);
    }

    // Support Chat (différent de l'IA)
    setupSupportChat() {
        const supportToggle = document.getElementById('support-toggle');
        const supportChat = document.getElementById('support-chat');
        const supportClose = document.getElementById('support-close');
        const supportInput = document.getElementById('support-input');
        const supportSend = document.getElementById('support-send');

        let isOpen = false;

        supportToggle?.addEventListener('click', () => {
            isOpen = !isOpen;
            if (isOpen) {
                supportChat?.classList.add('show');
                this.playSound('open');
            } else {
                supportChat?.classList.remove('show');
            }
        });

        supportClose?.addEventListener('click', () => {
            isOpen = false;
            supportChat?.classList.remove('show');
            this.playSound('close');
        });

        const sendSupportMessage = () => {
            const message = supportInput?.value.trim();
            if (!message) return;

            this.addSupportMessage(message, 'user');
            supportInput.value = '';

            // Simuler réponse du support
            setTimeout(() => {
                const responses = [
                    "Merci pour votre message ! Un membre de notre équipe vous répondra sous peu.",
                    "Je comprends votre question. Laissez-moi vous aider avec cela.",
                    "C'est une excellente question ! Voici comment procéder :",
                    "Merci de nous avoir contactés. Voici les informations que vous cherchez :",
                ];
                const response = responses[Math.floor(Math.random() * responses.length)];
                this.addSupportMessage(response, 'support');
                this.playSound('message');
            }, 1000 + Math.random() * 2000);
        };

        supportSend?.addEventListener('click', sendSupportMessage);
        supportInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendSupportMessage();
            }
        });
    }

    addSupportMessage(content, type) {
        const supportMessages = document.getElementById('support-messages');
        if (!supportMessages) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type === 'support' ? 'bot' : 'user'}-message`;

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = type === 'support' ? '💬' : '👤';

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.textContent = content;

        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        supportMessages.appendChild(messageDiv);

        supportMessages.scrollTop = supportMessages.scrollHeight;
    }

    initUserData() {
        // Charger les données utilisateur sauvegardées
        const savedUser = localStorage.getItem('nephiris_user');
        if (savedUser) {
            this.currentUser = JSON.parse(savedUser);
        }

        const savedCount = localStorage.getItem('nephiris_message_count');
        if (savedCount) {
            this.messageCount = parseInt(savedCount);
        }

        // Reset quotidien
        const lastReset = localStorage.getItem('nephiris_last_reset');
        const today = new Date().toDateString();
        if (lastReset !== today) {
            this.messageCount = 0;
            localStorage.setItem('nephiris_last_reset', today);
            localStorage.setItem('nephiris_message_count', '0');
        }
    }



    // Effets sonores
    setupSoundEffects() {
        this.sounds = {
            click: this.createSound(800, 0.1, 'sine'),
            hover: this.createSound(600, 0.05, 'sine'),
            open: this.createSound(440, 0.2, 'triangle'),
            close: this.createSound(330, 0.2, 'triangle'),
            message: this.createSound(660, 0.15, 'square'),
            notification: this.createSound(880, 0.2, 'sine')
        };

        this.soundEnabled = localStorage.getItem('soundEnabled') !== 'false';

        // Ajouter toggle son
        const soundToggle = document.createElement('button');
        soundToggle.className = 'sound-toggle';
        soundToggle.innerHTML = this.soundEnabled ? '🔊' : '🔇';
        soundToggle.title = 'Toggle sound effects';
        document.body.appendChild(soundToggle);

        soundToggle.addEventListener('click', () => {
            this.soundEnabled = !this.soundEnabled;
            localStorage.setItem('soundEnabled', this.soundEnabled);
            soundToggle.innerHTML = this.soundEnabled ? '🔊' : '🔇';
            soundToggle.classList.toggle('muted', !this.soundEnabled);
        });
    }

    createSound(frequency, duration, type = 'sine') {
        return () => {
            if (!this.soundEnabled) return;

            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.value = frequency;
            oscillator.type = type;

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
        };
    }

    playSound(soundName) {
        if (this.sounds && this.sounds[soundName]) {
            this.sounds[soundName]();
        }
    }

    // Système de notifications avancé
    setupNotifications() {
        this.notificationId = 0;
    }

    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notifications');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.id = `notification-${++this.notificationId}`;

        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌'
        };

        notification.innerHTML = `
            <div class="notification-header">
                <span class="notification-icon">${icons[type] || icons.info}</span>
                <span class="notification-title">${type.charAt(0).toUpperCase() + type.slice(1)}</span>
                <button class="notification-close">×</button>
            </div>
            <div class="notification-content">${message}</div>
            <div class="notification-progress"></div>
        `;

        container.appendChild(notification);

        // Fermer au clic
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.removeNotification(notification);
        });

        // Auto-remove après durée
        setTimeout(() => {
            this.removeNotification(notification);
        }, duration);

        this.playSound('notification');
    }

    removeNotification(notification) {
        if (notification && notification.parentNode) {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }
    }

    // Loading Screen avec progression
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const progressBar = document.getElementById('loading-progress');
        const percentage = document.getElementById('loading-percentage');

        if (!loadingScreen) return;

        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 100) progress = 100;

            progressBar.style.width = progress + '%';
            percentage.textContent = Math.floor(progress) + '%';

            if (progress >= 100) {
                clearInterval(interval);
                setTimeout(() => {
                    loadingScreen.classList.add('hidden');
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 500);
                }, 500);
            }
        }, 100);

        // Créer des particules de chargement
        this.createLoadingParticles();
    }

    createLoadingParticles() {
        const container = document.querySelector('.loading-particles');
        if (!container) return;

        for (let i = 0; i < 20; i++) {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: absolute;
                width: 4px;
                height: 4px;
                background: var(--primary);
                border-radius: 50%;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation: float ${4 + Math.random() * 4}s infinite linear;
                animation-delay: ${Math.random() * 2}s;
            `;
            container.appendChild(particle);
        }
    }

    // Menu flottant d'actions rapides
    setupFloatingMenu() {
        const floatingMenu = document.getElementById('floating-menu');
        const mainBtn = document.getElementById('main-floating-btn');

        let isOpen = false;

        mainBtn?.addEventListener('click', () => {
            isOpen = !isOpen;
            floatingMenu?.classList.toggle('open', isOpen);
            this.playSound(isOpen ? 'open' : 'close');
        });

        // Actions des boutons
        document.getElementById('quick-demo')?.addEventListener('click', () => {
            this.playSound('click');
            document.getElementById('demo')?.scrollIntoView({ behavior: 'smooth' });
            this.showNotification('Démo lancée !', 'success');
        });

        document.getElementById('quick-contact')?.addEventListener('click', () => {
            this.playSound('click');
            this.showContactModal();
        });

        document.getElementById('quick-help')?.addEventListener('click', () => {
            this.playSound('click');
            this.showHelpModal();
        });

        document.getElementById('quick-feedback')?.addEventListener('click', () => {
            this.playSound('click');
            this.showFeedbackModal();
        });
    }

    // Système de recherche avancé
    setupSearch() {
        const searchOverlay = document.getElementById('search-overlay');
        const searchInput = document.getElementById('search-input');
        const searchClose = document.getElementById('search-close');
        const searchResults = document.getElementById('search-results');

        // Ouvrir recherche avec Ctrl+K
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.openSearch();
            }
            if (e.key === 'Escape') {
                this.closeSearch();
            }
        });

        searchClose?.addEventListener('click', () => this.closeSearch());
        searchOverlay?.addEventListener('click', (e) => {
            if (e.target === searchOverlay) this.closeSearch();
        });

        // Recherche en temps réel
        searchInput?.addEventListener('input', (e) => {
            this.performSearch(e.target.value);
        });

        // Suggestions cliquables
        document.querySelectorAll('.search-item').forEach(item => {
            item.addEventListener('click', () => {
                const query = item.dataset.query;
                searchInput.value = query;
                this.performSearch(query);
            });
        });
    }

    openSearch() {
        const searchOverlay = document.getElementById('search-overlay');
        const searchInput = document.getElementById('search-input');

        searchOverlay?.classList.add('show');
        setTimeout(() => searchInput?.focus(), 100);
        this.playSound('open');
    }

    closeSearch() {
        const searchOverlay = document.getElementById('search-overlay');
        searchOverlay?.classList.remove('show');
        this.playSound('close');
    }

    performSearch(query) {
        if (!query.trim()) return;

        const results = this.searchContent(query);
        this.displaySearchResults(results);
    }

    searchContent(query) {
        const searchData = [
            { title: 'Automatisation des tâches', content: 'Automatisez vos workflows', type: 'feature', url: '#features' },
            { title: 'Analyse de données', content: 'Insights intelligents', type: 'feature', url: '#features' },
            { title: 'Assistant conversationnel', content: 'Chat IA avancé', type: 'feature', url: '#chatbot' },
            { title: 'Plans et tarifs', content: 'Découvrez nos offres', type: 'pricing', url: '#pricing' },
            { title: 'Témoignages clients', content: 'Avis utilisateurs', type: 'social', url: '#testimonials' },
            { title: 'Blog et actualités', content: 'Dernières nouvelles', type: 'content', url: '#blog' }
        ];

        return searchData.filter(item =>
            item.title.toLowerCase().includes(query.toLowerCase()) ||
            item.content.toLowerCase().includes(query.toLowerCase())
        );
    }

    displaySearchResults(results) {
        const container = document.getElementById('search-results');
        if (!container) return;

        if (results.length === 0) {
            container.innerHTML = '<div class="search-no-results">Aucun résultat trouvé</div>';
            return;
        }

        const resultsHTML = results.map(result => `
            <div class="search-result-item" data-url="${result.url}">
                <div class="search-result-title">${result.title}</div>
                <div class="search-result-content">${result.content}</div>
                <div class="search-result-type">${result.type}</div>
            </div>
        `).join('');

        container.innerHTML = resultsHTML;

        // Ajouter les événements de clic
        container.querySelectorAll('.search-result-item').forEach(item => {
            item.addEventListener('click', () => {
                const url = item.dataset.url;
                this.closeSearch();
                document.querySelector(url)?.scrollIntoView({ behavior: 'smooth' });
                this.playSound('click');
            });
        });
    }



    // Gestion des cookies
    setupCookieConsent() {
        const cookieConsent = document.getElementById('cookie-consent');
        const acceptBtn = document.getElementById('cookie-accept');
        const declineBtn = document.getElementById('cookie-decline');

        const cookieChoice = localStorage.getItem('cookieConsent');
        if (!cookieChoice) {
            setTimeout(() => {
                cookieConsent?.classList.add('show');
            }, 3000);
        }

        acceptBtn?.addEventListener('click', () => {
            localStorage.setItem('cookieConsent', 'accepted');
            cookieConsent?.classList.remove('show');
            this.playSound('click');
            this.showNotification('Cookies acceptés', 'success');
        });

        declineBtn?.addEventListener('click', () => {
            localStorage.setItem('cookieConsent', 'declined');
            cookieConsent?.classList.remove('show');
            this.playSound('click');
            this.showNotification('Cookies refusés', 'info');
        });
    }

    // Moniteur de performance
    setupPerformanceMonitor() {
        const monitor = document.getElementById('performance-monitor');
        const toggle = document.getElementById('perf-toggle');
        const fpsCounter = document.getElementById('fps-counter');
        const latencyCounter = document.getElementById('latency-counter');
        const memoryCounter = document.getElementById('memory-counter');

        let isVisible = false;
        let frameCount = 0;
        let lastTime = performance.now();

        toggle?.addEventListener('click', () => {
            isVisible = !isVisible;
            monitor.style.display = isVisible ? 'block' : 'none';
            this.playSound('click');
        });

        const updateFPS = () => {
            frameCount++;
            const currentTime = performance.now();

            if (currentTime - lastTime >= 1000) {
                if (fpsCounter) {
                    fpsCounter.textContent = frameCount;
                }
                frameCount = 0;
                lastTime = currentTime;
            }

            requestAnimationFrame(updateFPS);
        };

        setInterval(() => {
            if (latencyCounter) {
                latencyCounter.textContent = (8 + Math.random() * 10).toFixed(0) + 'ms';
            }
            if (memoryCounter) {
                memoryCounter.textContent = (40 + Math.random() * 20).toFixed(0) + 'MB';
            }
        }, 2000);

        updateFPS();
    }

    // Easter Egg
    setupEasterEgg() {
        let sequence = [];
        const targetSequence = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight'];

        document.addEventListener('keydown', (e) => {
            sequence.push(e.code);

            if (sequence.length > targetSequence.length) {
                sequence.shift();
            }

            if (JSON.stringify(sequence) === JSON.stringify(targetSequence)) {
                this.showEasterEgg();
                sequence = [];
            }
        });

        const logo = document.querySelector('.logo');
        let clickCount = 0;

        logo?.addEventListener('click', () => {
            clickCount++;
            setTimeout(() => { clickCount = 0; }, 1000);

            if (clickCount === 3) {
                this.showEasterEgg();
                clickCount = 0;
            }
        });
    }

    showEasterEgg() {
        const easterEgg = document.getElementById('easter-egg');
        const closeBtn = document.getElementById('easter-close');

        easterEgg?.classList.add('show');
        this.playSound('notification');

        closeBtn?.addEventListener('click', () => {
            easterEgg?.classList.remove('show');
            this.playSound('close');
        });
    }

    // Fonctionnalités avancées
    setupAdvancedFeatures() {
        this.setupKeyboardShortcuts();
        this.setupGestures();
        this.setupAnalytics();
        this.setupAccessibility();
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                document.getElementById('theme-toggle')?.click();
            }

            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                document.getElementById('chatbot-toggle')?.click();
            }

            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'S') {
                e.preventDefault();
                document.querySelector('.sound-toggle')?.click();
            }
        });
    }

    setupGestures() {
        let touchStartX = 0;
        let touchStartY = 0;

        document.addEventListener('touchstart', (e) => {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', (e) => {
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;

            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;

            if (deltaX > 100 && Math.abs(deltaY) < 50) {
                document.getElementById('main-floating-btn')?.click();
            }

            if (deltaY < -100 && Math.abs(deltaX) < 50) {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        });
    }

    setupAnalytics() {
        this.analytics = {
            pageViews: 0,
            interactions: 0,
            timeSpent: 0
        };

        document.addEventListener('click', () => {
            this.analytics.interactions++;
        });

        const startTime = Date.now();
        setInterval(() => {
            this.analytics.timeSpent = Math.floor((Date.now() - startTime) / 1000);
        }, 1000);
    }

    setupAccessibility() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });

        this.announceToScreenReader = (message) => {
            const announcement = document.createElement('div');
            announcement.setAttribute('aria-live', 'polite');
            announcement.setAttribute('aria-atomic', 'true');
            announcement.style.position = 'absolute';
            announcement.style.left = '-10000px';
            announcement.textContent = message;

            document.body.appendChild(announcement);
            setTimeout(() => {
                document.body.removeChild(announcement);
            }, 1000);
        };
    }

    showContactModal() {
        this.showCustomModal('Contact', `
            <form class="contact-form">
                <input type="text" placeholder="Votre nom" required>
                <input type="email" placeholder="Votre email" required>
                <textarea placeholder="Votre message" rows="4" required></textarea>
                <button type="submit" class="btn-primary">Envoyer</button>
            </form>
        `);
    }

    showCustomModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'modal custom-modal show';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="modal-close">&times;</span>
                <div class="modal-body">
                    <h3>${title}</h3>
                    ${content}
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        const closeBtn = modal.querySelector('.modal-close');
        closeBtn.addEventListener('click', () => {
            modal.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(modal);
            }, 300);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeBtn.click();
            }
        });
    }

    // Nouvelles fonctionnalités avancées
    exportConversation() {
        if (!this.currentConversation || !this.currentConversation.messages.length) {
            this.showNotification('Aucune conversation à exporter', 'warning');
            return;
        }

        const conversation = this.currentConversation;
        const exportData = {
            title: conversation.title,
            date: new Date().toLocaleDateString(),
            messages: conversation.messages.map(msg => ({
                type: msg.type === 'user' ? 'Utilisateur' : 'Nephiris AI',
                content: msg.content,
                time: new Date(msg.timestamp).toLocaleTimeString()
            }))
        };

        // Créer le contenu markdown
        let markdown = `# ${exportData.title}\n\n`;
        markdown += `**Date d'export :** ${exportData.date}\n\n`;
        markdown += `---\n\n`;

        exportData.messages.forEach(msg => {
            markdown += `## ${msg.type} (${msg.time})\n\n`;
            markdown += `${msg.content}\n\n`;
            markdown += `---\n\n`;
        });

        // Télécharger le fichier
        const blob = new Blob([markdown], { type: 'text/markdown' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `nephiris-conversation-${Date.now()}.md`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showNotification('Conversation exportée !', 'success');
        this.playSound('notification');
    }

    clearCurrentConversation() {
        if (!this.currentConversation) return;

        if (confirm('Êtes-vous sûr de vouloir effacer cette conversation ? Cette action est irréversible.')) {
            // Vider les messages
            this.currentConversation.messages = [];
            this.currentConversation.lastMessage = '';

            // Nettoyer l'interface
            const messagesContainer = document.getElementById('ai-messages');
            if (messagesContainer) {
                messagesContainer.innerHTML = `
                    <div class="welcome-message">
                        <div class="welcome-icon">🔮</div>
                        <h3>Conversation effacée</h3>
                        <p>Vous pouvez commencer une nouvelle conversation.</p>
                    </div>
                `;
            }

            // Sauvegarder
            localStorage.setItem('nephiris_conversations', JSON.stringify(this.conversations));
            this.loadConversations();

            this.showNotification('Conversation effacée', 'info');
            this.playSound('click');
        }
    }

    showAISettings() {
        this.showCustomModal('Paramètres IA', `
            <div class="ai-settings-content">
                <div class="settings-section">
                    <h4>🎯 Comportement de l'IA</h4>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="setting-creative" checked>
                            Mode créatif (réponses plus imaginatives)
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="setting-detailed" checked>
                            Réponses détaillées
                        </label>
                    </div>
                </div>

                <div class="settings-section">
                    <h4>💾 Données</h4>
                    <div class="setting-item">
                        <button class="btn-ghost" onclick="window.nephirisApp.exportAllConversations()">
                            📤 Exporter toutes les conversations
                        </button>
                    </div>
                    <div class="setting-item">
                        <button class="btn-ghost" onclick="window.nephirisApp.clearAllData()">
                            🗑️ Effacer toutes les données
                        </button>
                    </div>
                </div>

                <div class="settings-actions">
                    <button class="btn-primary" onclick="window.nephirisApp.saveAISettings()">
                        Sauvegarder
                    </button>
                </div>
            </div>
        `);
    }

    exportAllConversations() {
        if (!this.conversations.length) {
            this.showNotification('Aucune conversation à exporter', 'warning');
            return;
        }

        const exportData = {
            export_date: new Date().toISOString(),
            user: this.currentUser?.name || 'Utilisateur',
            conversations: this.conversations
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `nephiris-all-conversations-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showNotification('Toutes les conversations exportées !', 'success');
    }

    clearAllData() {
        if (confirm('⚠️ ATTENTION : Cette action effacera TOUTES vos données. Êtes-vous sûr ?')) {
            localStorage.clear();
            this.conversations = [];
            this.currentConversation = null;
            this.messageCount = 0;
            this.closeAIInterface();
            this.showNotification('Toutes les données ont été effacées', 'info');
            setTimeout(() => window.location.reload(), 2000);
        }
    }

    saveAISettings() {
        const settings = {
            creative: document.getElementById('setting-creative')?.checked || true,
            detailed: document.getElementById('setting-detailed')?.checked || true
        };
        localStorage.setItem('nephiris_ai_settings', JSON.stringify(settings));
        this.showNotification('Paramètres sauvegardés !', 'success');
        document.querySelector('.custom-modal')?.remove();
    }
}

// Ajouter les animations CSS manquantes
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100%);
        }
    }
`;
document.head.appendChild(style);

// Enregistrer le service worker pour PWA
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
                console.log('Service Worker enregistré avec succès:', registration.scope);

                // Vérifier les mises à jour
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            // Nouvelle version disponible
                            if (confirm('Une nouvelle version de Nephiris AI est disponible. Recharger ?')) {
                                window.location.reload();
                            }
                        }
                    });
                });
            })
            .catch((error) => {
                console.log('Échec de l\'enregistrement du Service Worker:', error);
            });
    });
}

// Initialiser l'application
document.addEventListener('DOMContentLoaded', () => {
    window.nephirisApp = new NephirisApp();

    // Afficher notification d'installation PWA
    let deferredPrompt;
    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;

        // Afficher bouton d'installation après 5 secondes
        setTimeout(() => {
            if (deferredPrompt) {
                const app = document.querySelector('.nephiris-app');
                if (app) {
                    app.showNotification('Installer Nephiris AI sur votre appareil pour une meilleure expérience !', 'info', 10000);
                }
            }
        }, 5000);
    });

    // Gestion de l'installation
    window.addEventListener('appinstalled', () => {
        console.log('PWA installée avec succès');
        deferredPrompt = null;
    });
});
