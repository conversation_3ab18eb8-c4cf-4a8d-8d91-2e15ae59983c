"""
Module : multi_agent_coordinator.py

Ce module gère la coordination et la collaboration entre plusieurs agents spécialisés,
permettant le partage de plan de tâches, la communication inter-agent et la supervision.

Fonctionnalités principales :
- Gestion d'un pool d'agents : création, enregistrement et retrait.
- Attribution automatique de tâches à l'agent le plus pertinent (scoring, matching).
- Communication inter-agent : messages, échange de données, notifications de statut.
- Suivi d'un plan partagé (objectifs, dépendances, jalons).
- Superviseur intelligent qui surveille la progression, détecte les blocages et réalloue les ressources.
- Possibilité de définir des rôles d'agents (ex. agent “créatif” vs. agent “logique”).
- Planification long terme : permet de stocker et réactualiser la liste de tâches futures.

Exemple d’utilisation :
1. Créer plusieurs agents spécialisés (AgentCréatif, AgentLogique, etc.) 
2. Les enregistrer via la classe MultiAgentCoordinator
3. Définir un plan de tâches sous forme de liste d’objectifs ou d’actions
4. Lancer la coordination pour répartir et enchaîner les tâches entre les agents
5. Observer la progression, récupérer les résultats intermédiaires et notifier en cas d’anomalie.

"""

from typing import Dict, List, Any
import uuid
import random
import time

class MultiAgentCoordinator:
    def __init__(self):
        """
        Initialise un coordinateur multi-agents.
         - self.agents : Dictionnaire d'agents enregistrés 
           (clé = agent_id, valeur = instance d'agent).
         - self.plan : Liste d'objectifs globaux ou de tâches à réaliser.
         - self.supervisor_logs : Historique des évènements enregistrés par le superviseur.
        """
        self.agents: Dict[str, Any] = {}
        self.plan: List[Dict[str, Any]] = []
        self.supervisor_logs: List[str] = []

    def register_agent(self, agent_instance: Any, agent_role: str) -> str:
        """
        Enregistre un nouvel agent avec un rôle spécifique, 
        renvoie l'ID de l'agent attribué.
        """
        agent_id = str(uuid.uuid4())
        self.agents[agent_id] = {
            "instance": agent_instance,
            "role": agent_role,
            "status": "idle"
        }
        self._log_supervisor_event(f"Agent enregistré : {agent_id}, rôle = {agent_role}")
        return agent_id

    def remove_agent(self, agent_id: str):
        """
        Supprime un agent du pool.
        """
        if agent_id in self.agents:
            del self.agents[agent_id]
            self._log_supervisor_event(f"Agent retiré : {agent_id}")
        else:
            self._log_supervisor_event(f"Impossible de retirer l’agent, ID introuvable : {agent_id}")

    def define_plan(self, global_plan: List[Dict[str, Any]]):
        """
        Définit un plan global de tâches/étapes.
        Format d'un élément de plan suggéré :
        {
            "task_id": "uuid",
            "description": "Expliquer le concept X",
            "dependencies": [...],
            "assigned_to": None,
            "completed": False
        }
        """
        self.plan = global_plan
        self._log_supervisor_event("Plan global défini.")

    def run_coordination(self):
        """
        Démarre la coordination multi-agents :
        - Identifie les tâches non assignées dont les dépendances sont satisfaites
        - Attribue les tâches aux agents disponibles selon leur rôle ou le matching
        - Surveille la progression et réalloue si besoin 
        - Continue jusqu'à ce que le plan soit terminé ou que plus aucune tâche ne puisse avancer
        """
        self._log_supervisor_event("Démarrage de la coordination multi-agents...")
        while True:
            next_tasks = self._get_ready_tasks()
            if not next_tasks:
                # Plus de tâches à traiter ou blocage
                self._log_supervisor_event("Aucune tâche disponible ou blocage détecté.")
                break

            for task in next_tasks:
                agent_id = self._assign_agent(task)
                if agent_id:
                    self._execute_task(agent_id, task)
                else:
                    self._log_supervisor_event("Aucun agent disponible pour la tâche : " + task["task_id"])

            if all([t["completed"] for t in self.plan]):
                self._log_supervisor_event("Toutes les tâches du plan sont terminées.")
                break

    def _get_ready_tasks(self) -> List[Dict[str, Any]]:
        """
        Renvoie la liste des tâches qui ne sont pas terminées,
        sans dépendances non résolues, et non encore assignées ou en cours.
        """
        ready_tasks = []
        for task in self.plan:
            if not task["completed"]:
                # Vérifier dépendances
                deps_done = all([dep["completed"] for dep in task.get("dependencies", [])])
                if deps_done and not task.get("assigned_to"):
                    ready_tasks.append(task)
        return ready_tasks

    def _assign_agent(self, task: Dict[str, Any]) -> str:
        """
        Sélectionne un agent parmi ceux disponibles, éventuellement en fonction
        du rôle requis. Stratégie simpliste : prend le premier agent libre.
        """
        # Logique plus avancée : si la tâche mentionne un rôle souhaité, 
        # on effectue un matching. Par ex : 
        # if task.get("required_role"):
        #    ...
        free_agents = [aid for aid, info in self.agents.items() if info["status"] == "idle"]
        if not free_agents:
            return ""

        chosen_agent = random.choice(free_agents)
        self.agents[chosen_agent]["status"] = "busy"
        task["assigned_to"] = chosen_agent
        self._log_supervisor_event(f"Tâche {task['task_id']} assignée à l’agent {chosen_agent}")
        return chosen_agent

    def _execute_task(self, agent_id: str, task: Dict[str, Any]):
        """
        Simule l’exécution de la tâche par l’agent.
        Dans une implémentation réelle, on appellerait agent_instance.handle_task(task)
        ou similaire.
        """
        self._log_supervisor_event(f"L’agent {agent_id} commence la tâche {task['task_id']}...")
        time.sleep(0.5)  # simulation d’un temps de traitement
        # Pour l’exemple, on suppose que la tâche se termine toujours
        task["completed"] = True
        self._log_supervisor_event(f"L’agent {agent_id} a terminé la tâche {task['task_id']}")
        # Libérer l’agent
        self.agents[agent_id]["status"] = "idle"

    def _log_supervisor_event(self, message: str):
        """
        Ajoute un message au journal du superviseur.
        """
        self.supervisor_logs.append(message)

    def get_supervisor_logs(self) -> List[str]:
        """
        Retourne l'historique complet des événements recensés par le superviseur.
        """
        return self.supervisor_logs

    def get_state_snapshot(self) -> Dict[str, Any]:
        """
        Retourne l'état global du coordinateur, y compris le plan, 
        l'état des agents et le journal de supervision.
        """
        return {
            "agents": self.agents,
            "plan": self.plan,
            "logs": self.supervisor_logs
        }
