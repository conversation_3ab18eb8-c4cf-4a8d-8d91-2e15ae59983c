import React, { useState, useRef, useEffect, createContext, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FiSend, 
  FiPlus, 
  FiSettings, 
  FiSun, 
  FiMoon, 
  FiUser, 
  FiMessageSquare,
  FiZap,
  FiCopy,
  FiCheck,
  FiTrash2,
  FiEdit3,
  FiMoreVertical,
  FiDownload,
  FiShare2,
  FiMic,
  FiMicOff,
  FiVolume2,
  FiVolumeX,
  FiMaximize2,
  FiMinimize2,
  FiRefreshCw,
  FiSearch,
  FiFilter,
  FiBookmark,
  FiHeart,
  FiStar,
  FiTrendingUp
} from 'react-icons/fi';
import { v4 as uuidv4 } from 'uuid';
import toast from 'react-hot-toast';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';

// Context pour gérer l'état global de l'application
const AppContext = createContext();

// Hook personnalisé pour utiliser le contexte
const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within AppProvider');
  }
  return context;
};

// Styles inline améliorés avec plus de variantes
const styles = {
  app: {
    display: 'flex',
    height: '100vh',
    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
    backgroundColor: 'var(--bg-primary)',
    color: 'var(--text-primary)',
    overflow: 'hidden',
  },
  sidebar: {
    width: 280,
    background: 'var(--bg-secondary)',
    borderRight: '1px solid var(--border)',
    display: 'flex',
    flexDirection: 'column',
    transition: 'all 0.3s ease',
    position: 'relative',
  },
  sidebarCollapsed: {
    width: 60,
  },
  logo: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    padding: '20px',
    borderBottom: '1px solid var(--border)',
    fontWeight: '700',
    fontSize: '18px',
    background: 'var(--gradient-primary)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
  },
  logoIcon: {
    fontSize: '24px',
    filter: 'drop-shadow(0 0 10px rgba(99, 102, 241, 0.5))',
  },
  newChatButton: {
    margin: '16px',
    padding: '12px 16px',
    borderRadius: '12px',
    border: '1px solid var(--border)',
    backgroundColor: 'transparent',
    color: 'var(--text-primary)',
    cursor: 'pointer',
    fontWeight: '600',
    fontSize: '14px',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    transition: 'all 0.2s ease',
    position: 'relative',
    overflow: 'hidden',
  },
  newChatButtonHover: {
    backgroundColor: 'var(--primary)',
    borderColor: 'var(--primary)',
    transform: 'translateY(-1px)',
    boxShadow: '0 4px 12px rgba(99, 102, 241, 0.3)',
  },
  conversationsList: {
    flex: 1,
    overflowY: 'auto',
    padding: '8px 16px',
  },
  conversationItem: {
    padding: '12px 16px',
    margin: '4px 0',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    fontSize: '14px',
    color: 'var(--text-secondary)',
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    group: true,
  },
  conversationItemActive: {
    backgroundColor: 'var(--primary-light)',
    color: 'var(--primary)',
    borderLeft: '3px solid var(--primary)',
  },
  conversationItemHover: {
    backgroundColor: 'var(--bg-tertiary)',
    color: 'var(--text-primary)',
  },
  sidebarFooter: {
    padding: '16px',
    borderTop: '1px solid var(--border)',
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
  },
  userInfo: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    padding: '12px',
    borderRadius: '8px',
    backgroundColor: 'var(--bg-tertiary)',
  },
  userAvatar: {
    width: '32px',
    height: '32px',
    borderRadius: '50%',
    background: 'var(--gradient-primary)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'white',
    fontWeight: '600',
    fontSize: '14px',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: '14px',
    fontWeight: '600',
    color: 'var(--text-primary)',
  },
  userPlan: {
    fontSize: '12px',
    color: 'var(--text-muted)',
  },
  themeToggle: {
    background: 'none',
    border: '1px solid var(--border)',
    borderRadius: '8px',
    color: 'var(--text-secondary)',
    cursor: 'pointer',
    padding: '8px',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  mainContent: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
  },
  chatHeader: {
    height: '60px',
    borderBottom: '1px solid var(--border)',
    display: 'flex',
    alignItems: 'center',
    padding: '0 24px',
    justifyContent: 'space-between',
    backgroundColor: 'var(--bg-primary)',
    backdropFilter: 'blur(10px)',
    position: 'sticky',
    top: 0,
    zIndex: 10,
  },
  chatTitle: {
    fontSize: '18px',
    fontWeight: '600',
    color: 'var(--text-primary)',
  },
  modelBadge: {
    background: 'var(--primary-light)',
    color: 'var(--primary)',
    padding: '4px 12px',
    borderRadius: '20px',
    fontSize: '12px',
    fontWeight: '600',
    border: '1px solid var(--primary)',
  },
  messagesArea: {
    flex: 1,
    overflowY: 'auto',
    padding: '24px 0',
    position: 'relative',
  },
  welcomeScreen: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    textAlign: 'center',
    padding: '0 24px',
  },
  welcomeLogo: {
    width: '80px',
    height: '80px',
    background: 'var(--gradient-primary)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '32px',
    marginBottom: '24px',
    animation: 'pulse 2s infinite',
    boxShadow: '0 0 30px rgba(99, 102, 241, 0.3)',
  },
  welcomeTitle: {
    fontSize: '32px',
    fontWeight: '700',
    marginBottom: '12px',
    background: 'var(--gradient-primary)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
  },
  welcomeSubtitle: {
    fontSize: '18px',
    color: 'var(--text-secondary)',
    marginBottom: '32px',
    maxWidth: '600px',
  },
  suggestions: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
    gap: '16px',
    maxWidth: '800px',
    width: '100%',
  },
  suggestionCard: {
    background: 'var(--bg-card)',
    border: '1px solid var(--border)',
    borderRadius: '12px',
    padding: '20px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    textAlign: 'left',
    position: 'relative',
    overflow: 'hidden',
  },
  suggestionCardHover: {
    borderColor: 'var(--primary)',
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 25px rgba(99, 102, 241, 0.15)',
  },
  suggestionIcon: {
    fontSize: '24px',
    marginBottom: '12px',
  },
  suggestionTitle: {
    fontSize: '16px',
    fontWeight: '600',
    marginBottom: '8px',
    color: 'var(--text-primary)',
  },
  suggestionDesc: {
    fontSize: '14px',
    color: 'var(--text-secondary)',
  },
  messages: {
    maxWidth: '800px',
    margin: '0 auto',
    padding: '0 24px',
  },
  message: {
    marginBottom: '32px',
    display: 'flex',
    gap: '16px',
    animation: 'fadeInUp 0.3s ease-out',
  },
  messageAvatar: {
    width: '32px',
    height: '32px',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '14px',
    fontWeight: '600',
    flexShrink: 0,
  },
  messageContent: {
    flex: 1,
    lineHeight: 1.6,
  },
  messageHeader: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    marginBottom: '8px',
  },
  messageAuthor: {
    fontWeight: '600',
    fontSize: '14px',
  },
  messageTime: {
    fontSize: '12px',
    color: 'var(--text-muted)',
  },
  messageText: {
    color: 'var(--text-primary)',
    fontSize: '15px',
    lineHeight: 1.6,
  },
  messageActions: {
    display: 'flex',
    gap: '8px',
    marginTop: '8px',
    opacity: 0,
    transition: 'opacity 0.2s ease',
  },
  messageActionsVisible: {
    opacity: 1,
  },
  actionButton: {
    background: 'none',
    border: '1px solid var(--border)',
    borderRadius: '6px',
    color: 'var(--text-muted)',
    cursor: 'pointer',
    padding: '4px 8px',
    fontSize: '12px',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
  },
  actionButtonHover: {
    backgroundColor: 'var(--bg-tertiary)',
    color: 'var(--text-primary)',
  },
  typingIndicator: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    color: 'var(--text-secondary)',
    fontStyle: 'italic',
    marginTop: '8px',
  },
  typingDots: {
    display: 'flex',
    gap: '4px',
  },
  typingDot: {
    width: '6px',
    height: '6px',
    background: 'var(--text-secondary)',
    borderRadius: '50%',
    animation: 'typing 1.4s infinite ease-in-out',
  },
  inputArea: {
    padding: '24px',
    borderTop: '1px solid var(--border)',
    backgroundColor: 'var(--bg-primary)',
    backdropFilter: 'blur(10px)',
  },
  inputContainer: {
    maxWidth: '800px',
    margin: '0 auto',
  },
  inputWrapper: {
    background: 'var(--bg-tertiary)',
    border: '1px solid var(--border)',
    borderRadius: '24px',
    padding: '12px 20px',
    display: 'flex',
    alignItems: 'flex-end',
    gap: '12px',
    transition: 'all 0.2s ease',
    position: 'relative',
  },
  inputWrapperFocused: {
    borderColor: 'var(--primary)',
    boxShadow: '0 0 0 3px var(--primary-light)',
  },
  messageInput: {
    flex: 1,
    background: 'none',
    border: 'none',
    color: 'var(--text-primary)',
    fontSize: '16px',
    lineHeight: 1.5,
    resize: 'none',
    outline: 'none',
    minHeight: '24px',
    maxHeight: '200px',
    fontFamily: 'inherit',
  },
  messageInputPlaceholder: {
    color: 'var(--text-muted)',
  },
  sendButton: {
    background: 'var(--primary)',
    border: 'none',
    borderRadius: '50%',
    width: '32px',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    color: 'white',
  },
  sendButtonHover: {
    background: 'var(--primary-hover)',
    transform: 'scale(1.05)',
  },
  sendButtonDisabled: {
    opacity: 0.5,
    cursor: 'not-allowed',
    transform: 'none',
  },
  inputFooter: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: '12px',
    fontSize: '12px',
    color: 'var(--text-muted)',
  },
  usageInfo: {
    color: 'var(--text-secondary)',
  },
  upgradeLink: {
    color: 'var(--primary)',
    textDecoration: 'none',
    fontWeight: '500',
    cursor: 'pointer',
  },
  upgradeLinkHover: {
    textDecoration: 'underline',
  },
  searchBar: {
    margin: '8px 16px',
    padding: '8px 12px',
    background: 'var(--bg-tertiary)',
    border: '1px solid var(--border)',
    borderRadius: '8px',
    color: 'var(--text-primary)',
    fontSize: '14px',
    outline: 'none',
    transition: 'all 0.2s ease',
  },
  searchBarFocused: {
    borderColor: 'var(--primary)',
    boxShadow: '0 0 0 2px var(--primary-light)',
  },
  voiceButton: {
    background: 'var(--bg-tertiary)',
    border: '1px solid var(--border)',
    borderRadius: '50%',
    width: '32px',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    color: 'var(--text-secondary)',
  },
  voiceButtonActive: {
    background: 'var(--error)',
    borderColor: 'var(--error)',
    color: 'white',
    animation: 'pulse 1s infinite',
  },
  fullscreenButton: {
    background: 'none',
    border: '1px solid var(--border)',
    borderRadius: '6px',
    color: 'var(--text-secondary)',
    cursor: 'pointer',
    padding: '6px',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  statsCard: {
    background: 'var(--bg-card)',
    border: '1px solid var(--border)',
    borderRadius: '8px',
    padding: '12px',
    margin: '4px 0',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  },
  statsIcon: {
    color: 'var(--primary)',
    fontSize: '16px',
  },
  statsText: {
    fontSize: '12px',
    color: 'var(--text-secondary)',
  },
  statsValue: {
    fontSize: '14px',
    fontWeight: '600',
    color: 'var(--text-primary)',
  },
  messageRating: {
    display: 'flex',
    gap: '4px',
    marginTop: '8px',
  },
  ratingButton: {
    background: 'none',
    border: 'none',
    color: 'var(--text-muted)',
    cursor: 'pointer',
    padding: '2px',
    borderRadius: '4px',
    transition: 'all 0.2s ease',
  },
  ratingButtonActive: {
    color: 'var(--warning)',
  },
  exportButton: {
    background: 'var(--bg-tertiary)',
    border: '1px solid var(--border)',
    borderRadius: '6px',
    color: 'var(--text-secondary)',
    cursor: 'pointer',
    padding: '6px 12px',
    fontSize: '12px',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
  },
};

// Animations CSS en JS
const keyframes = `
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
`;

// Composant pour l'indicateur de frappe
const TypingIndicator = () => (
  <div style={styles.message}>
    <div style={{
      ...styles.messageAvatar,
      background: 'var(--bg-secondary)',
      border: '1px solid var(--border)',
    }}>
      🔮
    </div>
    <div style={styles.messageContent}>
      <div style={styles.messageHeader}>
        <div style={styles.messageAuthor}>Nephiris AI</div>
      </div>
      <div style={styles.typingIndicator}>
        <span>est en train d'écrire</span>
        <div style={styles.typingDots}>
          <div style={{...styles.typingDot, animationDelay: '-0.32s'}} />
          <div style={{...styles.typingDot, animationDelay: '-0.16s'}} />
          <div style={{...styles.typingDot, animationDelay: '0s'}} />
        </div>
      </div>
    </div>
  </div>
);

// Hook pour la reconnaissance vocale
const useSpeechRecognition = () => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    setIsSupported(!!SpeechRecognition);
  }, []);

  const startListening = () => {
    if (!isSupported) {
      toast.error('Reconnaissance vocale non supportée dans ce navigateur');
      return;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();

    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'fr-FR';

    recognition.onstart = () => {
      setIsListening(true);
      toast.success('Écoute en cours... Parlez maintenant');
    };

    recognition.onresult = (event) => {
      const current = event.resultIndex;
      const transcript = event.results[current][0].transcript;
      setTranscript(transcript);
    };

    recognition.onerror = (event) => {
      console.error('Erreur reconnaissance vocale:', event.error);
      setIsListening(false);
      toast.error('Erreur de reconnaissance vocale');
    };

    recognition.onend = () => {
      setIsListening(false);
    };

    recognition.start();

    return recognition;
  };

  const stopListening = (recognition) => {
    if (recognition) {
      recognition.stop();
    }
    setIsListening(false);
  };

  return { isListening, transcript, isSupported, startListening, stopListening, setTranscript };
};

// Composant pour les actions de message avec rating
const MessageActions = ({ message, onCopy, onEdit, onDelete, onRate }) => {
  const [copied, setCopied] = useState(false);
  const [rating, setRating] = useState(message.rating || 0);

  const handleCopy = () => {
    onCopy(message.text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleRate = (stars) => {
    setRating(stars);
    onRate(message.id, stars);
    toast.success(`Message noté ${stars} étoile${stars > 1 ? 's' : ''}`);
  };

  const handleSpeak = () => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(message.text);
      utterance.lang = 'fr-FR';
      utterance.rate = 0.9;
      speechSynthesis.speak(utterance);
      toast.success('Lecture audio démarrée');
    } else {
      toast.error('Synthèse vocale non supportée');
    }
  };

  return (
    <div style={styles.messageActions} className="message-actions">
      <button
        style={styles.actionButton}
        onClick={handleCopy}
        title="Copier"
      >
        {copied ? <FiCheck size={12} /> : <FiCopy size={12} />}
        {copied ? 'Copié' : 'Copier'}
      </button>

      {message.from === 'ai' && (
        <button
          style={styles.actionButton}
          onClick={handleSpeak}
          title="Écouter"
        >
          <FiVolume2 size={12} />
          Écouter
        </button>
      )}

      <button
        style={styles.actionButton}
        onClick={() => onEdit(message)}
        title="Modifier"
      >
        <FiEdit3 size={12} />
        Modifier
      </button>

      <button
        style={styles.actionButton}
        onClick={() => onDelete(message.id)}
        title="Supprimer"
      >
        <FiTrash2 size={12} />
        Supprimer
      </button>

      {message.from === 'ai' && (
        <div style={styles.messageRating}>
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              style={{
                ...styles.ratingButton,
                ...(star <= rating ? styles.ratingButtonActive : {})
              }}
              onClick={() => handleRate(star)}
              title={`Noter ${star} étoile${star > 1 ? 's' : ''}`}
            >
              <FiStar size={12} fill={star <= rating ? 'currentColor' : 'none'} />
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

// Composant de statistiques pour la sidebar
const StatsPanel = ({ conversations, messageCount, maxMessages }) => {
  const totalMessages = conversations.reduce((acc, conv) => acc + conv.messages.length, 0);
  const avgMessagesPerConv = conversations.length > 0 ? Math.round(totalMessages / conversations.length) : 0;
  const aiMessages = conversations.reduce((acc, conv) =>
    acc + conv.messages.filter(msg => msg.from === 'ai').length, 0
  );

  return (
    <div style={{ padding: '8px 16px', borderTop: '1px solid var(--border)' }}>
      <div style={{ fontSize: '12px', fontWeight: '600', marginBottom: '8px', color: 'var(--text-secondary)' }}>
        Statistiques
      </div>

      <div style={styles.statsCard}>
        <FiMessageSquare style={styles.statsIcon} />
        <div>
          <div style={styles.statsValue}>{totalMessages}</div>
          <div style={styles.statsText}>Messages total</div>
        </div>
      </div>

      <div style={styles.statsCard}>
        <FiTrendingUp style={styles.statsIcon} />
        <div>
          <div style={styles.statsValue}>{avgMessagesPerConv}</div>
          <div style={styles.statsText}>Moy. par conversation</div>
        </div>
      </div>

      <div style={styles.statsCard}>
        <FiZap style={styles.statsIcon} />
        <div>
          <div style={styles.statsValue}>{aiMessages}</div>
          <div style={styles.statsText}>Réponses IA</div>
        </div>
      </div>

      <div style={styles.statsCard}>
        <FiUser style={styles.statsIcon} />
        <div>
          <div style={styles.statsValue}>{Math.max(0, maxMessages - messageCount)}</div>
          <div style={styles.statsText}>Messages restants</div>
        </div>
      </div>
    </div>
  );
};

// Composant Message amélioré avec support Markdown
const Message = ({ message, onCopy, onEdit, onDelete }) => {
  const [showActions, setShowActions] = useState(false);
  const isUser = message.from === 'user';
  const isAI = message.from === 'ai';

  const handleCopy = (text) => {
    navigator.clipboard.writeText(text);
    toast.success('Message copié !');
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      style={styles.message}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div style={{
        ...styles.messageAvatar,
        background: isUser ? 'var(--primary)' : 'var(--bg-secondary)',
        color: isUser ? 'white' : 'var(--text-primary)',
        border: isAI ? '1px solid var(--border)' : 'none',
      }}>
        {isUser ? 'U' : '🔮'}
      </div>
      <div style={styles.messageContent}>
        <div style={styles.messageHeader}>
          <div style={styles.messageAuthor}>
            {isUser ? 'Vous' : 'Nephiris AI'}
          </div>
          <div style={styles.messageTime}>
            {formatTime(message.timestamp)}
          </div>
        </div>
        <div style={styles.messageText}>
          {isAI ? (
            <ReactMarkdown
              components={{
                code({ node, inline, className, children, ...props }) {
                  const match = /language-(\w+)/.exec(className || '');
                  return !inline && match ? (
                    <SyntaxHighlighter
                      style={oneDark}
                      language={match[1]}
                      PreTag="div"
                      {...props}
                    >
                      {String(children).replace(/\n$/, '')}
                    </SyntaxHighlighter>
                  ) : (
                    <code className={className} {...props}>
                      {children}
                    </code>
                  );
                }
              }}
            >
              {message.text}
            </ReactMarkdown>
          ) : (
            message.text
          )}
        </div>
        {showActions && (
          <MessageActions
            message={message}
            onCopy={handleCopy}
            onEdit={onEdit}
            onDelete={onDelete}
          />
        )}
      </div>
    </motion.div>
  );
};

// Composant Sidebar amélioré
const Sidebar = ({
  conversations,
  currentConversationId,
  onNewConversation,
  onSelectConversation,
  onDeleteConversation,
  onExportConversation,
  theme,
  onToggleTheme,
  messageCount,
  maxMessages
}) => {
  const [collapsed, setCollapsed] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchFocused, setSearchFocused] = useState(false);

  const filteredConversations = conversations.filter(conv =>
    conv.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    conv.messages.some(msg => msg.text.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleExportAll = () => {
    const data = {
      conversations,
      exportDate: new Date().toISOString(),
      version: '2.1.0'
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `nephiris-conversations-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success('Conversations exportées !');
  };

  return (
    <div style={{
      ...styles.sidebar,
      ...(collapsed ? styles.sidebarCollapsed : {})
    }}>
      <div style={styles.logo}>
        <span style={styles.logoIcon}>🔮</span>
        {!collapsed && <span>Nephiris AI</span>}
      </div>

      {!collapsed && (
        <>
          <button
            style={styles.newChatButton}
            onClick={onNewConversation}
            onMouseEnter={(e) => Object.assign(e.target.style, styles.newChatButtonHover)}
            onMouseLeave={(e) => Object.assign(e.target.style, styles.newChatButton)}
          >
            <FiPlus size={16} />
            Nouvelle conversation
          </button>

          <input
            type="text"
            placeholder="Rechercher..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onFocus={() => setSearchFocused(true)}
            onBlur={() => setSearchFocused(false)}
            style={{
              ...styles.searchBar,
              ...(searchFocused ? styles.searchBarFocused : {})
            }}
          />

          <div style={{ display: 'flex', gap: '8px', margin: '8px 16px' }}>
            <button
              style={styles.exportButton}
              onClick={handleExportAll}
              title="Exporter toutes les conversations"
            >
              <FiDownload size={12} />
              Exporter
            </button>
            <button
              style={styles.exportButton}
              onClick={() => toast.info('Fonctionnalité bientôt disponible')}
              title="Filtrer les conversations"
            >
              <FiFilter size={12} />
              Filtrer
            </button>
          </div>
        </>
      )}

      {!collapsed && (
        <div style={styles.conversationsList}>
          {filteredConversations.length === 0 && searchTerm ? (
            <div style={{
              padding: '20px',
              textAlign: 'center',
              color: 'var(--text-muted)',
              fontSize: '14px'
            }}>
              Aucune conversation trouvée
            </div>
          ) : (
            filteredConversations.map((conversation) => (
            <div
              key={conversation.id}
              style={{
                ...styles.conversationItem,
                ...(conversation.id === currentConversationId ? styles.conversationItemActive : {})
              }}
              onClick={() => onSelectConversation(conversation.id)}
              onMouseEnter={(e) => {
                if (conversation.id !== currentConversationId) {
                  Object.assign(e.target.style, styles.conversationItemHover);
                }
              }}
              onMouseLeave={(e) => {
                if (conversation.id !== currentConversationId) {
                  Object.assign(e.target.style, styles.conversationItem);
                }
              }}
            >
              <div style={{ flex: 1, overflow: 'hidden' }}>
                <div style={{
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}>
                  {conversation.title || 'Nouvelle conversation'}
                </div>
                <div style={{
                  fontSize: '12px',
                  color: 'var(--text-muted)',
                  marginTop: '2px'
                }}>
                  {conversation.messages.length} messages
                </div>
              </div>
              <button
                style={{
                  background: 'none',
                  border: 'none',
                  color: 'var(--text-muted)',
                  cursor: 'pointer',
                  padding: '4px',
                  borderRadius: '4px',
                  opacity: 0,
                  transition: 'opacity 0.2s ease',
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteConversation(conversation.id);
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = 'var(--bg-tertiary)';
                  e.target.style.color = 'var(--error)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = 'transparent';
                  e.target.style.color = 'var(--text-muted)';
                }}
                className="delete-btn"
              >
                <FiTrash2 size={14} />
              </button>
            </div>
            ))
          )}
        </div>
      )}

      {!collapsed && <StatsPanel
        conversations={conversations}
        messageCount={messageCount}
        maxMessages={maxMessages}
      />}

      <div style={styles.sidebarFooter}>
        {!collapsed && (
          <div style={styles.userInfo}>
            <div style={styles.userAvatar}>U</div>
            <div style={styles.userDetails}>
              <div style={styles.userName}>Utilisateur</div>
              <div style={styles.userPlan}>
                Plan Gratuit ({Math.max(0, maxMessages - messageCount)}/{maxMessages})
              </div>
            </div>
          </div>
        )}

        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            style={styles.themeToggle}
            onClick={onToggleTheme}
            title={theme === 'dark' ? 'Mode clair' : 'Mode sombre'}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = 'var(--bg-tertiary)';
              e.target.style.color = 'var(--text-primary)';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = 'transparent';
              e.target.style.color = 'var(--text-secondary)';
            }}
          >
            {theme === 'dark' ? <FiSun size={16} /> : <FiMoon size={16} />}
          </button>

          <button
            style={styles.themeToggle}
            onClick={() => setCollapsed(!collapsed)}
            title={collapsed ? 'Étendre' : 'Réduire'}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = 'var(--bg-tertiary)';
              e.target.style.color = 'var(--text-primary)';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = 'transparent';
              e.target.style.color = 'var(--text-secondary)';
            }}
          >
            <FiSettings size={16} />
          </button>
        </div>
      </div>
    </div>
  );
};

// Composant WelcomeScreen
const WelcomeScreen = ({ onSendSuggestion }) => {
  const suggestions = [
    {
      icon: '🧠',
      title: 'Expliquer l\'IA',
      description: 'Comprendre l\'intelligence artificielle',
      prompt: 'Explique-moi le concept d\'intelligence artificielle de manière simple et accessible'
    },
    {
      icon: '✉️',
      title: 'Rédiger un email',
      description: 'Communication professionnelle',
      prompt: 'Aide-moi à rédiger un email professionnel pour demander une réunion'
    },
    {
      icon: '📚',
      title: 'Plan d\'apprentissage',
      description: 'Organiser mes études',
      prompt: 'Crée-moi un plan d\'étude détaillé pour apprendre Python en 4 semaines'
    },
    {
      icon: '💡',
      title: 'Brainstorming',
      description: 'Idées créatives et innovation',
      prompt: 'Génère des idées créatives pour améliorer la productivité au travail'
    }
  ];

  return (
    <div style={styles.welcomeScreen}>
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5 }}
        style={styles.welcomeLogo}
      >
        🔮
      </motion.div>

      <motion.h1
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        style={styles.welcomeTitle}
      >
        Bonjour ! Je suis Nephiris AI
      </motion.h1>

      <motion.p
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        style={styles.welcomeSubtitle}
      >
        Votre assistant IA intelligent et créatif. Comment puis-je vous aider aujourd'hui ?
      </motion.p>

      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        style={styles.suggestions}
      >
        {suggestions.map((suggestion, index) => (
          <motion.div
            key={index}
            style={styles.suggestionCard}
            onClick={() => onSendSuggestion(suggestion.prompt)}
            whileHover={{
              scale: 1.02,
              boxShadow: '0 8px 25px rgba(99, 102, 241, 0.15)'
            }}
            whileTap={{ scale: 0.98 }}
            onMouseEnter={(e) => Object.assign(e.target.style, styles.suggestionCardHover)}
            onMouseLeave={(e) => Object.assign(e.target.style, styles.suggestionCard)}
          >
            <div style={styles.suggestionIcon}>{suggestion.icon}</div>
            <div style={styles.suggestionTitle}>{suggestion.title}</div>
            <div style={styles.suggestionDesc}>{suggestion.description}</div>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

// Composant ChatInput avec reconnaissance vocale
const ChatInput = ({ onSendMessage, isTyping, disabled }) => {
  const [input, setInput] = useState('');
  const [focused, setFocused] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const textareaRef = useRef(null);
  const recognitionRef = useRef(null);

  const {
    isListening,
    transcript,
    isSupported,
    startListening,
    stopListening,
    setTranscript
  } = useSpeechRecognition();

  // Mettre à jour l'input avec la transcription vocale
  useEffect(() => {
    if (transcript) {
      setInput(transcript);
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
        textareaRef.current.style.height = Math.min(textareaRef.current.scrollHeight, 200) + 'px';
      }
    }
  }, [transcript]);

  const handleSend = () => {
    if (!input.trim() || isTyping || disabled) return;
    onSendMessage(input.trim());
    setInput('');
    setTranscript('');
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
    if (e.key === 'Escape' && isFullscreen) {
      setIsFullscreen(false);
    }
  };

  const handleInput = (e) => {
    setInput(e.target.value);

    // Auto-resize textarea
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
  };

  const handleVoiceToggle = () => {
    if (isListening) {
      stopListening(recognitionRef.current);
    } else {
      recognitionRef.current = startListening();
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    if (!isFullscreen) {
      setTimeout(() => textareaRef.current?.focus(), 100);
    }
  };

  const canSend = input.trim() && !isTyping && !disabled;

  return (
    <div style={{
      ...styles.inputArea,
      ...(isFullscreen ? {
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1000,
        background: 'var(--bg-primary)',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        padding: '40px',
      } : {})
    }}>
      <div style={styles.inputContainer}>
        {isFullscreen && (
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '20px'
          }}>
            <h2 style={{ color: 'var(--text-primary)', margin: 0 }}>Mode Focus</h2>
            <button
              style={styles.fullscreenButton}
              onClick={toggleFullscreen}
              title="Quitter le mode focus"
            >
              <FiMinimize2 size={16} />
            </button>
          </div>
        )}

        <div style={{
          ...styles.inputWrapper,
          ...(focused ? styles.inputWrapperFocused : {}),
          ...(isFullscreen ? { minHeight: '120px', padding: '20px' } : {})
        }}>
          <textarea
            ref={textareaRef}
            style={{
              ...styles.messageInput,
              ...(isFullscreen ? { fontSize: '18px', minHeight: '80px' } : {})
            }}
            placeholder={isListening ? "Parlez maintenant..." : "Envoyez un message à Nephiris AI..."}
            value={input}
            onChange={handleInput}
            onKeyDown={handleKeyDown}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            rows={isFullscreen ? 4 : 1}
            disabled={disabled}
          />

          <div style={{ display: 'flex', gap: '8px', alignItems: 'flex-end' }}>
            {!isFullscreen && (
              <button
                style={styles.fullscreenButton}
                onClick={toggleFullscreen}
                title="Mode focus"
              >
                <FiMaximize2 size={16} />
              </button>
            )}

            {isSupported && (
              <button
                style={{
                  ...styles.voiceButton,
                  ...(isListening ? styles.voiceButtonActive : {})
                }}
                onClick={handleVoiceToggle}
                title={isListening ? "Arrêter l'écoute" : "Reconnaissance vocale"}
              >
                {isListening ? <FiMicOff size={16} /> : <FiMic size={16} />}
              </button>
            )}

            <button
              style={{
                ...styles.sendButton,
                ...(canSend ? {} : styles.sendButtonDisabled)
              }}
              onClick={handleSend}
              disabled={!canSend}
              onMouseEnter={(e) => {
                if (canSend) {
                  Object.assign(e.target.style, styles.sendButtonHover);
                }
              }}
              onMouseLeave={(e) => {
                if (canSend) {
                  Object.assign(e.target.style, styles.sendButton);
                }
              }}
            >
              <FiSend size={16} />
            </button>
          </div>
        </div>

        {!isFullscreen && (
          <div style={styles.inputFooter}>
            <div style={styles.usageInfo}>
              {isListening ? (
                <span style={{ color: 'var(--error)' }}>
                  🎤 Écoute en cours... Parlez maintenant
                </span>
              ) : (
                "Nephiris AI peut faire des erreurs. Vérifiez les informations importantes."
              )}
            </div>
            <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
              <span style={{ fontSize: '11px', color: 'var(--text-muted)' }}>
                Ctrl+Enter pour envoyer
              </span>
              <a
                href="#"
                style={styles.upgradeLink}
                onClick={(e) => {
                  e.preventDefault();
                  toast.info('Fonctionnalité Premium bientôt disponible !');
                }}
                onMouseEnter={(e) => Object.assign(e.target.style, styles.upgradeLinkHover)}
                onMouseLeave={(e) => Object.assign(e.target.style, styles.upgradeLink)}
              >
                Passer au Premium
              </a>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Provider de contexte pour gérer l'état global
const AppProvider = ({ children }) => {
  const [conversations, setConversations] = useState([]);
  const [currentConversationId, setCurrentConversationId] = useState(null);
  const [isTyping, setIsTyping] = useState(false);
  const [theme, setTheme] = useState('dark');
  const [messageCount, setMessageCount] = useState(0);
  const maxMessages = 15;

  // Charger les données depuis localStorage
  useEffect(() => {
    const savedConversations = localStorage.getItem('nephiris_conversations');
    const savedTheme = localStorage.getItem('nephiris_theme');
    const savedMessageCount = localStorage.getItem('nephiris_message_count');

    if (savedConversations) {
      try {
        const parsed = JSON.parse(savedConversations);
        setConversations(parsed);
        if (parsed.length > 0) {
          setCurrentConversationId(parsed[0].id);
        }
      } catch (error) {
        console.error('Erreur lors du chargement des conversations:', error);
      }
    }

    if (savedTheme) {
      setTheme(savedTheme);
      document.documentElement.setAttribute('data-theme', savedTheme);
    }

    if (savedMessageCount) {
      setMessageCount(parseInt(savedMessageCount, 10));
    }
  }, []);

  // Sauvegarder les conversations
  useEffect(() => {
    if (conversations.length > 0) {
      localStorage.setItem('nephiris_conversations', JSON.stringify(conversations));
    }
  }, [conversations]);

  // Sauvegarder le thème
  useEffect(() => {
    localStorage.setItem('nephiris_theme', theme);
    document.documentElement.setAttribute('data-theme', theme);
  }, [theme]);

  // Sauvegarder le compteur de messages
  useEffect(() => {
    localStorage.setItem('nephiris_message_count', messageCount.toString());
  }, [messageCount]);

  const createNewConversation = () => {
    const newConversation = {
      id: uuidv4(),
      title: '',
      messages: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    setConversations(prev => [newConversation, ...prev]);
    setCurrentConversationId(newConversation.id);
    return newConversation.id;
  };

  const selectConversation = (id) => {
    setCurrentConversationId(id);
  };

  const deleteConversation = (id) => {
    setConversations(prev => {
      const filtered = prev.filter(conv => conv.id !== id);
      if (id === currentConversationId) {
        setCurrentConversationId(filtered.length > 0 ? filtered[0].id : null);
      }
      return filtered;
    });
    toast.success('Conversation supprimée');
  };

  const updateConversationTitle = (conversationId, title) => {
    setConversations(prev => prev.map(conv =>
      conv.id === conversationId
        ? { ...conv, title, updatedAt: new Date().toISOString() }
        : conv
    ));
  };

  const addMessage = (conversationId, message) => {
    setConversations(prev => prev.map(conv =>
      conv.id === conversationId
        ? {
            ...conv,
            messages: [...conv.messages, message],
            updatedAt: new Date().toISOString()
          }
        : conv
    ));
  };

  const sendMessage = async (text) => {
    if (messageCount >= maxMessages) {
      toast.error('Limite de messages atteinte ! Passez au Premium pour continuer.');
      return;
    }

    let conversationId = currentConversationId;

    // Créer une nouvelle conversation si nécessaire
    if (!conversationId) {
      conversationId = createNewConversation();
    }

    // Ajouter le message utilisateur
    const userMessage = {
      id: uuidv4(),
      from: 'user',
      text,
      timestamp: new Date().toISOString(),
    };

    addMessage(conversationId, userMessage);
    setMessageCount(prev => prev + 1);

    // Mettre à jour le titre de la conversation si c'est le premier message
    const conversation = conversations.find(conv => conv.id === conversationId);
    if (conversation && !conversation.title) {
      const title = text.length > 50 ? text.substring(0, 50) + '...' : text;
      updateConversationTitle(conversationId, title);
    }

    // Simuler la réponse de l'IA
    setIsTyping(true);

    try {
      // Simuler un délai d'API
      await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 2000));

      const aiResponses = [
        "Je comprends votre question. Voici une réponse détaillée qui prend en compte tous les aspects de votre demande. L'intelligence artificielle est effectivement un domaine fascinant qui continue d'évoluer rapidement.",
        "Excellente question ! Laissez-moi vous expliquer cela de manière claire et structurée. Il y a plusieurs points importants à considérer dans cette situation.",
        "Voici ce que je peux vous dire à ce sujet. Il y a plusieurs approches possibles, et je vais vous présenter les plus efficaces selon votre contexte.",
        "Je vais vous aider avec cela. Voici une approche étape par étape pour résoudre votre problème de manière efficace et durable.",
        "C'est un sujet très intéressant ! Permettez-moi de vous donner une explication complète avec des exemples concrets pour mieux illustrer le concept.",
        `Pour répondre à votre question sur "${text.substring(0, 30)}...", je peux vous proposer plusieurs perspectives intéressantes.`,
      ];

      const aiMessage = {
        id: uuidv4(),
        from: 'ai',
        text: aiResponses[Math.floor(Math.random() * aiResponses.length)],
        timestamp: new Date().toISOString(),
      };

      addMessage(conversationId, aiMessage);
      toast.success('Réponse générée !');
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error);
      toast.error('Erreur lors de la génération de la réponse');
    } finally {
      setIsTyping(false);
    }
  };

  const toggleTheme = () => {
    setTheme(prev => prev === 'dark' ? 'light' : 'dark');
  };

  const getCurrentConversation = () => {
    return conversations.find(conv => conv.id === currentConversationId);
  };

  const value = {
    conversations,
    currentConversationId,
    currentConversation: getCurrentConversation(),
    isTyping,
    theme,
    messageCount,
    maxMessages,
    createNewConversation,
    selectConversation,
    deleteConversation,
    sendMessage,
    toggleTheme,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

// Composant principal NephirisChat
const NephirisChat = () => {
  const {
    conversations,
    currentConversationId,
    currentConversation,
    isTyping,
    theme,
    messageCount,
    maxMessages,
    createNewConversation,
    selectConversation,
    deleteConversation,
    sendMessage,
    toggleTheme,
  } = useApp();

  const messagesEndRef = useRef(null);

  // Auto-scroll vers le bas quand de nouveaux messages arrivent
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [currentConversation?.messages, isTyping]);

  const handleSendMessage = (text) => {
    sendMessage(text);
  };

  const handleSendSuggestion = (prompt) => {
    sendMessage(prompt);
  };

  const handleCopyMessage = (text) => {
    navigator.clipboard.writeText(text);
    toast.success('Message copié !');
  };

  const handleEditMessage = (message) => {
    // TODO: Implémenter l'édition de message
    toast.info('Fonctionnalité d\'édition bientôt disponible !');
  };

  const handleDeleteMessage = (messageId) => {
    // TODO: Implémenter la suppression de message
    toast.info('Fonctionnalité de suppression bientôt disponible !');
  };

  const messages = currentConversation?.messages || [];
  const hasMessages = messages.length > 0;

  return (
    <>
      <style>{keyframes}</style>
      <div style={styles.app}>
        <Sidebar
          conversations={conversations}
          currentConversationId={currentConversationId}
          onNewConversation={createNewConversation}
          onSelectConversation={selectConversation}
          onDeleteConversation={deleteConversation}
          theme={theme}
          onToggleTheme={toggleTheme}
          messageCount={messageCount}
          maxMessages={maxMessages}
        />

        <div style={styles.mainContent}>
          <div style={styles.chatHeader}>
            <div style={styles.chatTitle}>Nephiris AI</div>
            <div style={styles.modelBadge}>GPT-4 Turbo</div>
          </div>

          <div style={styles.messagesArea}>
            {!hasMessages ? (
              <WelcomeScreen onSendSuggestion={handleSendSuggestion} />
            ) : (
              <div style={styles.messages}>
                <AnimatePresence>
                  {messages.map((message) => (
                    <Message
                      key={message.id}
                      message={message}
                      onCopy={handleCopyMessage}
                      onEdit={handleEditMessage}
                      onDelete={handleDeleteMessage}
                    />
                  ))}
                </AnimatePresence>

                {isTyping && <TypingIndicator />}
                <div ref={messagesEndRef} />
              </div>
            )}
          </div>

          <ChatInput
            onSendMessage={handleSendMessage}
            isTyping={isTyping}
            disabled={messageCount >= maxMessages}
          />
        </div>
      </div>
    </>
  );
};

// Composant App principal avec Provider
const App = () => {
  return (
    <AppProvider>
      <NephirisChat />
    </AppProvider>
  );
};

export default App;
